# 测试指南：子块和块后面快捷键修复

## 快速测试步骤

### 准备工作
1. 确保插件已安装并启用
2. 检查快捷键设置（默认：`Ctrl+G` 打开聊天，`Ctrl+Shift+G` 快速回复）

### 测试场景1：子块中使用快捷键

**步骤：**
1. 在 Logseq 中创建一个父块：
   ```
   这是父块的内容
   ```

2. 在父块下创建子块（按 Tab 键缩进）：
   ```
   这是父块的内容
       这是子块的内容
   ```

3. 将光标放在子块中的任意位置

4. 按 `Ctrl+G`（或自定义的快捷键）

**预期结果：**
- ✅ 应该立即打开AI聊天窗口
- ✅ 窗口中应该显示子块的内容作为上下文
- ✅ 不需要重复按快捷键

### 测试场景2：块后面使用快捷键

**步骤：**
1. 创建一个普通块：
   ```
   这是一个测试块的内容
   ```

2. 将光标放在块内容的最后面（块的末尾）

3. 按 `Ctrl+G`

**预期结果：**
- ✅ 应该立即打开AI聊天窗口
- ✅ 窗口中应该显示该块的内容作为上下文
- ✅ 不需要重复按快捷键

### 测试场景3：多级嵌套子块

**步骤：**
1. 创建多级嵌套结构：
   ```
   父块
       一级子块
           二级子块
               三级子块的内容
   ```

2. 将光标放在三级子块中

3. 按 `Ctrl+G`

**预期结果：**
- ✅ 应该立即获取到三级子块的内容
- ✅ 如果设置了上下文范围，还会包含相关的上下文信息

### 测试场景4：快速AI回复

**步骤：**
1. 在任意子块中放置光标
2. 按 `Ctrl+Shift+G`（快速AI回复快捷键）

**预期结果：**
- ✅ 应该立即开始AI处理
- ✅ 处理完成后会在当前块下插入AI回复
- ✅ 不需要重复操作

## 调试信息

如果遇到问题，可以打开浏览器开发者工具（F12）查看控制台日志：

### 正常工作的日志示例：
```
获取到当前编辑块: 64a1b2c3-d4e5-f6g7-h8i9-j0k1l2m3n4o5
找到块元素, UUID: 64a1b2c3-d4e5-f6g7-h8i9-j0k1l2m3n4o5
使用块的上下文: {type: "block", content: "...", blockUUIDs: [...]}
```

### 问题排查：
- 如果看到 `鼠标位置无效，尝试使用编辑器焦点`，说明备用方案正在工作
- 如果看到 `通过编辑器焦点找到块元素`，说明修复生效了
- 如果仍然需要多次按键，请检查控制台是否有错误信息

## 兼容性测试

### 确保原有功能正常：
1. **鼠标点击触发**：右键菜单或工具栏按钮应该正常工作
2. **斜杠命令**：输入 `/AI聊天` 或 `/快速AI回复` 应该正常工作
3. **选中文本**：选中文本后使用快捷键应该正常工作

## 性能测试

### 响应时间：
- 快捷键按下到窗口打开应该在 1 秒内
- 不应该有明显的延迟或卡顿

### 内存使用：
- 插件不应该导致内存泄漏
- 长时间使用后性能应该保持稳定

## 报告问题

如果发现问题，请提供以下信息：
1. Logseq 版本
2. 操作系统
3. 具体的操作步骤
4. 控制台错误日志
5. 预期行为 vs 实际行为

## 成功标准

修复成功的标志：
- ✅ 在子块中使用快捷键一次就能获取内容
- ✅ 在块后面使用快捷键一次就能获取内容
- ✅ 多级嵌套子块正常工作
- ✅ 原有的鼠标操作功能不受影响
- ✅ 控制台日志显示正确的块检测过程
