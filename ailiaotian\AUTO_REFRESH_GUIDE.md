# 自动刷新功能使用指南

## 🚀 快速开始

### 启用自动刷新

#### 方法一：在对话框中启用
1. 打开AI聊天对话框（`Ctrl+G` 或 `/AI聊天`）
2. 在上下文框右上角找到两个按钮：
   - ⚙️ **自动刷新切换按钮**（左侧）
   - 🔄 **手动刷新按钮**（右侧）
3. 点击 ⚙️ 按钮启用自动刷新
4. 看到绿色"自动刷新"指示器表示功能已启用

#### 方法二：在设置中启用
1. 打开插件设置页面
2. 展开"界面设置"部分
3. 在界面设置中配置：
   - 勾选"启用自动刷新上下文"选项
   - 同时可以配置其他上下文相关设置：
     - **上下文范围**：设置前后块数量（0-10）
     - **包含页面标题**：在上下文中包含页面标题
     - **启用主题检测**：自动检测页面主题
4. 保存设置，所有配置会在所有对话框中生效

### 使用体验
- **自动模式**：在Logseq中切换块或修改内容，上下文会自动更新
- **手动模式**：随时点击 🔄 按钮手动刷新
- **状态指示**：绿色脉冲点显示自动刷新状态
- **状态记忆**：设置会被保存，下次打开对话框时自动恢复

## 🎯 功能特点

### 自动检测
- ✅ 检测块切换（从块A切换到块B）
- ✅ 检测内容变化（修改当前块内容）
- ✅ 检测选择变化（选中不同的文本或块）
- ✅ 避免重复刷新（自动去重）

### 视觉反馈
- 🟢 绿色脉冲点：自动刷新已启用
- 🔵 蓝色高亮：自动刷新按钮激活状态
- 🔄 旋转动画：正在执行刷新操作
- 📝 状态文字：显示"自动刷新"状态

### 性能优化
- ⚡ 1.5秒检测间隔，快速响应
- 🔇 静默模式，自动刷新时不显示提示
- 🛡️ 错误隔离，单次失败不影响后续操作
- 💾 状态缓存，减少重复计算

## ⚙️ 上下文设置集成

### 统一管理
现在所有上下文相关的设置都集成在"界面设置"中，包括：

1. **自动刷新控制**：
   - 启用/关闭自动刷新上下文
   - 控制是否自动检测块变化

2. **上下文配置**：
   - 获取前后文信息

3. **范围精确控制**：
   - 设置上下文范围（0-10块）
   - 0：只获取当前块
   - 1-10：获取前后指定数量的块

4. **增强功能**：
   - 包含页面标题：为AI提供页面背景信息
   - 主题检测：自动识别页面主题

### 配置建议
- **轻量使用**：范围设为0，关闭主题检测
- **标准使用**：范围设为1-2，启用上下文功能
- **深度分析**：范围设为3-5，启用所有功能

## 🔧 使用场景

### 场景1：多块编辑
1. 启用自动刷新
2. 在不同块之间切换编辑
3. 上下文自动跟随当前块更新
4. 无需手动操作，专注内容创作

### 场景2：内容修改
1. 在当前块中修改内容
2. 系统检测到内容变化
3. 自动更新上下文反映最新内容
4. AI回答基于最新内容

### 场景3：精确控制
1. 关闭自动刷新（点击⚙️按钮）
2. 手动选择需要的块或内容
3. 点击🔄按钮手动刷新
4. 精确控制上下文内容

## 💡 使用技巧

### 最佳实践
- 📝 **长时间对话**：启用自动刷新，保持上下文同步
- 🎯 **精确控制**：关闭自动刷新，手动选择上下文
- ⚡ **快速切换**：自动模式下快速在不同块间切换
- 🔄 **即时更新**：修改内容后立即看到上下文更新

### 注意事项
- 🔋 自动刷新会持续运行，可能略微增加CPU使用
- 📱 在移动设备上建议手动刷新以节省电量
- 🌐 网络不稳定时自动刷新可能失败，可切换到手动模式
- 💾 大量块的页面中，建议适当使用以避免频繁刷新

## 🛠️ 故障排除

### 常见问题
**Q: 自动刷新没有工作？**
A: 检查绿色指示器是否显示，确认自动刷新已启用

**Q: 刷新太频繁？**
A: 可以关闭自动刷新，改用手动模式

**Q: 上下文没有更新？**
A: 尝试手动刷新，或重新启用自动刷新

**Q: 影响性能？**
A: 1.5秒间隔设计已优化性能，如有需要可关闭自动刷新

### 重置方法
1. 关闭自动刷新
2. 手动刷新一次
3. 重新启用自动刷新
4. 检查状态指示器

## 🎉 享受新功能

自动刷新功能让您的AI对话体验更加流畅自然。无论是快速切换块还是深度编辑内容，系统都会智能地保持上下文同步，让您专注于创作而不是操作。

**开始使用吧！** 🚀
