import React, { useState, useEffect } from 'react';
import { getThemeManager, ThemeMode } from '../themeManager';

interface ThemeToggleProps {
  className?: string;
  showLabel?: boolean;
}

/**
 * 主题切换组件
 */
export const ThemeToggle: React.FC<ThemeToggleProps> = ({ 
  className = '', 
  showLabel = true 
}) => {
  const themeManager = getThemeManager();
  const [currentTheme, setCurrentTheme] = useState(themeManager.getTheme());

  useEffect(() => {
    const handleThemeChange = (theme: any) => {
      setCurrentTheme(theme);
    };

    themeManager.addListener(handleThemeChange);

    return () => {
      themeManager.removeListener(handleThemeChange);
    };
  }, [themeManager]);

  const handleThemeChange = (mode: ThemeMode) => {
    themeManager.setTheme(mode);
  };

  const getThemeIcon = (mode: ThemeMode) => {
    switch (mode) {
      case 'light':
        return '☀️';
      case 'dark':
        return '🌙';
      case 'auto':
        return '🔄';
      default:
        return '☀️';
    }
  };

  const getThemeLabel = (mode: ThemeMode) => {
    switch (mode) {
      case 'light':
        return '亮色模式';
      case 'dark':
        return '暗色模式';
      case 'auto':
        return '跟随系统';
      default:
        return '亮色模式';
    }
  };

  return (
    <div className={`theme-toggle ${className}`}>
      {showLabel && (
        <label 
          className="block text-sm font-medium mb-2"
          style={{ color: themeManager.getThemeColor('primary') }}
        >
          主题设置
        </label>
      )}
      
      <div className="flex items-center space-x-2">
        {(['light', 'dark', 'auto'] as ThemeMode[]).map((mode) => (
          <button
            key={mode}
            onClick={() => handleThemeChange(mode)}
            className={`
              flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200
              ${currentTheme.mode === mode 
                ? 'ring-2 ring-offset-2' 
                : 'hover:opacity-80'
              }
            `}
            style={{
              backgroundColor: currentTheme.mode === mode
                ? themeManager.getThemeColor('accent')
                : themeManager.getThemeColor('surface'),
              color: currentTheme.mode === mode
                ? '#ffffff'
                : themeManager.getThemeColor('primary')
            }}
          >
            <span className="text-base">{getThemeIcon(mode)}</span>
            <span>{getThemeLabel(mode)}</span>
          </button>
        ))}
      </div>
      
      {currentTheme.mode === 'auto' && (
        <div 
          className="mt-2 text-xs opacity-75"
          style={{ color: themeManager.getThemeColor('secondary') }}
        >
          当前: {currentTheme.isDark ? '暗色' : '亮色'} 
          {currentTheme.systemPrefersDark ? ' (系统偏好暗色)' : ' (系统偏好亮色)'}
        </div>
      )}
    </div>
  );
};

/**
 * 简单的主题切换按钮
 */
export const SimpleThemeToggle: React.FC<{ className?: string }> = ({ className = '' }) => {
  const themeManager = getThemeManager();
  const [isDark, setIsDark] = useState(themeManager.isDarkMode());

  useEffect(() => {
    const handleThemeChange = (theme: any) => {
      setIsDark(theme.isDark);
    };

    themeManager.addListener(handleThemeChange);

    return () => {
      themeManager.removeListener(handleThemeChange);
    };
  }, [themeManager]);

  const toggleTheme = () => {
    themeManager.toggleTheme();
  };

  return (
    <button
      onClick={toggleTheme}
      className={`
        flex items-center justify-center w-10 h-10 rounded-full transition-all duration-200
        hover:scale-110 active:scale-95 ${className}
      `}
      style={{
        backgroundColor: themeManager.getThemeColor('surface'),
        color: themeManager.getThemeColor('primary'),
        border: `1px solid ${themeManager.getThemeColor('border')}`
      }}
      title={isDark ? '切换到亮色模式' : '切换到暗色模式'}
    >
      <span className="text-lg">
        {isDark ? '☀️' : '🌙'}
      </span>
    </button>
  );
};

/**
 * 主题状态指示器
 */
export const ThemeIndicator: React.FC<{ className?: string }> = ({ className = '' }) => {
  const themeManager = getThemeManager();
  const [currentTheme, setCurrentTheme] = useState(themeManager.getTheme());

  useEffect(() => {
    const handleThemeChange = (theme: any) => {
      setCurrentTheme(theme);
    };

    themeManager.addListener(handleThemeChange);

    return () => {
      themeManager.removeListener(handleThemeChange);
    };
  }, [themeManager]);

  return (
    <div 
      className={`flex items-center space-x-2 text-sm ${className}`}
      style={{ color: themeManager.getThemeColor('secondary') }}
    >
      <div 
        className="w-3 h-3 rounded-full"
        style={{ 
          backgroundColor: currentTheme.isDark 
            ? '#4A90E2' 
            : '#F5A623' 
        }}
      />
      <span>
        {currentTheme.mode === 'auto' 
          ? `自动 (${currentTheme.isDark ? '暗色' : '亮色'})` 
          : currentTheme.isDark ? '暗色模式' : '亮色模式'
        }
      </span>
    </div>
  );
};
