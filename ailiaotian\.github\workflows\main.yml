# This is a basic workflow to help you get started with Actions

name: Releases

env:
  PLUGIN_NAME: logseq-plugin-template-react

# Controls when the action will run.
on:
  # push:
  #   branches:
  #     - "master"
  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  release:
    # The type of runner that the job will run on
    runs-on: ubuntu-latest

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      # Checks-out your repository under $GITHUB_WORKSPACE, so your job can access it
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: "16"
      - uses: pnpm/action-setup@v2.4.1
        with:
          version: 6.0.2
      - run: pnpm install
      - run: pnpm build
      - name: Install zip
        uses: montudor/action-zip@v1
      - name: Release
        run: npx semantic-release
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
