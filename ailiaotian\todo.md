# Logseq AI Chat 插件开发计划

## 第一阶段：项目配置与初始化

- [x] **基础项目初始化**
  - [x] 更新`package.json`中的插件信息：
    - 修改`name`为"logseq-plugin-ai-chat"
    - 添加适当的`description`
    - 在`logseq`部分修改`id`为合适的值（例如："logseq-ai-chat"）
    - 更新`icon`为适合AI聊天的图标
  - [x] 根据需要添加其他依赖项（如处理API请求的库）

- [x] **设置配置文件结构**
  - [x] 在`src`目录下创建`settings.ts`文件用于管理插件设置
  - [x] 定义基本设置结构：
    ```typescript
    interface PluginSettings {
      apiUrl: string;
      apiKey: string;
      modelName: string;
      enableHistory: boolean;
      customPrompts: Array<{name: string, prompt: string}>;
    }
    ```
  - [x] 实现设置初始化函数，使用`logseq.settings`API保存设置

## 第二阶段：设置界面开发

- [x] **创建设置页面UI组件**
  - [x] 在`src`目录下创建`components`文件夹
  - [x] 创建`SettingsUI.tsx`组件实现以下功能：
    - [x] API URL输入框
    - [x] API Key密码输入框（确保安全处理）
    - [x] 模型名称输入框
    - [x] 启用/禁用历史记录的开关
    - [x] 自定义提示管理部分：
      - [x] 显示现有提示列表
      - [x] 添加新提示的表单
      - [x] 删除提示的按钮
  - [x] 实现设置保存功能，通过`logseq.updateSettings`保存所有设置变更
  - [x] 实现设置加载功能，在组件挂载时从`logseq.settings`读取现有设置

## 第三阶段：触发机制与上下文获取

- [x] **注册插件命令**
  - [x] 在`main.tsx`中使用`logseq.Editor.registerSlashCommand`注册`/aichat`命令
  - [x] 使用`logseq.App.registerCommandShortcut`注册`Ctrl+G`快捷键

- [x] **实现上下文收集逻辑**
  - [x] 创建`src/contextManager.ts`实现以下功能：
    - [x] 检查是否有选中文本（使用`logseq.Editor.getEditingCursorPosition`）
    - [x] 检查是否有多个块被选中（使用`logseq.Editor.getSelectedBlocks`）
    - [x] 如果没有选择，获取当前块内容（使用`logseq.Editor.getCurrentBlock`）
    - [x] 返回适当的文本内容以及来源信息（单个块UUID或多个块UUID）

## 第四阶段：聊天界面UI与交互

- [x] **创建聊天模态框组件**
  - [x] 在`src/components`目录下创建`ChatModal.tsx`
  - [x] 设计模态框UI布局，确保其显示在活动块附近
  - [x] 添加以下UI元素：
    - [x] 消息/问题输入框（textarea）
    - [x] 提示选择下拉菜单（包括"默认"和自定义提示）
    - [x] AI响应显示区域（支持Markdown格式）
    - [x] "替换"和"插入"按钮（初始禁用，仅在收到响应后启用）
    - [x] 发送按钮
    - [x] 加载指示器（在等待API响应时显示）
    - [x] 错误消息显示区域

- [x] **添加历史记录功能（基于设置）**
  - [x] 如果`enableHistory`为`true`，添加显示之前消息的区域
  - [x] 添加清除历史记录的按钮

## 第五阶段：API集成与通信

- [x] **实现API请求功能**
  - [x] 创建`src/api.ts`文件实现以下功能：
    - [x] 从设置中获取`apiUrl`、`apiKey`、`modelName`
    - [x] 构建请求负载，包括：
      - [x] 用户提示/消息
      - [x] 收集的上下文
      - [x] 选定的系统提示（如有）
      - [x] 配置的`modelName`
      - [x] 启用流式响应的参数（`stream: true`）
    - [x] 设置适当的请求头（如`Authorization`、`Content-Type`）
    - [x] 使用fetch API发送POST请求

- [x] **实现流式响应处理**
  - [x] 处理流式响应（使用EventSource或处理分块数据）
  - [x] 解析传入的数据块，提取文本内容
  - [x] 逐步更新模态框的响应显示区域
  - [x] 处理流结束信号

- [x] **组合提示逻辑**
  - [x] 确保在发送到API之前正确组合选定的系统提示和用户输入消息

## 第六阶段：输出处理

- [x] **响应解析与格式化**
  - [x] 确保收到完整响应后，最终文本可用于进一步处理
  - [x] 实现Markdown到Logseq块格式的转换，处理换行、列表、代码块等

- [x] **"替换"功能实现**
  - [x] 获取原始块的UUID
  - [x] 使用`logseq.Editor.deleteBlock`或`logseq.Editor.removeBlocks`删除原始块
  - [x] 使用`logseq.Editor.insertBatchBlock`在原始位置插入AI响应
  - [x] 关闭模态框

- [x] **"插入"功能实现**
  - [x] 获取主要块的UUID
  - [x] 使用`logseq.Editor.insertBatchBlock`将AI响应作为原始块的子块插入
  - [x] 关闭模态框

## 第七阶段：错误处理与边缘情况

- [ ] **API错误处理**
  - [ ] 使用try/catch包装API调用
  - [ ] 捕获错误（网络错误、4xx/5xx状态码）并在模态框的错误区域显示友好消息

- [ ] **设置错误处理**
  - [ ] 处理API URL/Key/Model在设置中可能缺失的情况
  - [ ] 如果需要，通过模态框提示用户

- [ ] **上下文收集错误处理**
  - [ ] 处理上下文收集过程中可能出现的问题

- [ ] **流式传输错误处理**
  - [ ] 处理流式处理过程中的中断或错误

## 第八阶段：文档与打包

- [ ] **更新README.md**
  - [ ] 添加清晰的安装说明
  - [ ] 编写详细的使用指南（斜杠命令、快捷键、上下文规则、模态框交互、设置配置）
  - [ ] 解释自定义提示和历史记录功能
  - [ ] 提供API配置详情

- [ ] **添加代码注释**
  - [ ] 为复杂逻辑添加必要的注释

- [ ] **构建与打包**
  - [ ] 确保构建过程正常工作（`npm run build`）
  - [ ] 创建发布压缩包

## 开发注意事项

1. **安全考虑**：确保API密钥等敏感信息得到安全处理
2. **UI/UX设计**：确保聊天模态框的用户体验流畅，响应式布局适应不同屏幕尺寸
3. **性能优化**：处理大型响应时保持应用性能
4. **测试**：在每个阶段测试各项功能
5. **国际化**：考虑添加多语言支持
6. **版本控制**：使用语义化版本控制，维护CHANGELOG.md 