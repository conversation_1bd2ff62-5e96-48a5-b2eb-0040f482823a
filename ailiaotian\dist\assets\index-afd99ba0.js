(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const i of o)if(i.type==="childList")for(const a of i.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&r(a)}).observe(document,{childList:!0,subtree:!0});function n(o){const i={};return o.integrity&&(i.integrity=o.integrity),o.referrerPolicy&&(i.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?i.credentials="include":o.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(o){if(o.ep)return;o.ep=!0;const i=n(o);fetch(o.href,i)}})();var $i=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Uh(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Od={exports:{}},Vl={},Id={exports:{}},he={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var fi=Symbol.for("react.element"),$h=Symbol.for("react.portal"),Bh=Symbol.for("react.fragment"),Hh=Symbol.for("react.strict_mode"),Wh=Symbol.for("react.profiler"),Vh=Symbol.for("react.provider"),qh=Symbol.for("react.context"),Qh=Symbol.for("react.forward_ref"),Kh=Symbol.for("react.suspense"),Gh=Symbol.for("react.memo"),Yh=Symbol.for("react.lazy"),wc=Symbol.iterator;function Xh(e){return e===null||typeof e!="object"?null:(e=wc&&e[wc]||e["@@iterator"],typeof e=="function"?e:null)}var Ld={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},jd=Object.assign,Md={};function ao(e,t,n){this.props=e,this.context=t,this.refs=Md,this.updater=n||Ld}ao.prototype.isReactComponent={};ao.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};ao.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Ad(){}Ad.prototype=ao.prototype;function Ja(e,t,n){this.props=e,this.context=t,this.refs=Md,this.updater=n||Ld}var eu=Ja.prototype=new Ad;eu.constructor=Ja;jd(eu,ao.prototype);eu.isPureReactComponent=!0;var kc=Array.isArray,Rd=Object.prototype.hasOwnProperty,tu={current:null},zd={key:!0,ref:!0,__self:!0,__source:!0};function Dd(e,t,n){var r,o={},i=null,a=null;if(t!=null)for(r in t.ref!==void 0&&(a=t.ref),t.key!==void 0&&(i=""+t.key),t)Rd.call(t,r)&&!zd.hasOwnProperty(r)&&(o[r]=t[r]);var c=arguments.length-2;if(c===1)o.children=n;else if(1<c){for(var f=Array(c),p=0;p<c;p++)f[p]=arguments[p+2];o.children=f}if(e&&e.defaultProps)for(r in c=e.defaultProps,c)o[r]===void 0&&(o[r]=c[r]);return{$$typeof:fi,type:e,key:i,ref:a,props:o,_owner:tu.current}}function Zh(e,t){return{$$typeof:fi,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function nu(e){return typeof e=="object"&&e!==null&&e.$$typeof===fi}function Jh(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Cc=/\/+/g;function js(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Jh(""+e.key):t.toString(36)}function sl(e,t,n,r,o){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var a=!1;if(e===null)a=!0;else switch(i){case"string":case"number":a=!0;break;case"object":switch(e.$$typeof){case fi:case $h:a=!0}}if(a)return a=e,o=o(a),e=r===""?"."+js(a,0):r,kc(o)?(n="",e!=null&&(n=e.replace(Cc,"$&/")+"/"),sl(o,t,n,"",function(p){return p})):o!=null&&(nu(o)&&(o=Zh(o,n+(!o.key||a&&a.key===o.key?"":(""+o.key).replace(Cc,"$&/")+"/")+e)),t.push(o)),1;if(a=0,r=r===""?".":r+":",kc(e))for(var c=0;c<e.length;c++){i=e[c];var f=r+js(i,c);a+=sl(i,t,n,f,o)}else if(f=Xh(e),typeof f=="function")for(e=f.call(e),c=0;!(i=e.next()).done;)i=i.value,f=r+js(i,c++),a+=sl(i,t,n,f,o);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return a}function Bi(e,t,n){if(e==null)return e;var r=[],o=0;return sl(e,r,"","",function(i){return t.call(n,i,o++)}),r}function em(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var ut={current:null},al={transition:null},tm={ReactCurrentDispatcher:ut,ReactCurrentBatchConfig:al,ReactCurrentOwner:tu};function Fd(){throw Error("act(...) is not supported in production builds of React.")}he.Children={map:Bi,forEach:function(e,t,n){Bi(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Bi(e,function(){t++}),t},toArray:function(e){return Bi(e,function(t){return t})||[]},only:function(e){if(!nu(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};he.Component=ao;he.Fragment=Bh;he.Profiler=Wh;he.PureComponent=Ja;he.StrictMode=Hh;he.Suspense=Kh;he.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=tm;he.act=Fd;he.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=jd({},e.props),o=e.key,i=e.ref,a=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,a=tu.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var c=e.type.defaultProps;for(f in t)Rd.call(t,f)&&!zd.hasOwnProperty(f)&&(r[f]=t[f]===void 0&&c!==void 0?c[f]:t[f])}var f=arguments.length-2;if(f===1)r.children=n;else if(1<f){c=Array(f);for(var p=0;p<f;p++)c[p]=arguments[p+2];r.children=c}return{$$typeof:fi,type:e.type,key:o,ref:i,props:r,_owner:a}};he.createContext=function(e){return e={$$typeof:qh,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Vh,_context:e},e.Consumer=e};he.createElement=Dd;he.createFactory=function(e){var t=Dd.bind(null,e);return t.type=e,t};he.createRef=function(){return{current:null}};he.forwardRef=function(e){return{$$typeof:Qh,render:e}};he.isValidElement=nu;he.lazy=function(e){return{$$typeof:Yh,_payload:{_status:-1,_result:e},_init:em}};he.memo=function(e,t){return{$$typeof:Gh,type:e,compare:t===void 0?null:t}};he.startTransition=function(e){var t=al.transition;al.transition={};try{e()}finally{al.transition=t}};he.unstable_act=Fd;he.useCallback=function(e,t){return ut.current.useCallback(e,t)};he.useContext=function(e){return ut.current.useContext(e)};he.useDebugValue=function(){};he.useDeferredValue=function(e){return ut.current.useDeferredValue(e)};he.useEffect=function(e,t){return ut.current.useEffect(e,t)};he.useId=function(){return ut.current.useId()};he.useImperativeHandle=function(e,t,n){return ut.current.useImperativeHandle(e,t,n)};he.useInsertionEffect=function(e,t){return ut.current.useInsertionEffect(e,t)};he.useLayoutEffect=function(e,t){return ut.current.useLayoutEffect(e,t)};he.useMemo=function(e,t){return ut.current.useMemo(e,t)};he.useReducer=function(e,t,n){return ut.current.useReducer(e,t,n)};he.useRef=function(e){return ut.current.useRef(e)};he.useState=function(e){return ut.current.useState(e)};he.useSyncExternalStore=function(e,t,n){return ut.current.useSyncExternalStore(e,t,n)};he.useTransition=function(){return ut.current.useTransition()};he.version="18.3.1";Id.exports=he;var ae=Id.exports;const Ud=Uh(ae);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var nm=ae,rm=Symbol.for("react.element"),om=Symbol.for("react.fragment"),im=Object.prototype.hasOwnProperty,lm=nm.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,sm={key:!0,ref:!0,__self:!0,__source:!0};function $d(e,t,n){var r,o={},i=null,a=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(a=t.ref);for(r in t)im.call(t,r)&&!sm.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:rm,type:e,key:i,ref:a,props:o,_owner:lm.current}}Vl.Fragment=om;Vl.jsx=$d;Vl.jsxs=$d;Od.exports=Vl;var ru=Od.exports;const am=ru.Fragment,D=ru.jsx,se=ru.jsxs;var wl={exports:{}};/*! For license information please see lsplugin.user.js.LICENSE.txt */wl.exports;(function(e,t){(function(n,r){e.exports=r()})(self,()=>(()=>{var n={227:(a,c,f)=>{var p=f(155);c.formatArgs=function(C){if(C[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+C[0]+(this.useColors?"%c ":" ")+"+"+a.exports.humanize(this.diff),!this.useColors)return;const x="color: "+this.color;C.splice(1,0,x,"color: inherit");let E=0,v=0;C[0].replace(/%[a-zA-Z%]/g,N=>{N!=="%%"&&(E++,N==="%c"&&(v=E))}),C.splice(v,0,x)},c.save=function(C){try{C?c.storage.setItem("debug",C):c.storage.removeItem("debug")}catch{}},c.load=function(){let C;try{C=c.storage.getItem("debug")}catch{}return!C&&p!==void 0&&"env"in p&&(C=p.env.DEBUG),C},c.useColors=function(){return typeof window<"u"&&window.process&&(window.process.type==="renderer"||window.process.__nwjs)?!0:typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/)?!1:typeof document<"u"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window<"u"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},c.storage=function(){try{return localStorage}catch{}}(),c.destroy=(()=>{let C=!1;return()=>{C||(C=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),c.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],c.log=console.debug||console.log||(()=>{}),a.exports=f(447)(c);const{formatters:w}=a.exports;w.j=function(C){try{return JSON.stringify(C)}catch(x){return"[UnexpectedJSONParseError]: "+x.message}}},447:(a,c,f)=>{a.exports=function(p){function w(E){let v,N,O,h=null;function d(...g){if(!d.enabled)return;const S=d,P=Number(new Date),L=P-(v||P);S.diff=L,S.prev=v,S.curr=P,v=P,g[0]=w.coerce(g[0]),typeof g[0]!="string"&&g.unshift("%O");let y=0;g[0]=g[0].replace(/%([a-zA-Z%])/g,(_,A)=>{if(_==="%%")return"%";y++;const j=w.formatters[A];if(typeof j=="function"){const G=g[y];_=j.call(S,G),g.splice(y,1),y--}return _}),w.formatArgs.call(S,g),(S.log||w.log).apply(S,g)}return d.namespace=E,d.useColors=w.useColors(),d.color=w.selectColor(E),d.extend=C,d.destroy=w.destroy,Object.defineProperty(d,"enabled",{enumerable:!0,configurable:!1,get:()=>h!==null?h:(N!==w.namespaces&&(N=w.namespaces,O=w.enabled(E)),O),set:g=>{h=g}}),typeof w.init=="function"&&w.init(d),d}function C(E,v){const N=w(this.namespace+(v===void 0?":":v)+E);return N.log=this.log,N}function x(E){return E.toString().substring(2,E.toString().length-2).replace(/\.\*\?$/,"*")}return w.debug=w,w.default=w,w.coerce=function(E){return E instanceof Error?E.stack||E.message:E},w.disable=function(){const E=[...w.names.map(x),...w.skips.map(x).map(v=>"-"+v)].join(",");return w.enable(""),E},w.enable=function(E){let v;w.save(E),w.namespaces=E,w.names=[],w.skips=[];const N=(typeof E=="string"?E:"").split(/[\s,]+/),O=N.length;for(v=0;v<O;v++)N[v]&&((E=N[v].replace(/\*/g,".*?"))[0]==="-"?w.skips.push(new RegExp("^"+E.slice(1)+"$")):w.names.push(new RegExp("^"+E+"$")))},w.enabled=function(E){if(E[E.length-1]==="*")return!0;let v,N;for(v=0,N=w.skips.length;v<N;v++)if(w.skips[v].test(E))return!1;for(v=0,N=w.names.length;v<N;v++)if(w.names[v].test(E))return!0;return!1},w.humanize=f(824),w.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(p).forEach(E=>{w[E]=p[E]}),w.names=[],w.skips=[],w.formatters={},w.selectColor=function(E){let v=0;for(let N=0;N<E.length;N++)v=(v<<5)-v+E.charCodeAt(N),v|=0;return w.colors[Math.abs(v)%w.colors.length]},w.enable(w.load()),w}},996:a=>{var c=function(O){return function(h){return!!h&&typeof h=="object"}(O)&&!function(h){var d=Object.prototype.toString.call(h);return d==="[object RegExp]"||d==="[object Date]"||function(g){return g.$$typeof===f}(h)}(O)},f=typeof Symbol=="function"&&Symbol.for?Symbol.for("react.element"):60103;function p(O,h){return h.clone!==!1&&h.isMergeableObject(O)?v((d=O,Array.isArray(d)?[]:{}),O,h):O;var d}function w(O,h,d){return O.concat(h).map(function(g){return p(g,d)})}function C(O){return Object.keys(O).concat(function(h){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(h).filter(function(d){return Object.propertyIsEnumerable.call(h,d)}):[]}(O))}function x(O,h){try{return h in O}catch{return!1}}function E(O,h,d){var g={};return d.isMergeableObject(O)&&C(O).forEach(function(S){g[S]=p(O[S],d)}),C(h).forEach(function(S){(function(P,L){return x(P,L)&&!(Object.hasOwnProperty.call(P,L)&&Object.propertyIsEnumerable.call(P,L))})(O,S)||(x(O,S)&&d.isMergeableObject(h[S])?g[S]=function(P,L){if(!L.customMerge)return v;var y=L.customMerge(P);return typeof y=="function"?y:v}(S,d)(O[S],h[S],d):g[S]=p(h[S],d))}),g}function v(O,h,d){(d=d||{}).arrayMerge=d.arrayMerge||w,d.isMergeableObject=d.isMergeableObject||c,d.cloneUnlessOtherwiseSpecified=p;var g=Array.isArray(h);return g===Array.isArray(O)?g?d.arrayMerge(O,h,d):E(O,h,d):p(h,d)}v.all=function(O,h){if(!Array.isArray(O))throw new Error("first argument should be an array");return O.reduce(function(d,g){return v(d,g,h)},{})};var N=v;a.exports=N},856:function(a){a.exports=function(){function c(W){return c=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(K){return typeof K}:function(K){return K&&typeof Symbol=="function"&&K.constructor===Symbol&&K!==Symbol.prototype?"symbol":typeof K},c(W)}function f(W,K){return f=Object.setPrototypeOf||function(re,pe){return re.__proto__=pe,re},f(W,K)}function p(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function w(W,K,re){return w=p()?Reflect.construct:function(pe,ze,At){var Yt=[null];Yt.push.apply(Yt,ze);var Sn=new(Function.bind.apply(pe,Yt));return At&&f(Sn,At.prototype),Sn},w.apply(null,arguments)}function C(W){return x(W)||E(W)||v(W)||O()}function x(W){if(Array.isArray(W))return N(W)}function E(W){if(typeof Symbol<"u"&&W[Symbol.iterator]!=null||W["@@iterator"]!=null)return Array.from(W)}function v(W,K){if(W){if(typeof W=="string")return N(W,K);var re=Object.prototype.toString.call(W).slice(8,-1);return re==="Object"&&W.constructor&&(re=W.constructor.name),re==="Map"||re==="Set"?Array.from(W):re==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(re)?N(W,K):void 0}}function N(W,K){(K==null||K>W.length)&&(K=W.length);for(var re=0,pe=new Array(K);re<K;re++)pe[re]=W[re];return pe}function O(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var h=Object.hasOwnProperty,d=Object.setPrototypeOf,g=Object.isFrozen,S=Object.getPrototypeOf,P=Object.getOwnPropertyDescriptor,L=Object.freeze,y=Object.seal,_=Object.create,A=typeof Reflect<"u"&&Reflect,j=A.apply,G=A.construct;j||(j=function(W,K,re){return W.apply(K,re)}),L||(L=function(W){return W}),y||(y=function(W){return W}),G||(G=function(W,K){return w(W,C(K))});var ce=ee(Array.prototype.forEach),de=ee(Array.prototype.pop),te=ee(Array.prototype.push),Te=ee(String.prototype.toLowerCase),Ne=ee(String.prototype.match),Q=ee(String.prototype.replace),F=ee(String.prototype.indexOf),T=ee(String.prototype.trim),M=ee(RegExp.prototype.test),$=ve(TypeError);function ee(W){return function(K){for(var re=arguments.length,pe=new Array(re>1?re-1:0),ze=1;ze<re;ze++)pe[ze-1]=arguments[ze];return j(W,K,pe)}}function ve(W){return function(){for(var K=arguments.length,re=new Array(K),pe=0;pe<K;pe++)re[pe]=arguments[pe];return G(W,re)}}function B(W,K){d&&d(W,null);for(var re=K.length;re--;){var pe=K[re];if(typeof pe=="string"){var ze=Te(pe);ze!==pe&&(g(K)||(K[re]=ze),pe=ze)}W[pe]=!0}return W}function oe(W){var K,re=_(null);for(K in W)j(h,W,[K])&&(re[K]=W[K]);return re}function fe(W,K){for(;W!==null;){var re=P(W,K);if(re){if(re.get)return ee(re.get);if(typeof re.value=="function")return ee(re.value)}W=S(W)}function pe(ze){return console.warn("fallback value for",ze),null}return pe}var Re=L(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),qe=L(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),Y=L(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),ie=L(["animate","color-profile","cursor","discard","fedropshadow","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),we=L(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover"]),me=L(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),Ue=L(["#text"]),Ge=L(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),jt=L(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),ln=L(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),vt=L(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),dt=y(/\{\{[\w\W]*|[\w\W]*\}\}/gm),Jn=y(/<%[\w\W]*|[\w\W]*%>/gm),as=y(/^data-[\-\w.\u00B7-\uFFFF]/),Mt=y(/^aria-[\-\w]+$/),Gt=y(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),yi=y(/^(?:\w+script|data):/i),us=y(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),cs=y(/^html$/i),er=function(){return typeof window>"u"?null:window},ds=function(W,K){if(c(W)!=="object"||typeof W.createPolicy!="function")return null;var re=null,pe="data-tt-policy-suffix";K.currentScript&&K.currentScript.hasAttribute(pe)&&(re=K.currentScript.getAttribute(pe));var ze="dompurify"+(re?"#"+re:"");try{return W.createPolicy(ze,{createHTML:function(At){return At}})}catch{return console.warn("TrustedTypes policy "+ze+" could not be created."),null}};function vi(){var W=arguments.length>0&&arguments[0]!==void 0?arguments[0]:er(),K=function(k){return vi(k)};if(K.version="2.3.8",K.removed=[],!W||!W.document||W.document.nodeType!==9)return K.isSupported=!1,K;var re=W.document,pe=W.document,ze=W.DocumentFragment,At=W.HTMLTemplateElement,Yt=W.Node,Sn=W.Element,po=W.NodeFilter,wi=W.NamedNodeMap,sn=wi===void 0?W.NamedNodeMap||W.MozNamedAttrMap:wi,fs=W.HTMLFormElement,ps=W.DOMParser,hs=W.trustedTypes,Er=Sn.prototype,ms=fe(Er,"cloneNode"),gs=fe(Er,"nextSibling"),ys=fe(Er,"childNodes"),ho=fe(Er,"parentNode");if(typeof At=="function"){var Rt=pe.createElement("template");Rt.content&&Rt.content.ownerDocument&&(pe=Rt.content.ownerDocument)}var zt=ds(hs,re),ki=zt?zt.createHTML(""):"",Tr=pe,mo=Tr.implementation,_n=Tr.createNodeIterator,Ci=Tr.createDocumentFragment,Si=Tr.getElementsByTagName,vs=re.importNode,_i={};try{_i=oe(pe).documentMode?pe.documentMode:{}}catch{}var wt={};K.isSupported=typeof ho=="function"&&mo&&mo.createHTMLDocument!==void 0&&_i!==9;var tr,Xt,Nr=dt,br=Jn,go=as,ws=Mt,xi=yi,Pr=us,ke=Gt,$e=null,Ei=B({},[].concat(C(Re),C(qe),C(Y),C(we),C(Ue))),Be=null,xn=B({},[].concat(C(Ge),C(jt),C(ln),C(vt))),Oe=Object.seal(Object.create(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),En=null,Or=null,yo=!0,vo=!0,Ti=!1,Tn=!1,an=!1,wo=!1,ko=!1,Nn=!1,Ir=!1,bn=!1,Ni=!0,Co=!0,Pn=!1,Dt={},On=null,bi=B({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]),Pi=null,Oi=B({},["audio","video","img","source","image","track"]),So=null,un=B({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),In="http://www.w3.org/1998/Math/MathML",_o="http://www.w3.org/2000/svg",Zt="http://www.w3.org/1999/xhtml",Lr=Zt,Ii=!1,nr=["application/xhtml+xml","text/html"],rr="text/html",Ln=null,ks=pe.createElement("form"),Li=function(k){return k instanceof RegExp||k instanceof Function},xo=function(k){Ln&&Ln===k||(k&&c(k)==="object"||(k={}),k=oe(k),$e="ALLOWED_TAGS"in k?B({},k.ALLOWED_TAGS):Ei,Be="ALLOWED_ATTR"in k?B({},k.ALLOWED_ATTR):xn,So="ADD_URI_SAFE_ATTR"in k?B(oe(un),k.ADD_URI_SAFE_ATTR):un,Pi="ADD_DATA_URI_TAGS"in k?B(oe(Oi),k.ADD_DATA_URI_TAGS):Oi,On="FORBID_CONTENTS"in k?B({},k.FORBID_CONTENTS):bi,En="FORBID_TAGS"in k?B({},k.FORBID_TAGS):{},Or="FORBID_ATTR"in k?B({},k.FORBID_ATTR):{},Dt="USE_PROFILES"in k&&k.USE_PROFILES,yo=k.ALLOW_ARIA_ATTR!==!1,vo=k.ALLOW_DATA_ATTR!==!1,Ti=k.ALLOW_UNKNOWN_PROTOCOLS||!1,Tn=k.SAFE_FOR_TEMPLATES||!1,an=k.WHOLE_DOCUMENT||!1,Nn=k.RETURN_DOM||!1,Ir=k.RETURN_DOM_FRAGMENT||!1,bn=k.RETURN_TRUSTED_TYPE||!1,ko=k.FORCE_BODY||!1,Ni=k.SANITIZE_DOM!==!1,Co=k.KEEP_CONTENT!==!1,Pn=k.IN_PLACE||!1,ke=k.ALLOWED_URI_REGEXP||ke,Lr=k.NAMESPACE||Zt,k.CUSTOM_ELEMENT_HANDLING&&Li(k.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(Oe.tagNameCheck=k.CUSTOM_ELEMENT_HANDLING.tagNameCheck),k.CUSTOM_ELEMENT_HANDLING&&Li(k.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(Oe.attributeNameCheck=k.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),k.CUSTOM_ELEMENT_HANDLING&&typeof k.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements=="boolean"&&(Oe.allowCustomizedBuiltInElements=k.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),tr=tr=nr.indexOf(k.PARSER_MEDIA_TYPE)===-1?rr:k.PARSER_MEDIA_TYPE,Xt=tr==="application/xhtml+xml"?function(q){return q}:Te,Tn&&(vo=!1),Ir&&(Nn=!0),Dt&&($e=B({},C(Ue)),Be=[],Dt.html===!0&&(B($e,Re),B(Be,Ge)),Dt.svg===!0&&(B($e,qe),B(Be,jt),B(Be,vt)),Dt.svgFilters===!0&&(B($e,Y),B(Be,jt),B(Be,vt)),Dt.mathMl===!0&&(B($e,we),B(Be,ln),B(Be,vt))),k.ADD_TAGS&&($e===Ei&&($e=oe($e)),B($e,k.ADD_TAGS)),k.ADD_ATTR&&(Be===xn&&(Be=oe(Be)),B(Be,k.ADD_ATTR)),k.ADD_URI_SAFE_ATTR&&B(So,k.ADD_URI_SAFE_ATTR),k.FORBID_CONTENTS&&(On===bi&&(On=oe(On)),B(On,k.FORBID_CONTENTS)),Co&&($e["#text"]=!0),an&&B($e,["html","head","body"]),$e.table&&(B($e,["tbody"]),delete En.tbody),L&&L(k),Ln=k)},ji=B({},["mi","mo","mn","ms","mtext"]),Mi=B({},["foreignobject","desc","title","annotation-xml"]),Cs=B({},["title","style","font","a","script"]),Ft=B({},qe);B(Ft,Y),B(Ft,ie);var jr=B({},we);B(jr,me);var Ss=function(k){var q=ho(k);q&&q.tagName||(q={namespaceURI:Zt,tagName:"template"});var H=Te(k.tagName),Ce=Te(q.tagName);return k.namespaceURI===_o?q.namespaceURI===Zt?H==="svg":q.namespaceURI===In?H==="svg"&&(Ce==="annotation-xml"||ji[Ce]):!!Ft[H]:k.namespaceURI===In?q.namespaceURI===Zt?H==="math":q.namespaceURI===_o?H==="math"&&Mi[Ce]:!!jr[H]:k.namespaceURI===Zt&&!(q.namespaceURI===_o&&!Mi[Ce])&&!(q.namespaceURI===In&&!ji[Ce])&&!jr[H]&&(Cs[H]||!Ft[H])},Ut=function(k){te(K.removed,{element:k});try{k.parentNode.removeChild(k)}catch{try{k.outerHTML=ki}catch{k.remove()}}},or=function(k,q){try{te(K.removed,{attribute:q.getAttributeNode(k),from:q})}catch{te(K.removed,{attribute:null,from:q})}if(q.removeAttribute(k),k==="is"&&!Be[k])if(Nn||Ir)try{Ut(q)}catch{}else try{q.setAttribute(k,"")}catch{}},Ai=function(k){var q,H;if(ko)k="<remove></remove>"+k;else{var Ce=Ne(k,/^[\r\n\t ]+/);H=Ce&&Ce[0]}tr==="application/xhtml+xml"&&(k='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+k+"</body></html>");var Le=zt?zt.createHTML(k):k;if(Lr===Zt)try{q=new ps().parseFromString(Le,tr)}catch{}if(!q||!q.documentElement){q=mo.createDocument(Lr,"template",null);try{q.documentElement.innerHTML=Ii?"":Le}catch{}}var Ye=q.body||q.documentElement;return k&&H&&Ye.insertBefore(pe.createTextNode(H),Ye.childNodes[0]||null),Lr===Zt?Si.call(q,an?"html":"body")[0]:an?q.documentElement:Ye},Ri=function(k){return _n.call(k.ownerDocument||k,k,po.SHOW_ELEMENT|po.SHOW_COMMENT|po.SHOW_TEXT,null,!1)},_s=function(k){return k instanceof fs&&(typeof k.nodeName!="string"||typeof k.textContent!="string"||typeof k.removeChild!="function"||!(k.attributes instanceof sn)||typeof k.removeAttribute!="function"||typeof k.setAttribute!="function"||typeof k.namespaceURI!="string"||typeof k.insertBefore!="function")},ir=function(k){return c(Yt)==="object"?k instanceof Yt:k&&c(k)==="object"&&typeof k.nodeType=="number"&&typeof k.nodeName=="string"},nt=function(k,q,H){wt[k]&&ce(wt[k],function(Ce){Ce.call(K,q,H,Ln)})},lr=function(k){var q;if(nt("beforeSanitizeElements",k,null),_s(k)||M(/[\u0080-\uFFFF]/,k.nodeName))return Ut(k),!0;var H=Xt(k.nodeName);if(nt("uponSanitizeElement",k,{tagName:H,allowedTags:$e}),k.hasChildNodes()&&!ir(k.firstElementChild)&&(!ir(k.content)||!ir(k.content.firstElementChild))&&M(/<[/\w]/g,k.innerHTML)&&M(/<[/\w]/g,k.textContent)||H==="select"&&M(/<template/i,k.innerHTML))return Ut(k),!0;if(!$e[H]||En[H]){if(!En[H]&&cn(H)&&(Oe.tagNameCheck instanceof RegExp&&M(Oe.tagNameCheck,H)||Oe.tagNameCheck instanceof Function&&Oe.tagNameCheck(H)))return!1;if(Co&&!On[H]){var Ce=ho(k)||k.parentNode,Le=ys(k)||k.childNodes;if(Le&&Ce)for(var Ye=Le.length-1;Ye>=0;--Ye)Ce.insertBefore(ms(Le[Ye],!0),gs(k))}return Ut(k),!0}return k instanceof Sn&&!Ss(k)?(Ut(k),!0):H!=="noscript"&&H!=="noembed"||!M(/<\/no(script|embed)/i,k.innerHTML)?(Tn&&k.nodeType===3&&(q=k.textContent,q=Q(q,Nr," "),q=Q(q,br," "),k.textContent!==q&&(te(K.removed,{element:k.cloneNode()}),k.textContent=q)),nt("afterSanitizeElements",k,null),!1):(Ut(k),!0)},zi=function(k,q,H){if(Ni&&(q==="id"||q==="name")&&(H in pe||H in ks))return!1;if(!(vo&&!Or[q]&&M(go,q))){if(!(yo&&M(ws,q))){if(!Be[q]||Or[q]){if(!(cn(k)&&(Oe.tagNameCheck instanceof RegExp&&M(Oe.tagNameCheck,k)||Oe.tagNameCheck instanceof Function&&Oe.tagNameCheck(k))&&(Oe.attributeNameCheck instanceof RegExp&&M(Oe.attributeNameCheck,q)||Oe.attributeNameCheck instanceof Function&&Oe.attributeNameCheck(q))||q==="is"&&Oe.allowCustomizedBuiltInElements&&(Oe.tagNameCheck instanceof RegExp&&M(Oe.tagNameCheck,H)||Oe.tagNameCheck instanceof Function&&Oe.tagNameCheck(H))))return!1}else if(!So[q]){if(!M(ke,Q(H,Pr,""))){if((q!=="src"&&q!=="xlink:href"&&q!=="href"||k==="script"||F(H,"data:")!==0||!Pi[k])&&!(Ti&&!M(xi,Q(H,Pr,"")))){if(H)return!1}}}}}return!0},cn=function(k){return k.indexOf("-")>0},sr=function(k){var q,H,Ce,Le;nt("beforeSanitizeAttributes",k,null);var Ye=k.attributes;if(Ye){var He={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:Be};for(Le=Ye.length;Le--;){var ar=q=Ye[Le],Jt=ar.name,ft=ar.namespaceURI;if(H=Jt==="value"?q.value:T(q.value),Ce=Xt(Jt),He.attrName=Ce,He.attrValue=H,He.keepAttr=!0,He.forceKeepAttr=void 0,nt("uponSanitizeAttribute",k,He),H=He.attrValue,!He.forceKeepAttr&&(or(Jt,k),He.keepAttr))if(M(/\/>/i,H))or(Jt,k);else{Tn&&(H=Q(H,Nr," "),H=Q(H,br," "));var Eo=Xt(k.nodeName);if(zi(Eo,Ce,H))try{ft?k.setAttributeNS(ft,Jt,H):k.setAttribute(Jt,H),de(K.removed)}catch{}}}nt("afterSanitizeAttributes",k,null)}},xs=function k(q){var H,Ce=Ri(q);for(nt("beforeSanitizeShadowDOM",q,null);H=Ce.nextNode();)nt("uponSanitizeShadowNode",H,null),lr(H)||(H.content instanceof ze&&k(H.content),sr(H));nt("afterSanitizeShadowDOM",q,null)};return K.sanitize=function(k,q){var H,Ce,Le,Ye,He;if((Ii=!k)&&(k="<!-->"),typeof k!="string"&&!ir(k)){if(typeof k.toString!="function")throw $("toString is not a function");if(typeof(k=k.toString())!="string")throw $("dirty is not a string, aborting")}if(!K.isSupported){if(c(W.toStaticHTML)==="object"||typeof W.toStaticHTML=="function"){if(typeof k=="string")return W.toStaticHTML(k);if(ir(k))return W.toStaticHTML(k.outerHTML)}return k}if(wo||xo(q),K.removed=[],typeof k=="string"&&(Pn=!1),Pn){if(k.nodeName){var ar=Xt(k.nodeName);if(!$e[ar]||En[ar])throw $("root node is forbidden and cannot be sanitized in-place")}}else if(k instanceof Yt)(Ce=(H=Ai("<!---->")).ownerDocument.importNode(k,!0)).nodeType===1&&Ce.nodeName==="BODY"||Ce.nodeName==="HTML"?H=Ce:H.appendChild(Ce);else{if(!Nn&&!Tn&&!an&&k.indexOf("<")===-1)return zt&&bn?zt.createHTML(k):k;if(!(H=Ai(k)))return Nn?null:bn?ki:""}H&&ko&&Ut(H.firstChild);for(var Jt=Ri(Pn?k:H);Le=Jt.nextNode();)Le.nodeType===3&&Le===Ye||lr(Le)||(Le.content instanceof ze&&xs(Le.content),sr(Le),Ye=Le);if(Ye=null,Pn)return k;if(Nn){if(Ir)for(He=Ci.call(H.ownerDocument);H.firstChild;)He.appendChild(H.firstChild);else He=H;return Be.shadowroot&&(He=vs.call(re,He,!0)),He}var ft=an?H.outerHTML:H.innerHTML;return an&&$e["!doctype"]&&H.ownerDocument&&H.ownerDocument.doctype&&H.ownerDocument.doctype.name&&M(cs,H.ownerDocument.doctype.name)&&(ft="<!DOCTYPE "+H.ownerDocument.doctype.name+`>
`+ft),Tn&&(ft=Q(ft,Nr," "),ft=Q(ft,br," ")),zt&&bn?zt.createHTML(ft):ft},K.setConfig=function(k){xo(k),wo=!0},K.clearConfig=function(){Ln=null,wo=!1},K.isValidAttribute=function(k,q,H){Ln||xo({});var Ce=Xt(k),Le=Xt(q);return zi(Ce,Le,H)},K.addHook=function(k,q){typeof q=="function"&&(wt[k]=wt[k]||[],te(wt[k],q))},K.removeHook=function(k){if(wt[k])return de(wt[k])},K.removeHooks=function(k){wt[k]&&(wt[k]=[])},K.removeAllHooks=function(){wt={}},K}return vi()}()},729:a=>{var c=Object.prototype.hasOwnProperty,f="~";function p(){}function w(v,N,O){this.fn=v,this.context=N,this.once=O||!1}function C(v,N,O,h,d){if(typeof O!="function")throw new TypeError("The listener must be a function");var g=new w(O,h||v,d),S=f?f+N:N;return v._events[S]?v._events[S].fn?v._events[S]=[v._events[S],g]:v._events[S].push(g):(v._events[S]=g,v._eventsCount++),v}function x(v,N){--v._eventsCount==0?v._events=new p:delete v._events[N]}function E(){this._events=new p,this._eventsCount=0}Object.create&&(p.prototype=Object.create(null),new p().__proto__||(f=!1)),E.prototype.eventNames=function(){var v,N,O=[];if(this._eventsCount===0)return O;for(N in v=this._events)c.call(v,N)&&O.push(f?N.slice(1):N);return Object.getOwnPropertySymbols?O.concat(Object.getOwnPropertySymbols(v)):O},E.prototype.listeners=function(v){var N=f?f+v:v,O=this._events[N];if(!O)return[];if(O.fn)return[O.fn];for(var h=0,d=O.length,g=new Array(d);h<d;h++)g[h]=O[h].fn;return g},E.prototype.listenerCount=function(v){var N=f?f+v:v,O=this._events[N];return O?O.fn?1:O.length:0},E.prototype.emit=function(v,N,O,h,d,g){var S=f?f+v:v;if(!this._events[S])return!1;var P,L,y=this._events[S],_=arguments.length;if(y.fn){switch(y.once&&this.removeListener(v,y.fn,void 0,!0),_){case 1:return y.fn.call(y.context),!0;case 2:return y.fn.call(y.context,N),!0;case 3:return y.fn.call(y.context,N,O),!0;case 4:return y.fn.call(y.context,N,O,h),!0;case 5:return y.fn.call(y.context,N,O,h,d),!0;case 6:return y.fn.call(y.context,N,O,h,d,g),!0}for(L=1,P=new Array(_-1);L<_;L++)P[L-1]=arguments[L];y.fn.apply(y.context,P)}else{var A,j=y.length;for(L=0;L<j;L++)switch(y[L].once&&this.removeListener(v,y[L].fn,void 0,!0),_){case 1:y[L].fn.call(y[L].context);break;case 2:y[L].fn.call(y[L].context,N);break;case 3:y[L].fn.call(y[L].context,N,O);break;case 4:y[L].fn.call(y[L].context,N,O,h);break;default:if(!P)for(A=1,P=new Array(_-1);A<_;A++)P[A-1]=arguments[A];y[L].fn.apply(y[L].context,P)}}return!0},E.prototype.on=function(v,N,O){return C(this,v,N,O,!1)},E.prototype.once=function(v,N,O){return C(this,v,N,O,!0)},E.prototype.removeListener=function(v,N,O,h){var d=f?f+v:v;if(!this._events[d])return this;if(!N)return x(this,d),this;var g=this._events[d];if(g.fn)g.fn!==N||h&&!g.once||O&&g.context!==O||x(this,d);else{for(var S=0,P=[],L=g.length;S<L;S++)(g[S].fn!==N||h&&!g[S].once||O&&g[S].context!==O)&&P.push(g[S]);P.length?this._events[d]=P.length===1?P[0]:P:x(this,d)}return this},E.prototype.removeAllListeners=function(v){var N;return v?(N=f?f+v:v,this._events[N]&&x(this,N)):(this._events=new p,this._eventsCount=0),this},E.prototype.off=E.prototype.removeListener,E.prototype.addListener=E.prototype.on,E.prefixed=f,E.EventEmitter=E,a.exports=E},717:a=>{typeof Object.create=="function"?a.exports=function(c,f){c.super_=f,c.prototype=Object.create(f.prototype,{constructor:{value:c,enumerable:!1,writable:!0,configurable:!0}})}:a.exports=function(c,f){c.super_=f;var p=function(){};p.prototype=f.prototype,c.prototype=new p,c.prototype.constructor=c}},824:a=>{var c=1e3,f=60*c,p=60*f,w=24*p,C=7*w,x=365.25*w;function E(v,N,O,h){var d=N>=1.5*O;return Math.round(v/O)+" "+h+(d?"s":"")}a.exports=function(v,N){N=N||{};var O=typeof v;if(O==="string"&&v.length>0)return function(h){if(!((h=String(h)).length>100)){var d=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(h);if(d){var g=parseFloat(d[1]);switch((d[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return g*x;case"weeks":case"week":case"w":return g*C;case"days":case"day":case"d":return g*w;case"hours":case"hour":case"hrs":case"hr":case"h":return g*p;case"minutes":case"minute":case"mins":case"min":case"m":return g*f;case"seconds":case"second":case"secs":case"sec":case"s":return g*c;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return g;default:return}}}}(v);if(O==="number"&&isFinite(v))return N.long?function(h){var d=Math.abs(h);return d>=w?E(h,d,w,"day"):d>=p?E(h,d,p,"hour"):d>=f?E(h,d,f,"minute"):d>=c?E(h,d,c,"second"):h+" ms"}(v):function(h){var d=Math.abs(h);return d>=w?Math.round(h/w)+"d":d>=p?Math.round(h/p)+"h":d>=f?Math.round(h/f)+"m":d>=c?Math.round(h/c)+"s":h+"ms"}(v);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(v))}},520:(a,c,f)=>{var p=f(155),w=p.platform==="win32",C=f(539);function x(y,_){for(var A=[],j=0;j<y.length;j++){var G=y[j];G&&G!=="."&&(G===".."?A.length&&A[A.length-1]!==".."?A.pop():_&&A.push(".."):A.push(G))}return A}function E(y){for(var _=y.length-1,A=0;A<=_&&!y[A];A++);for(var j=_;j>=0&&!y[j];j--);return A===0&&j===_?y:A>j?[]:y.slice(A,j+1)}var v=/^([a-zA-Z]:|[\\\/]{2}[^\\\/]+[\\\/]+[^\\\/]+)?([\\\/])?([\s\S]*?)$/,N=/^([\s\S]*?)((?:\.{1,2}|[^\\\/]+?|)(\.[^.\/\\]*|))(?:[\\\/]*)$/,O={};function h(y){var _=v.exec(y),A=(_[1]||"")+(_[2]||""),j=_[3]||"",G=N.exec(j);return[A,G[1],G[2],G[3]]}function d(y){var _=v.exec(y),A=_[1]||"",j=!!A&&A[1]!==":";return{device:A,isUnc:j,isAbsolute:j||!!_[2],tail:_[3]}}function g(y){return"\\\\"+y.replace(/^[\\\/]+/,"").replace(/[\\\/]+/g,"\\")}O.resolve=function(){for(var y="",_="",A=!1,j=arguments.length-1;j>=-1;j--){var G;if(j>=0?G=arguments[j]:y?(G=p.env["="+y])&&G.substr(0,3).toLowerCase()===y.toLowerCase()+"\\"||(G=y+"\\"):G=p.cwd(),!C.isString(G))throw new TypeError("Arguments to path.resolve must be strings");if(G){var ce=d(G),de=ce.device,te=ce.isUnc,Te=ce.isAbsolute,Ne=ce.tail;if((!de||!y||de.toLowerCase()===y.toLowerCase())&&(y||(y=de),A||(_=Ne+"\\"+_,A=Te),y&&A))break}}return te&&(y=g(y)),y+(A?"\\":"")+(_=x(_.split(/[\\\/]+/),!A).join("\\"))||"."},O.normalize=function(y){var _=d(y),A=_.device,j=_.isUnc,G=_.isAbsolute,ce=_.tail,de=/[\\\/]$/.test(ce);return(ce=x(ce.split(/[\\\/]+/),!G).join("\\"))||G||(ce="."),ce&&de&&(ce+="\\"),j&&(A=g(A)),A+(G?"\\":"")+ce},O.isAbsolute=function(y){return d(y).isAbsolute},O.join=function(){for(var y=[],_=0;_<arguments.length;_++){var A=arguments[_];if(!C.isString(A))throw new TypeError("Arguments to path.join must be strings");A&&y.push(A)}var j=y.join("\\");return/^[\\\/]{2}[^\\\/]/.test(y[0])||(j=j.replace(/^[\\\/]{2,}/,"\\")),O.normalize(j)},O.relative=function(y,_){y=O.resolve(y),_=O.resolve(_);for(var A=y.toLowerCase(),j=_.toLowerCase(),G=E(_.split("\\")),ce=E(A.split("\\")),de=E(j.split("\\")),te=Math.min(ce.length,de.length),Te=te,Ne=0;Ne<te;Ne++)if(ce[Ne]!==de[Ne]){Te=Ne;break}if(Te==0)return _;var Q=[];for(Ne=Te;Ne<ce.length;Ne++)Q.push("..");return(Q=Q.concat(G.slice(Te))).join("\\")},O._makeLong=function(y){if(!C.isString(y))return y;if(!y)return"";var _=O.resolve(y);return/^[a-zA-Z]\:\\/.test(_)?"\\\\?\\"+_:/^\\\\[^?.]/.test(_)?"\\\\?\\UNC\\"+_.substring(2):y},O.dirname=function(y){var _=h(y),A=_[0],j=_[1];return A||j?(j&&(j=j.substr(0,j.length-1)),A+j):"."},O.basename=function(y,_){var A=h(y)[2];return _&&A.substr(-1*_.length)===_&&(A=A.substr(0,A.length-_.length)),A},O.extname=function(y){return h(y)[3]},O.format=function(y){if(!C.isObject(y))throw new TypeError("Parameter 'pathObject' must be an object, not "+typeof y);var _=y.root||"";if(!C.isString(_))throw new TypeError("'pathObject.root' must be a string or undefined, not "+typeof y.root);var A=y.dir,j=y.base||"";return A?A[A.length-1]===O.sep?A+j:A+O.sep+j:j},O.parse=function(y){if(!C.isString(y))throw new TypeError("Parameter 'pathString' must be a string, not "+typeof y);var _=h(y);if(!_||_.length!==4)throw new TypeError("Invalid path '"+y+"'");return{root:_[0],dir:_[0]+_[1].slice(0,-1),base:_[2],ext:_[3],name:_[2].slice(0,_[2].length-_[3].length)}},O.sep="\\",O.delimiter=";";var S=/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/,P={};function L(y){return S.exec(y).slice(1)}P.resolve=function(){for(var y="",_=!1,A=arguments.length-1;A>=-1&&!_;A--){var j=A>=0?arguments[A]:p.cwd();if(!C.isString(j))throw new TypeError("Arguments to path.resolve must be strings");j&&(y=j+"/"+y,_=j[0]==="/")}return(_?"/":"")+(y=x(y.split("/"),!_).join("/"))||"."},P.normalize=function(y){var _=P.isAbsolute(y),A=y&&y[y.length-1]==="/";return(y=x(y.split("/"),!_).join("/"))||_||(y="."),y&&A&&(y+="/"),(_?"/":"")+y},P.isAbsolute=function(y){return y.charAt(0)==="/"},P.join=function(){for(var y="",_=0;_<arguments.length;_++){var A=arguments[_];if(!C.isString(A))throw new TypeError("Arguments to path.join must be strings");A&&(y+=y?"/"+A:A)}return P.normalize(y)},P.relative=function(y,_){y=P.resolve(y).substr(1),_=P.resolve(_).substr(1);for(var A=E(y.split("/")),j=E(_.split("/")),G=Math.min(A.length,j.length),ce=G,de=0;de<G;de++)if(A[de]!==j[de]){ce=de;break}var te=[];for(de=ce;de<A.length;de++)te.push("..");return(te=te.concat(j.slice(ce))).join("/")},P._makeLong=function(y){return y},P.dirname=function(y){var _=L(y),A=_[0],j=_[1];return A||j?(j&&(j=j.substr(0,j.length-1)),A+j):"."},P.basename=function(y,_){var A=L(y)[2];return _&&A.substr(-1*_.length)===_&&(A=A.substr(0,A.length-_.length)),A},P.extname=function(y){return L(y)[3]},P.format=function(y){if(!C.isObject(y))throw new TypeError("Parameter 'pathObject' must be an object, not "+typeof y);var _=y.root||"";if(!C.isString(_))throw new TypeError("'pathObject.root' must be a string or undefined, not "+typeof y.root);return(y.dir?y.dir+P.sep:"")+(y.base||"")},P.parse=function(y){if(!C.isString(y))throw new TypeError("Parameter 'pathString' must be a string, not "+typeof y);var _=L(y);if(!_||_.length!==4)throw new TypeError("Invalid path '"+y+"'");return _[1]=_[1]||"",_[2]=_[2]||"",_[3]=_[3]||"",{root:_[0],dir:_[0]+_[1].slice(0,-1),base:_[2],ext:_[3],name:_[2].slice(0,_[2].length-_[3].length)}},P.sep="/",P.delimiter=":",a.exports=w?O:P,a.exports.posix=P,a.exports.win32=O},155:a=>{var c,f,p=a.exports={};function w(){throw new Error("setTimeout has not been defined")}function C(){throw new Error("clearTimeout has not been defined")}function x(P){if(c===setTimeout)return setTimeout(P,0);if((c===w||!c)&&setTimeout)return c=setTimeout,setTimeout(P,0);try{return c(P,0)}catch{try{return c.call(null,P,0)}catch{return c.call(this,P,0)}}}(function(){try{c=typeof setTimeout=="function"?setTimeout:w}catch{c=w}try{f=typeof clearTimeout=="function"?clearTimeout:C}catch{f=C}})();var E,v=[],N=!1,O=-1;function h(){N&&E&&(N=!1,E.length?v=E.concat(v):O=-1,v.length&&d())}function d(){if(!N){var P=x(h);N=!0;for(var L=v.length;L;){for(E=v,v=[];++O<L;)E&&E[O].run();O=-1,L=v.length}E=null,N=!1,function(y){if(f===clearTimeout)return clearTimeout(y);if((f===C||!f)&&clearTimeout)return f=clearTimeout,clearTimeout(y);try{f(y)}catch{try{return f.call(null,y)}catch{return f.call(this,y)}}}(P)}}function g(P,L){this.fun=P,this.array=L}function S(){}p.nextTick=function(P){var L=new Array(arguments.length-1);if(arguments.length>1)for(var y=1;y<arguments.length;y++)L[y-1]=arguments[y];v.push(new g(P,L)),v.length!==1||N||x(d)},g.prototype.run=function(){this.fun.apply(null,this.array)},p.title="browser",p.browser=!0,p.env={},p.argv=[],p.version="",p.versions={},p.on=S,p.addListener=S,p.once=S,p.off=S,p.removeListener=S,p.removeAllListeners=S,p.emit=S,p.prependListener=S,p.prependOnceListener=S,p.listeners=function(P){return[]},p.binding=function(P){throw new Error("process.binding is not supported")},p.cwd=function(){return"/"},p.chdir=function(P){throw new Error("process.chdir is not supported")},p.umask=function(){return 0}},384:a=>{a.exports=function(c){return c&&typeof c=="object"&&typeof c.copy=="function"&&typeof c.fill=="function"&&typeof c.readUInt8=="function"}},539:(a,c,f)=>{var p=f(155),w=/%[sdj%]/g;c.format=function(T){if(!y(T)){for(var M=[],$=0;$<arguments.length;$++)M.push(E(arguments[$]));return M.join(" ")}$=1;for(var ee=arguments,ve=ee.length,B=String(T).replace(w,function(fe){if(fe==="%%")return"%";if($>=ve)return fe;switch(fe){case"%s":return String(ee[$++]);case"%d":return Number(ee[$++]);case"%j":try{return JSON.stringify(ee[$++])}catch{return"[Circular]"}default:return fe}}),oe=ee[$];$<ve;oe=ee[++$])P(oe)||!j(oe)?B+=" "+oe:B+=" "+E(oe);return B},c.deprecate=function(T,M){if(_(f.g.process))return function(){return c.deprecate(T,M).apply(this,arguments)};if(p.noDeprecation===!0)return T;var $=!1;return function(){if(!$){if(p.throwDeprecation)throw new Error(M);p.traceDeprecation?console.trace(M):console.error(M),$=!0}return T.apply(this,arguments)}};var C,x={};function E(T,M){var $={seen:[],stylize:N};return arguments.length>=3&&($.depth=arguments[2]),arguments.length>=4&&($.colors=arguments[3]),S(M)?$.showHidden=M:M&&c._extend($,M),_($.showHidden)&&($.showHidden=!1),_($.depth)&&($.depth=2),_($.colors)&&($.colors=!1),_($.customInspect)&&($.customInspect=!0),$.colors&&($.stylize=v),O($,T,$.depth)}function v(T,M){var $=E.styles[M];return $?"\x1B["+E.colors[$][0]+"m"+T+"\x1B["+E.colors[$][1]+"m":T}function N(T,M){return T}function O(T,M,$){if(T.customInspect&&M&&de(M.inspect)&&M.inspect!==c.inspect&&(!M.constructor||M.constructor.prototype!==M)){var ee=M.inspect($,T);return y(ee)||(ee=O(T,ee,$)),ee}var ve=function(we,me){if(_(me))return we.stylize("undefined","undefined");if(y(me)){var Ue="'"+JSON.stringify(me).replace(/^"|"$/g,"").replace(/'/g,"\\'").replace(/\\"/g,'"')+"'";return we.stylize(Ue,"string")}if(L(me))return we.stylize(""+me,"number");if(S(me))return we.stylize(""+me,"boolean");if(P(me))return we.stylize("null","null")}(T,M);if(ve)return ve;var B=Object.keys(M),oe=function(we){var me={};return we.forEach(function(Ue,Ge){me[Ue]=!0}),me}(B);if(T.showHidden&&(B=Object.getOwnPropertyNames(M)),ce(M)&&(B.indexOf("message")>=0||B.indexOf("description")>=0))return h(M);if(B.length===0){if(de(M)){var fe=M.name?": "+M.name:"";return T.stylize("[Function"+fe+"]","special")}if(A(M))return T.stylize(RegExp.prototype.toString.call(M),"regexp");if(G(M))return T.stylize(Date.prototype.toString.call(M),"date");if(ce(M))return h(M)}var Re,qe="",Y=!1,ie=["{","}"];return g(M)&&(Y=!0,ie=["[","]"]),de(M)&&(qe=" [Function"+(M.name?": "+M.name:"")+"]"),A(M)&&(qe=" "+RegExp.prototype.toString.call(M)),G(M)&&(qe=" "+Date.prototype.toUTCString.call(M)),ce(M)&&(qe=" "+h(M)),B.length!==0||Y&&M.length!=0?$<0?A(M)?T.stylize(RegExp.prototype.toString.call(M),"regexp"):T.stylize("[Object]","special"):(T.seen.push(M),Re=Y?function(we,me,Ue,Ge,jt){for(var ln=[],vt=0,dt=me.length;vt<dt;++vt)F(me,String(vt))?ln.push(d(we,me,Ue,Ge,String(vt),!0)):ln.push("");return jt.forEach(function(Jn){Jn.match(/^\d+$/)||ln.push(d(we,me,Ue,Ge,Jn,!0))}),ln}(T,M,$,oe,B):B.map(function(we){return d(T,M,$,oe,we,Y)}),T.seen.pop(),function(we,me,Ue){return we.reduce(function(Ge,jt){return jt.indexOf(`
`)>=0,Ge+jt.replace(/\u001b\[\d\d?m/g,"").length+1},0)>60?Ue[0]+(me===""?"":me+`
 `)+" "+we.join(`,
  `)+" "+Ue[1]:Ue[0]+me+" "+we.join(", ")+" "+Ue[1]}(Re,qe,ie)):ie[0]+qe+ie[1]}function h(T){return"["+Error.prototype.toString.call(T)+"]"}function d(T,M,$,ee,ve,B){var oe,fe,Re;if((Re=Object.getOwnPropertyDescriptor(M,ve)||{value:M[ve]}).get?fe=Re.set?T.stylize("[Getter/Setter]","special"):T.stylize("[Getter]","special"):Re.set&&(fe=T.stylize("[Setter]","special")),F(ee,ve)||(oe="["+ve+"]"),fe||(T.seen.indexOf(Re.value)<0?(fe=P($)?O(T,Re.value,null):O(T,Re.value,$-1)).indexOf(`
`)>-1&&(fe=B?fe.split(`
`).map(function(qe){return"  "+qe}).join(`
`).substr(2):`
`+fe.split(`
`).map(function(qe){return"   "+qe}).join(`
`)):fe=T.stylize("[Circular]","special")),_(oe)){if(B&&ve.match(/^\d+$/))return fe;(oe=JSON.stringify(""+ve)).match(/^"([a-zA-Z_][a-zA-Z_0-9]*)"$/)?(oe=oe.substr(1,oe.length-2),oe=T.stylize(oe,"name")):(oe=oe.replace(/'/g,"\\'").replace(/\\"/g,'"').replace(/(^"|"$)/g,"'"),oe=T.stylize(oe,"string"))}return oe+": "+fe}function g(T){return Array.isArray(T)}function S(T){return typeof T=="boolean"}function P(T){return T===null}function L(T){return typeof T=="number"}function y(T){return typeof T=="string"}function _(T){return T===void 0}function A(T){return j(T)&&te(T)==="[object RegExp]"}function j(T){return typeof T=="object"&&T!==null}function G(T){return j(T)&&te(T)==="[object Date]"}function ce(T){return j(T)&&(te(T)==="[object Error]"||T instanceof Error)}function de(T){return typeof T=="function"}function te(T){return Object.prototype.toString.call(T)}function Te(T){return T<10?"0"+T.toString(10):T.toString(10)}c.debuglog=function(T){if(_(C)&&(C=p.env.NODE_DEBUG||""),T=T.toUpperCase(),!x[T])if(new RegExp("\\b"+T+"\\b","i").test(C)){var M=p.pid;x[T]=function(){var $=c.format.apply(c,arguments);console.error("%s %d: %s",T,M,$)}}else x[T]=function(){};return x[T]},c.inspect=E,E.colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]},E.styles={special:"cyan",number:"yellow",boolean:"yellow",undefined:"grey",null:"bold",string:"green",date:"magenta",regexp:"red"},c.isArray=g,c.isBoolean=S,c.isNull=P,c.isNullOrUndefined=function(T){return T==null},c.isNumber=L,c.isString=y,c.isSymbol=function(T){return typeof T=="symbol"},c.isUndefined=_,c.isRegExp=A,c.isObject=j,c.isDate=G,c.isError=ce,c.isFunction=de,c.isPrimitive=function(T){return T===null||typeof T=="boolean"||typeof T=="number"||typeof T=="string"||typeof T=="symbol"||T===void 0},c.isBuffer=f(384);var Ne=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function Q(){var T=new Date,M=[Te(T.getHours()),Te(T.getMinutes()),Te(T.getSeconds())].join(":");return[T.getDate(),Ne[T.getMonth()],M].join(" ")}function F(T,M){return Object.prototype.hasOwnProperty.call(T,M)}c.log=function(){console.log("%s - %s",Q(),c.format.apply(c,arguments))},c.inherits=f(717),c._extend=function(T,M){if(!M||!j(M))return T;for(var $=Object.keys(M),ee=$.length;ee--;)T[$[ee]]=M[$[ee]];return T}}},r={};function o(a){var c=r[a];if(c!==void 0)return c.exports;var f=r[a]={exports:{}};return n[a].call(f.exports,f,f.exports,o),f.exports}o.n=a=>{var c=a&&a.__esModule?()=>a.default:()=>a;return o.d(c,{a:c}),c},o.d=(a,c)=>{for(var f in c)o.o(c,f)&&!o.o(a,f)&&Object.defineProperty(a,f,{enumerable:!0,get:c[f]})},o.g=function(){if(typeof globalThis=="object")return globalThis;try{return this||new Function("return this")()}catch{if(typeof window=="object")return window}}(),o.o=(a,c)=>Object.prototype.hasOwnProperty.call(a,c),o.r=a=>{typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(a,"__esModule",{value:!0})};var i={};return(()=>{o.r(i),o.d(i,{LSPluginUser:()=>Ls,setupPluginUserInstance:()=>vc});var a=o(520),c=(o(856),o(996)),f=o.n(c),p=function(){return p=Object.assign||function(s){for(var l,u=1,m=arguments.length;u<m;u++)for(var b in l=arguments[u])Object.prototype.hasOwnProperty.call(l,b)&&(s[b]=l[b]);return s},p.apply(this,arguments)};function w(s){return s.toLowerCase()}var C=[/([a-z0-9])([A-Z])/g,/([A-Z])([A-Z][a-z])/g],x=/[^A-Z0-9]+/gi;function E(s,l,u){return l instanceof RegExp?s.replace(l,u):l.reduce(function(m,b){return m.replace(b,u)},s)}function v(s,l){return l===void 0&&(l={}),function(u,m){m===void 0&&(m={});for(var b=m.splitRegexp,I=b===void 0?C:b,R=m.stripRegexp,z=R===void 0?x:R,V=m.transform,J=V===void 0?w:V,ne=m.delimiter,Z=ne===void 0?" ":ne,le=E(E(u,I,"$1\0$2"),z,"\0"),ge=0,ue=le.length;le.charAt(ge)==="\0";)ge++;for(;le.charAt(ue-1)==="\0";)ue--;return le.slice(ge,ue).split("\0").map(J).join(Z)}(s,p({delimiter:"."},l))}var N=o(729),O=o.n(N);function h(s,l,u){return l in s?Object.defineProperty(s,l,{value:u,enumerable:!0,configurable:!0,writable:!0}):s[l]=u,s}const d=navigator.platform.toLowerCase()==="win32"?a.win32:a.posix,g=function(s,l){return l===void 0&&(l={}),v(s,p({delimiter:"_"},l))};class S extends O(){constructor(l,u){super(),h(this,"_tag",void 0),h(this,"_opts",void 0),h(this,"_logs",[]),this._tag=l,this._opts=u}write(l,u,m){var b;u!=null&&u.length&&u[u.length-1]===!0&&(m=!0,u.pop());const I=u.reduce((z,V)=>(V&&V instanceof Error?z+=`${V.message} ${V.stack}`:z+=V.toString(),z),`[${this._tag}][${new Date().toLocaleTimeString()}] `);var R;this._logs.push([l,I]),(m||(b=this._opts)!==null&&b!==void 0&&b.console)&&((R=console)===null||R===void 0||R[l==="ERROR"?"error":"debug"](`${l}: ${I}`)),this.emit("change")}clear(){this._logs=[],this.emit("change")}info(...l){this.write("INFO",l)}error(...l){this.write("ERROR",l)}warn(...l){this.write("WARN",l)}setTag(l){this._tag=l}toJSON(){return this._logs}}function P(s,...l){try{const u=new URL(s);if(!u.origin)throw new Error(null);const m=d.join(s.substr(u.origin.length),...l);return u.origin+m}catch{return d.join(s,...l)}}function L(s,l){let u,m,b=!1;const I=z=>V=>{s&&clearTimeout(s),z(V),b=!0},R=new Promise((z,V)=>{u=I(z),m=I(V),s&&(s=setTimeout(()=>m(new Error(`[deferred timeout] ${l}`)),s))});return{created:Date.now(),setTag:z=>l=z,resolve:u,reject:m,promise:R,get settled(){return b}}}const y=new Map;window.__injectedUIEffects=y;var _=o(227),A=o.n(_);function j(s,l,u){return l in s?Object.defineProperty(s,l,{value:u,enumerable:!0,configurable:!0,writable:!0}):s[l]=u,s}const G="application/x-postmate-v1+json";let ce=0;const de={handshake:1,"handshake-reply":1,call:1,emit:1,reply:1,request:1},te=(s,l)=>(typeof l!="string"||s.origin===l)&&!!s.data&&(typeof s.data!="object"||"postmate"in s.data)&&s.data.type===G&&!!de[s.data.postmate];class Te{constructor(l){j(this,"parent",void 0),j(this,"frame",void 0),j(this,"child",void 0),j(this,"events",{}),j(this,"childOrigin",void 0),j(this,"listener",void 0),this.parent=l.parent,this.frame=l.frame,this.child=l.child,this.childOrigin=l.childOrigin,this.listener=u=>{if(!te(u,this.childOrigin))return!1;const{data:m,name:b}=((u||{}).data||{}).value||{};u.data.postmate==="emit"&&b in this.events&&this.events[b].forEach(I=>{I.call(this,m)})},this.parent.addEventListener("message",this.listener,!1)}get(l,...u){return new Promise((m,b)=>{const I=++ce,R=z=>{z.data.uid===I&&z.data.postmate==="reply"&&(this.parent.removeEventListener("message",R,!1),z.data.error?b(z.data.error):m(z.data.value))};this.parent.addEventListener("message",R,!1),this.child.postMessage({postmate:"request",type:G,property:l,args:u,uid:I},this.childOrigin)})}call(l,u){this.child.postMessage({postmate:"call",type:G,property:l,data:u},this.childOrigin)}on(l,u){this.events[l]||(this.events[l]=[]),this.events[l].push(u)}destroy(){window.removeEventListener("message",this.listener,!1),this.frame.parentNode.removeChild(this.frame)}}class Ne{constructor(l){j(this,"model",void 0),j(this,"parent",void 0),j(this,"parentOrigin",void 0),j(this,"child",void 0),this.model=l.model,this.parent=l.parent,this.parentOrigin=l.parentOrigin,this.child=l.child,this.child.addEventListener("message",u=>{if(!te(u,this.parentOrigin))return;const{property:m,uid:b,data:I,args:R}=u.data;u.data.postmate!=="call"?((z,V,J)=>{const ne=typeof z[V]=="function"?z[V].apply(null,J):z[V];return Promise.resolve(ne)})(this.model,m,R).then(z=>{u.source.postMessage({property:m,postmate:"reply",type:G,uid:b,value:z},u.origin)}).catch(z=>{u.source.postMessage({property:m,postmate:"reply",type:G,uid:b,error:z},u.origin)}):m in this.model&&typeof this.model[m]=="function"&&this.model[m](I)})}emit(l,u){this.parent.postMessage({postmate:"emit",type:G,value:{name:l,data:u}},this.parentOrigin)}}class Q{constructor(l){j(this,"container",void 0),j(this,"parent",void 0),j(this,"frame",void 0),j(this,"child",void 0),j(this,"childOrigin",void 0),j(this,"url",void 0),j(this,"model",void 0),this.container=l.container,this.url=l.url,this.parent=window,this.frame=document.createElement("iframe"),l.id&&(this.frame.id=l.id),l.name&&(this.frame.name=l.name),this.frame.classList.add.apply(this.frame.classList,l.classListArray||[]),this.container.appendChild(this.frame),this.child=this.frame.contentWindow,this.model=l.model||{}}sendHandshake(l){const u=(I=>{const R=document.createElement("a");R.href=I;const z=R.protocol.length>4?R.protocol:window.location.protocol,V=R.host.length?R.port==="80"||R.port==="443"?R.hostname:R.host:window.location.host;return R.origin||`${z}//${V}`})(l=l||this.url);let m,b=0;return new Promise((I,R)=>{const z=J=>!!te(J,u)&&(J.data.postmate==="handshake-reply"?(clearInterval(m),this.parent.removeEventListener("message",z,!1),this.childOrigin=J.origin,I(new Te(this))):R("Failed handshake"));this.parent.addEventListener("message",z,!1);const V=()=>{b++,this.child.postMessage({postmate:"handshake",type:G,model:this.model},u),b===5&&clearInterval(m)};this.frame.addEventListener("load",()=>{V(),m=setInterval(V,500)}),this.frame.src=l})}destroy(){this.frame.parentNode.removeChild(this.frame)}}j(Q,"debug",!1),j(Q,"Model",void 0);class F{constructor(l){j(this,"child",void 0),j(this,"model",void 0),j(this,"parent",void 0),j(this,"parentOrigin",void 0),this.child=window,this.model=l,this.parent=this.child.parent}sendHandshakeReply(){return new Promise((l,u)=>{const m=b=>{if(b.data.postmate){if(b.data.postmate==="handshake"){this.child.removeEventListener("message",m,!1),b.source.postMessage({postmate:"handshake-reply",type:G},b.origin),this.parentOrigin=b.origin;const I=b.data.model;return I&&Object.keys(I).forEach(R=>{this.model[R]=I[R]}),l(new Ne(this))}return u("Handshake Reply Failed")}};this.child.addEventListener("message",m,!1)})}}function T(s,l,u){return l in s?Object.defineProperty(s,l,{value:u,enumerable:!0,configurable:!0,writable:!0}):s[l]=u,s}const{importHTML:M,createSandboxContainer:$}=window.QSandbox||{};function ee(s,l){return s.startsWith("http")?fetch(s,l):(s=s.replace("file://",""),new Promise(async(u,m)=>{try{const b=await window.apis.doAction(["readFile",s]);u({text:()=>b})}catch(b){console.error(b),m(b)}}))}class ve extends O(){constructor(l){super(),T(this,"_pluginLocal",void 0),T(this,"_frame",void 0),T(this,"_root",void 0),T(this,"_loaded",!1),T(this,"_unmountFns",[]),this._pluginLocal=l,l._dispose(()=>{this._unmount()})}async load(){const{name:l,entry:u}=this._pluginLocal.options;if(this.loaded||!u)return;const{template:m,execScripts:b}=await M(u,{fetch:ee});this._mount(m,document.body);const I=$(l,{elementGetter:()=>{var z;return(z=this._root)===null||z===void 0?void 0:z.firstChild}}).instance.proxy;I.__shadow_mode__=!0,I.LSPluginLocal=this._pluginLocal,I.LSPluginShadow=this,I.LSPluginUser=I.logseq=new Ls(this._pluginLocal.toJSON(),this._pluginLocal.caller);const R=await b(I,!0);this._unmountFns.push(R.unmount),this._loaded=!0}_mount(l,u){const m=this._frame=document.createElement("div");m.classList.add("lsp-shadow-sandbox"),m.id=this._pluginLocal.id,this._root=m.attachShadow({mode:"open"}),this._root.innerHTML=`<div>${l}</div>`,u.appendChild(m),this.emit("mounted")}_unmount(){for(const l of this._unmountFns)l&&l.call(null)}destroy(){var l,u;(l=this.frame)===null||l===void 0||(u=l.parentNode)===null||u===void 0||u.removeChild(this.frame)}get loaded(){return this._loaded}get document(){var l;return(l=this._root)===null||l===void 0?void 0:l.firstChild}get frame(){return this._frame}}function B(s,l,u){return l in s?Object.defineProperty(s,l,{value:u,enumerable:!0,configurable:!0,writable:!0}):s[l]=u,s}const oe=A()("LSPlugin:caller"),fe="#await#response#",Re="#lspmsg#",qe="#lspmsg#error#",Y=s=>`#lspmsg#${s}`;class ie extends O(){constructor(l){super(),B(this,"_pluginLocal",void 0),B(this,"_connected",!1),B(this,"_parent",void 0),B(this,"_child",void 0),B(this,"_shadow",void 0),B(this,"_status",void 0),B(this,"_userModel",{}),B(this,"_call",void 0),B(this,"_callUserModel",void 0),B(this,"_debugTag",""),this._pluginLocal=l,l&&(this._debugTag=l.debugTag)}async connectToChild(){if(this._connected)return;const{shadow:l}=this._pluginLocal;l?await this._setupShadowSandbox():await this._setupIframeSandbox()}async connectToParent(l={}){if(this._connected)return;const u=this,m=this._pluginLocal!=null;let b=0;const I=new Map,R=L(6e4),z=this._extendUserModel({"#lspmsg#ready#":async ne=>{z[Y(ne?.pid)]=({type:Z,payload:le})=>{oe(`[host (_call) -> *user] ${this._debugTag}`,Z,le),u.emit(Z,le)},await R.resolve()},"#lspmsg#beforeunload#":async ne=>{const Z=L(1e4);u.emit("beforeunload",Object.assign({actor:Z},ne)),await Z.promise},"#lspmsg#settings#":async({type:ne,payload:Z})=>{u.emit("settings:changed",Z)},[Re]:async({ns:ne,type:Z,payload:le})=>{oe(`[host (async) -> *user] ${this._debugTag} ns=${ne} type=${Z}`,le),ne&&ne.startsWith("hook")?u.emit(`${ne}:${Z}`,le):u.emit(Z,le)},"#lspmsg#reply#":({_sync:ne,result:Z})=>{if(oe(`[sync host -> *user] #${ne}`,Z),I.has(ne)){const le=I.get(ne);le&&(Z!=null&&Z.hasOwnProperty(qe)?le.reject(Z[qe]):le.resolve(Z),I.delete(ne))}},...l});var V;if(m)return await R.promise,JSON.parse(JSON.stringify((V=this._pluginLocal)===null||V===void 0?void 0:V.toJSON()));const J=new F(z).sendHandshakeReply();return this._status="pending",await J.then(ne=>{this._child=ne,this._connected=!0,this._call=async(Z,le={},ge)=>{if(ge){const ue=++b;I.set(ue,ge),le._sync=ue,ge.setTag(`async call #${ue}`),oe(`async call #${ue}`)}return ne.emit(Y(z.baseInfo.id),{type:Z,payload:le}),ge?.promise},this._callUserModel=async(Z,le)=>{try{z[Z](le)}catch{oe(`[model method] #${Z} not existed`)}},setInterval(()=>{if(I.size>100)for(const[Z,le]of I)le.settled&&I.delete(Z)},18e5)}).finally(()=>{this._status=void 0}),await R.promise,z.baseInfo}async call(l,u={}){var m;return(m=this._call)===null||m===void 0?void 0:m.call(this,l,u)}async callAsync(l,u={}){var m;const b=L(1e4);return(m=this._call)===null||m===void 0?void 0:m.call(this,l,u,b)}async callUserModel(l,...u){var m;return(m=this._callUserModel)===null||m===void 0?void 0:m.apply(this,[l,...u])}async callUserModelAsync(l,...u){var m;return l=`${fe}${l}`,(m=this._callUserModel)===null||m===void 0?void 0:m.apply(this,[l,...u])}async _setupIframeSandbox(){const l=this._pluginLocal,u=l.id,m=`${u}_lsp_main`,b=new URL(l.options.entry);b.searchParams.set("__v__",l.options.version);const I=document.querySelector(`#${m}`);I&&I.parentElement.removeChild(I);const R=document.createElement("div");R.classList.add("lsp-iframe-sandbox-container"),R.id=m,R.dataset.pid=u;try{var z;const Z=(z=await this._pluginLocal._loadLayoutsData())===null||z===void 0?void 0:z.$$0;if(Z){R.dataset.inited_layout="true";let{width:le,height:ge,left:ue,top:Se,vw:xe,vh:We}=Z;ue=Math.max(ue,0),ue=typeof xe=="number"?`${Math.min(100*ue/xe,99)}%`:`${ue}px`,Se=Math.max(Se,45),Se=typeof We=="number"?`${Math.min(100*Se/We,99)}%`:`${Se}px`,Object.assign(R.style,{width:le+"px",height:ge+"px",left:ue,top:Se})}}catch(Z){console.error("[Restore Layout Error]",Z)}document.body.appendChild(R);const V=new Q({id:u+"_iframe",container:R,url:b.href,classListArray:["lsp-iframe-sandbox"],model:{baseInfo:JSON.parse(JSON.stringify(l.toJSON()))}});let J,ne=V.sendHandshake();return this._status="pending",new Promise((Z,le)=>{J=setTimeout(()=>{le(new Error("handshake Timeout")),V.destroy()},4e3),ne.then(ge=>{this._parent=ge,this._connected=!0,this.emit("connected"),ge.on(Y(l.id),({type:ue,payload:Se})=>{var xe,We;oe("[user -> *host] ",ue,Se),(xe=this._pluginLocal)===null||xe===void 0||xe.emit(ue,Se||{}),(We=this._pluginLocal)===null||We===void 0||We.caller.emit(ue,Se||{})}),this._call=async(...ue)=>{await ge.call(Y(l.id),{type:ue[0],payload:Object.assign(ue[1]||{},{$$pid:l.id})})},this._callUserModel=async(ue,...Se)=>{if(ue.startsWith(fe))return await ge.get(ue.replace(fe,""),...Se);ge.call(ue,Se?.[0])},Z(null)}).catch(ge=>{le(ge)}).finally(()=>{clearTimeout(J)})}).catch(Z=>{throw oe("[iframe sandbox] error",Z),Z}).finally(()=>{this._status=void 0})}async _setupShadowSandbox(){const l=this._pluginLocal,u=this._shadow=new ve(l);try{this._status="pending",await u.load(),this._connected=!0,this.emit("connected"),this._call=async(m,b={},I)=>{var R;return I&&(b.actor=I),(R=this._pluginLocal)===null||R===void 0||R.emit(m,Object.assign(b,{$$pid:l.id})),I?.promise},this._callUserModel=async(...m)=>{var b;let I=m[0];(b=I)!==null&&b!==void 0&&b.startsWith(fe)&&(I=I.replace(fe,""));const R=m[1]||{},z=this._userModel[I];typeof z=="function"&&await z.call(null,R)}}catch(m){throw oe("[shadow sandbox] error",m),m}finally{this._status=void 0}}_extendUserModel(l){return Object.assign(this._userModel,l)}_getSandboxIframeContainer(){var l;return(l=this._parent)===null||l===void 0?void 0:l.frame.parentNode}_getSandboxShadowContainer(){var l;return(l=this._shadow)===null||l===void 0?void 0:l.frame.parentNode}_getSandboxIframeRoot(){var l;return(l=this._parent)===null||l===void 0?void 0:l.frame}_getSandboxShadowRoot(){var l;return(l=this._shadow)===null||l===void 0?void 0:l.frame}set debugTag(l){this._debugTag=l}async destroy(){var l;let u=null;this._parent&&(u=this._getSandboxIframeContainer(),await this._parent.destroy()),this._shadow&&(u=this._getSandboxShadowContainer(),this._shadow.destroy()),(l=u)===null||l===void 0||l.parentNode.removeChild(u)}}function we(s,l,u){return l in s?Object.defineProperty(s,l,{value:u,enumerable:!0,configurable:!0,writable:!0}):s[l]=u,s}class me{constructor(l,u){we(this,"ctx",void 0),we(this,"opts",void 0),this.ctx=l,this.opts=u}get ctxId(){return this.ctx.baseInfo.id}setItem(l,u){var m;return this.ctx.caller.callAsync("api:call",{method:"write-plugin-storage-file",args:[this.ctxId,l,u,(m=this.opts)===null||m===void 0?void 0:m.assets]})}getItem(l){var u;return this.ctx.caller.callAsync("api:call",{method:"read-plugin-storage-file",args:[this.ctxId,l,(u=this.opts)===null||u===void 0?void 0:u.assets]})}removeItem(l){var u;return this.ctx.caller.call("api:call",{method:"unlink-plugin-storage-file",args:[this.ctxId,l,(u=this.opts)===null||u===void 0?void 0:u.assets]})}allKeys(){var l;return this.ctx.caller.callAsync("api:call",{method:"list-plugin-storage-files",args:[this.ctxId,(l=this.opts)===null||l===void 0?void 0:l.assets]})}clear(){var l;return this.ctx.caller.call("api:call",{method:"clear-plugin-storage-files",args:[this.ctxId,(l=this.opts)===null||l===void 0?void 0:l.assets]})}hasItem(l){var u;return this.ctx.caller.callAsync("api:call",{method:"exist-plugin-storage-file",args:[this.ctxId,l,(u=this.opts)===null||u===void 0?void 0:u.assets]})}}class Ue{constructor(l){var u,m,b;b=void 0,(m="ctx")in(u=this)?Object.defineProperty(u,m,{value:b,enumerable:!0,configurable:!0,writable:!0}):u[m]=b,this.ctx=l}get React(){return this.ensureHostScope().React}get ReactDOM(){return this.ensureHostScope().ReactDOM}get pluginLocal(){return this.ensureHostScope().LSPluginCore.ensurePlugin(this.ctx.baseInfo.id)}invokeExperMethod(l,...u){var m,b;const I=this.ensureHostScope();return l=(m=g(l))===null||m===void 0?void 0:m.toLowerCase(),(b=I.logseq.api["exper_"+l])===null||b===void 0?void 0:b.apply(I,u)}async loadScripts(...l){(l=l.map(u=>u!=null&&u.startsWith("http")?u:this.ctx.resolveResourceFullUrl(u))).unshift(this.ctx.baseInfo.id),await this.invokeExperMethod("loadScripts",...l)}registerFencedCodeRenderer(l,u){return this.ensureHostScope().logseq.api.exper_register_fenced_code_renderer(this.ctx.baseInfo.id,l,u)}registerExtensionsEnhancer(l,u){const m=this.ensureHostScope();return l==="katex"&&m.katex&&u(m.katex).catch(console.error),m.logseq.api.exper_register_extensions_enhancer(this.ctx.baseInfo.id,l,u)}ensureHostScope(){if(window===top)throw new Error("Can not access host scope!");return top}}function Ge(s,l,u){return l in s?Object.defineProperty(s,l,{value:u,enumerable:!0,configurable:!0,writable:!0}):s[l]=u,s}const jt=s=>`task_callback_${s}`;class ln{constructor(l,u,m={}){Ge(this,"_client",void 0),Ge(this,"_requestId",void 0),Ge(this,"_requestOptions",void 0),Ge(this,"_promise",void 0),Ge(this,"_aborted",!1),this._client=l,this._requestId=u,this._requestOptions=m,this._promise=new Promise((z,V)=>{if(!this._requestId)return V(null);this._client.once(jt(this._requestId),J=>{J&&J instanceof Error?V(J):z(J)})});const{success:b,fail:I,final:R}=this._requestOptions;this._promise.then(z=>{b?.(z)}).catch(z=>{I?.(z)}).finally(()=>{R?.()})}abort(){this._requestOptions.abortable&&!this._aborted&&(this._client.ctx._execCallableAPI("http_request_abort",this._requestId),this._aborted=!0)}get promise(){return this._promise}get client(){return this._client}get requestId(){return this._requestId}}class vt extends N.EventEmitter{constructor(l){super(),Ge(this,"_ctx",void 0),this._ctx=l,this.ctx.caller.on("#lsp#request#callback",u=>{const m=u?.requestId;m&&this.emit(jt(m),u?.payload)})}static createRequestTask(l,u,m){return new ln(l,u,m)}async _request(l){const u=this.ctx.baseInfo.id,{success:m,fail:b,final:I,...R}=l,z=this.ctx.Experiments.invokeExperMethod("request",u,R),V=vt.createRequestTask(this.ctx.Request,z,l);return R.abortable?V:V.promise}get ctx(){return this._ctx}}const dt=Array.isArray,Jn=typeof $i=="object"&&$i&&$i.Object===Object&&$i;var as=typeof self=="object"&&self&&self.Object===Object&&self;const Mt=Jn||as||Function("return this")(),Gt=Mt.Symbol;var yi=Object.prototype,us=yi.hasOwnProperty,cs=yi.toString,er=Gt?Gt.toStringTag:void 0;const ds=function(s){var l=us.call(s,er),u=s[er];try{s[er]=void 0;var m=!0}catch{}var b=cs.call(s);return m&&(l?s[er]=u:delete s[er]),b};var vi=Object.prototype.toString;const W=function(s){return vi.call(s)};var K=Gt?Gt.toStringTag:void 0;const re=function(s){return s==null?s===void 0?"[object Undefined]":"[object Null]":K&&K in Object(s)?ds(s):W(s)},pe=function(s){var l=typeof s;return s!=null&&(l=="object"||l=="function")},ze=function(s){if(!pe(s))return!1;var l=re(s);return l=="[object Function]"||l=="[object GeneratorFunction]"||l=="[object AsyncFunction]"||l=="[object Proxy]"},At=Mt["__core-js_shared__"];var Yt,Sn=(Yt=/[^.]+$/.exec(At&&At.keys&&At.keys.IE_PROTO||""))?"Symbol(src)_1."+Yt:"";const po=function(s){return!!Sn&&Sn in s};var wi=Function.prototype.toString;const sn=function(s){if(s!=null){try{return wi.call(s)}catch{}try{return s+""}catch{}}return""};var fs=/^\[object .+?Constructor\]$/,ps=Function.prototype,hs=Object.prototype,Er=ps.toString,ms=hs.hasOwnProperty,gs=RegExp("^"+Er.call(ms).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");const ys=function(s){return!(!pe(s)||po(s))&&(ze(s)?gs:fs).test(sn(s))},ho=function(s,l){return s?.[l]},Rt=function(s,l){var u=ho(s,l);return ys(u)?u:void 0},zt=function(){try{var s=Rt(Object,"defineProperty");return s({},"",{}),s}catch{}}(),ki=function(s,l,u){l=="__proto__"&&zt?zt(s,l,{configurable:!0,enumerable:!0,value:u,writable:!0}):s[l]=u},Tr=function(s){return function(l,u,m){for(var b=-1,I=Object(l),R=m(l),z=R.length;z--;){var V=R[s?z:++b];if(u(I[V],V,I)===!1)break}return l}}(),mo=function(s,l){for(var u=-1,m=Array(s);++u<s;)m[u]=l(u);return m},_n=function(s){return s!=null&&typeof s=="object"},Ci=function(s){return _n(s)&&re(s)=="[object Arguments]"};var Si=Object.prototype,vs=Si.hasOwnProperty,_i=Si.propertyIsEnumerable;const wt=Ci(function(){return arguments}())?Ci:function(s){return _n(s)&&vs.call(s,"callee")&&!_i.call(s,"callee")},tr=function(){return!1};var Xt=t&&!t.nodeType&&t,Nr=Xt&&!0&&e&&!e.nodeType&&e,br=Nr&&Nr.exports===Xt?Mt.Buffer:void 0;const go=(br?br.isBuffer:void 0)||tr;var ws=/^(?:0|[1-9]\d*)$/;const xi=function(s,l){var u=typeof s;return!!(l=l??9007199254740991)&&(u=="number"||u!="symbol"&&ws.test(s))&&s>-1&&s%1==0&&s<l},Pr=function(s){return typeof s=="number"&&s>-1&&s%1==0&&s<=9007199254740991};var ke={};ke["[object Float32Array]"]=ke["[object Float64Array]"]=ke["[object Int8Array]"]=ke["[object Int16Array]"]=ke["[object Int32Array]"]=ke["[object Uint8Array]"]=ke["[object Uint8ClampedArray]"]=ke["[object Uint16Array]"]=ke["[object Uint32Array]"]=!0,ke["[object Arguments]"]=ke["[object Array]"]=ke["[object ArrayBuffer]"]=ke["[object Boolean]"]=ke["[object DataView]"]=ke["[object Date]"]=ke["[object Error]"]=ke["[object Function]"]=ke["[object Map]"]=ke["[object Number]"]=ke["[object Object]"]=ke["[object RegExp]"]=ke["[object Set]"]=ke["[object String]"]=ke["[object WeakMap]"]=!1;const $e=function(s){return _n(s)&&Pr(s.length)&&!!ke[re(s)]},Ei=function(s){return function(l){return s(l)}};var Be=t&&!t.nodeType&&t,xn=Be&&!0&&e&&!e.nodeType&&e,Oe=xn&&xn.exports===Be&&Jn.process,En=function(){try{var s=xn&&xn.require&&xn.require("util").types;return s||Oe&&Oe.binding&&Oe.binding("util")}catch{}}(),Or=En&&En.isTypedArray;const yo=Or?Ei(Or):$e;var vo=Object.prototype.hasOwnProperty;const Ti=function(s,l){var u=dt(s),m=!u&&wt(s),b=!u&&!m&&go(s),I=!u&&!m&&!b&&yo(s),R=u||m||b||I,z=R?mo(s.length,String):[],V=z.length;for(var J in s)!l&&!vo.call(s,J)||R&&(J=="length"||b&&(J=="offset"||J=="parent")||I&&(J=="buffer"||J=="byteLength"||J=="byteOffset")||xi(J,V))||z.push(J);return z};var Tn=Object.prototype;const an=function(s){var l=s&&s.constructor;return s===(typeof l=="function"&&l.prototype||Tn)},wo=function(s,l){return function(u){return s(l(u))}}(Object.keys,Object);var ko=Object.prototype.hasOwnProperty;const Nn=function(s){if(!an(s))return wo(s);var l=[];for(var u in Object(s))ko.call(s,u)&&u!="constructor"&&l.push(u);return l},Ir=function(s){return s!=null&&Pr(s.length)&&!ze(s)},bn=function(s){return Ir(s)?Ti(s):Nn(s)},Ni=function(s,l){return s&&Tr(s,l,bn)},Co=function(){this.__data__=[],this.size=0},Pn=function(s,l){return s===l||s!=s&&l!=l},Dt=function(s,l){for(var u=s.length;u--;)if(Pn(s[u][0],l))return u;return-1};var On=Array.prototype.splice;const bi=function(s){var l=this.__data__,u=Dt(l,s);return!(u<0)&&(u==l.length-1?l.pop():On.call(l,u,1),--this.size,!0)},Pi=function(s){var l=this.__data__,u=Dt(l,s);return u<0?void 0:l[u][1]},Oi=function(s){return Dt(this.__data__,s)>-1},So=function(s,l){var u=this.__data__,m=Dt(u,s);return m<0?(++this.size,u.push([s,l])):u[m][1]=l,this};function un(s){var l=-1,u=s==null?0:s.length;for(this.clear();++l<u;){var m=s[l];this.set(m[0],m[1])}}un.prototype.clear=Co,un.prototype.delete=bi,un.prototype.get=Pi,un.prototype.has=Oi,un.prototype.set=So;const In=un,_o=function(){this.__data__=new In,this.size=0},Zt=function(s){var l=this.__data__,u=l.delete(s);return this.size=l.size,u},Lr=function(s){return this.__data__.get(s)},Ii=function(s){return this.__data__.has(s)},nr=Rt(Mt,"Map"),rr=Rt(Object,"create"),Ln=function(){this.__data__=rr?rr(null):{},this.size=0},ks=function(s){var l=this.has(s)&&delete this.__data__[s];return this.size-=l?1:0,l};var Li=Object.prototype.hasOwnProperty;const xo=function(s){var l=this.__data__;if(rr){var u=l[s];return u==="__lodash_hash_undefined__"?void 0:u}return Li.call(l,s)?l[s]:void 0};var ji=Object.prototype.hasOwnProperty;const Mi=function(s){var l=this.__data__;return rr?l[s]!==void 0:ji.call(l,s)},Cs=function(s,l){var u=this.__data__;return this.size+=this.has(s)?0:1,u[s]=rr&&l===void 0?"__lodash_hash_undefined__":l,this};function Ft(s){var l=-1,u=s==null?0:s.length;for(this.clear();++l<u;){var m=s[l];this.set(m[0],m[1])}}Ft.prototype.clear=Ln,Ft.prototype.delete=ks,Ft.prototype.get=xo,Ft.prototype.has=Mi,Ft.prototype.set=Cs;const jr=Ft,Ss=function(){this.size=0,this.__data__={hash:new jr,map:new(nr||In),string:new jr}},Ut=function(s){var l=typeof s;return l=="string"||l=="number"||l=="symbol"||l=="boolean"?s!=="__proto__":s===null},or=function(s,l){var u=s.__data__;return Ut(l)?u[typeof l=="string"?"string":"hash"]:u.map},Ai=function(s){var l=or(this,s).delete(s);return this.size-=l?1:0,l},Ri=function(s){return or(this,s).get(s)},_s=function(s){return or(this,s).has(s)},ir=function(s,l){var u=or(this,s),m=u.size;return u.set(s,l),this.size+=u.size==m?0:1,this};function nt(s){var l=-1,u=s==null?0:s.length;for(this.clear();++l<u;){var m=s[l];this.set(m[0],m[1])}}nt.prototype.clear=Ss,nt.prototype.delete=Ai,nt.prototype.get=Ri,nt.prototype.has=_s,nt.prototype.set=ir;const lr=nt,zi=function(s,l){var u=this.__data__;if(u instanceof In){var m=u.__data__;if(!nr||m.length<199)return m.push([s,l]),this.size=++u.size,this;u=this.__data__=new lr(m)}return u.set(s,l),this.size=u.size,this};function cn(s){var l=this.__data__=new In(s);this.size=l.size}cn.prototype.clear=_o,cn.prototype.delete=Zt,cn.prototype.get=Lr,cn.prototype.has=Ii,cn.prototype.set=zi;const sr=cn,xs=function(s){return this.__data__.set(s,"__lodash_hash_undefined__"),this},k=function(s){return this.__data__.has(s)};function q(s){var l=-1,u=s==null?0:s.length;for(this.__data__=new lr;++l<u;)this.add(s[l])}q.prototype.add=q.prototype.push=xs,q.prototype.has=k;const H=q,Ce=function(s,l){for(var u=-1,m=s==null?0:s.length;++u<m;)if(l(s[u],u,s))return!0;return!1},Le=function(s,l){return s.has(l)},Ye=function(s,l,u,m,b,I){var R=1&u,z=s.length,V=l.length;if(z!=V&&!(R&&V>z))return!1;var J=I.get(s),ne=I.get(l);if(J&&ne)return J==l&&ne==s;var Z=-1,le=!0,ge=2&u?new H:void 0;for(I.set(s,l),I.set(l,s);++Z<z;){var ue=s[Z],Se=l[Z];if(m)var xe=R?m(Se,ue,Z,l,s,I):m(ue,Se,Z,s,l,I);if(xe!==void 0){if(xe)continue;le=!1;break}if(ge){if(!Ce(l,function(We,Tt){if(!Le(ge,Tt)&&(ue===We||b(ue,We,u,m,I)))return ge.push(Tt)})){le=!1;break}}else if(ue!==Se&&!b(ue,Se,u,m,I)){le=!1;break}}return I.delete(s),I.delete(l),le},He=Mt.Uint8Array,ar=function(s){var l=-1,u=Array(s.size);return s.forEach(function(m,b){u[++l]=[b,m]}),u},Jt=function(s){var l=-1,u=Array(s.size);return s.forEach(function(m){u[++l]=m}),u};var ft=Gt?Gt.prototype:void 0,Eo=ft?ft.valueOf:void 0;const Ku=function(s,l,u,m,b,I,R){switch(u){case"[object DataView]":if(s.byteLength!=l.byteLength||s.byteOffset!=l.byteOffset)return!1;s=s.buffer,l=l.buffer;case"[object ArrayBuffer]":return!(s.byteLength!=l.byteLength||!I(new He(s),new He(l)));case"[object Boolean]":case"[object Date]":case"[object Number]":return Pn(+s,+l);case"[object Error]":return s.name==l.name&&s.message==l.message;case"[object RegExp]":case"[object String]":return s==l+"";case"[object Map]":var z=ar;case"[object Set]":var V=1&m;if(z||(z=Jt),s.size!=l.size&&!V)return!1;var J=R.get(s);if(J)return J==l;m|=2,R.set(s,l);var ne=Ye(z(s),z(l),m,b,I,R);return R.delete(s),ne;case"[object Symbol]":if(Eo)return Eo.call(s)==Eo.call(l)}return!1},Kp=function(s,l){for(var u=-1,m=l.length,b=s.length;++u<m;)s[b+u]=l[u];return s},Gp=function(s,l,u){var m=l(s);return dt(s)?m:Kp(m,u(s))},Yp=function(s,l){for(var u=-1,m=s==null?0:s.length,b=0,I=[];++u<m;){var R=s[u];l(R,u,s)&&(I[b++]=R)}return I},Xp=function(){return[]};var Zp=Object.prototype.propertyIsEnumerable,Gu=Object.getOwnPropertySymbols;const Jp=Gu?function(s){return s==null?[]:(s=Object(s),Yp(Gu(s),function(l){return Zp.call(s,l)}))}:Xp,Yu=function(s){return Gp(s,bn,Jp)};var eh=Object.prototype.hasOwnProperty;const th=function(s,l,u,m,b,I){var R=1&u,z=Yu(s),V=z.length;if(V!=Yu(l).length&&!R)return!1;for(var J=V;J--;){var ne=z[J];if(!(R?ne in l:eh.call(l,ne)))return!1}var Z=I.get(s),le=I.get(l);if(Z&&le)return Z==l&&le==s;var ge=!0;I.set(s,l),I.set(l,s);for(var ue=R;++J<V;){var Se=s[ne=z[J]],xe=l[ne];if(m)var We=R?m(xe,Se,ne,l,s,I):m(Se,xe,ne,s,l,I);if(!(We===void 0?Se===xe||b(Se,xe,u,m,I):We)){ge=!1;break}ue||(ue=ne=="constructor")}if(ge&&!ue){var Tt=s.constructor,cr=l.constructor;Tt==cr||!("constructor"in s)||!("constructor"in l)||typeof Tt=="function"&&Tt instanceof Tt&&typeof cr=="function"&&cr instanceof cr||(ge=!1)}return I.delete(s),I.delete(l),ge},Es=Rt(Mt,"DataView"),Ts=Rt(Mt,"Promise"),Ns=Rt(Mt,"Set"),bs=Rt(Mt,"WeakMap");var Xu="[object Map]",Zu="[object Promise]",Ju="[object Set]",ec="[object WeakMap]",tc="[object DataView]",nh=sn(Es),rh=sn(nr),oh=sn(Ts),ih=sn(Ns),lh=sn(bs),ur=re;(Es&&ur(new Es(new ArrayBuffer(1)))!=tc||nr&&ur(new nr)!=Xu||Ts&&ur(Ts.resolve())!=Zu||Ns&&ur(new Ns)!=Ju||bs&&ur(new bs)!=ec)&&(ur=function(s){var l=re(s),u=l=="[object Object]"?s.constructor:void 0,m=u?sn(u):"";if(m)switch(m){case nh:return tc;case rh:return Xu;case oh:return Zu;case ih:return Ju;case lh:return ec}return l});const nc=ur;var rc="[object Arguments]",oc="[object Array]",Di="[object Object]",ic=Object.prototype.hasOwnProperty;const sh=function(s,l,u,m,b,I){var R=dt(s),z=dt(l),V=R?oc:nc(s),J=z?oc:nc(l),ne=(V=V==rc?Di:V)==Di,Z=(J=J==rc?Di:J)==Di,le=V==J;if(le&&go(s)){if(!go(l))return!1;R=!0,ne=!1}if(le&&!ne)return I||(I=new sr),R||yo(s)?Ye(s,l,u,m,b,I):Ku(s,l,V,u,m,b,I);if(!(1&u)){var ge=ne&&ic.call(s,"__wrapped__"),ue=Z&&ic.call(l,"__wrapped__");if(ge||ue){var Se=ge?s.value():s,xe=ue?l.value():l;return I||(I=new sr),b(Se,xe,u,m,I)}}return!!le&&(I||(I=new sr),th(s,l,u,m,b,I))},lc=function s(l,u,m,b,I){return l===u||(l==null||u==null||!_n(l)&&!_n(u)?l!=l&&u!=u:sh(l,u,m,b,s,I))},ah=function(s,l,u,m){var b=u.length,I=b,R=!m;if(s==null)return!I;for(s=Object(s);b--;){var z=u[b];if(R&&z[2]?z[1]!==s[z[0]]:!(z[0]in s))return!1}for(;++b<I;){var V=(z=u[b])[0],J=s[V],ne=z[1];if(R&&z[2]){if(J===void 0&&!(V in s))return!1}else{var Z=new sr;if(m)var le=m(J,ne,V,s,l,Z);if(!(le===void 0?lc(ne,J,3,m,Z):le))return!1}}return!0},sc=function(s){return s==s&&!pe(s)},uh=function(s){for(var l=bn(s),u=l.length;u--;){var m=l[u],b=s[m];l[u]=[m,b,sc(b)]}return l},ac=function(s,l){return function(u){return u!=null&&u[s]===l&&(l!==void 0||s in Object(u))}},ch=function(s){var l=uh(s);return l.length==1&&l[0][2]?ac(l[0][0],l[0][1]):function(u){return u===s||ah(u,s,l)}},Ps=function(s){return typeof s=="symbol"||_n(s)&&re(s)=="[object Symbol]"};var dh=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,fh=/^\w*$/;const Os=function(s,l){if(dt(s))return!1;var u=typeof s;return!(u!="number"&&u!="symbol"&&u!="boolean"&&s!=null&&!Ps(s))||fh.test(s)||!dh.test(s)||l!=null&&s in Object(l)};function Is(s,l){if(typeof s!="function"||l!=null&&typeof l!="function")throw new TypeError("Expected a function");var u=function(){var m=arguments,b=l?l.apply(this,m):m[0],I=u.cache;if(I.has(b))return I.get(b);var R=s.apply(this,m);return u.cache=I.set(b,R)||I,R};return u.cache=new(Is.Cache||lr),u}Is.Cache=lr;const ph=Is;var hh=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,mh=/\\(\\)?/g;const gh=function(s){var l=ph(s,function(m){return u.size===500&&u.clear(),m}),u=l.cache;return l}(function(s){var l=[];return s.charCodeAt(0)===46&&l.push(""),s.replace(hh,function(u,m,b,I){l.push(b?I.replace(mh,"$1"):m||u)}),l}),yh=function(s,l){for(var u=-1,m=s==null?0:s.length,b=Array(m);++u<m;)b[u]=l(s[u],u,s);return b};var uc=Gt?Gt.prototype:void 0,cc=uc?uc.toString:void 0;const vh=function s(l){if(typeof l=="string")return l;if(dt(l))return yh(l,s)+"";if(Ps(l))return cc?cc.call(l):"";var u=l+"";return u=="0"&&1/l==-1/0?"-0":u},wh=function(s){return s==null?"":vh(s)},dc=function(s,l){return dt(s)?s:Os(s,l)?[s]:gh(wh(s))},Fi=function(s){if(typeof s=="string"||Ps(s))return s;var l=s+"";return l=="0"&&1/s==-1/0?"-0":l},fc=function(s,l){for(var u=0,m=(l=dc(l,s)).length;s!=null&&u<m;)s=s[Fi(l[u++])];return u&&u==m?s:void 0},kh=function(s,l,u){var m=s==null?void 0:fc(s,l);return m===void 0?u:m},Ch=function(s,l){return s!=null&&l in Object(s)},Sh=function(s,l,u){for(var m=-1,b=(l=dc(l,s)).length,I=!1;++m<b;){var R=Fi(l[m]);if(!(I=s!=null&&u(s,R)))break;s=s[R]}return I||++m!=b?I:!!(b=s==null?0:s.length)&&Pr(b)&&xi(R,b)&&(dt(s)||wt(s))},_h=function(s,l){return s!=null&&Sh(s,l,Ch)},xh=function(s,l){return Os(s)&&sc(l)?ac(Fi(s),l):function(u){var m=kh(u,s);return m===void 0&&m===l?_h(u,s):lc(l,m,3)}},Eh=function(s){return s},Th=function(s){return function(l){return l?.[s]}},Nh=function(s){return function(l){return fc(l,s)}},bh=function(s){return Os(s)?Th(Fi(s)):Nh(s)},Ph=function(s){return typeof s=="function"?s:s==null?Eh:typeof s=="object"?dt(s)?xh(s[0],s[1]):ch(s):bh(s)},Oh=function(s,l){var u={};return l=Ph(l),Ni(s,function(m,b,I){ki(u,l(m,b,I),m)}),u};function pc(s,l,u){return l in s?Object.defineProperty(s,l,{value:u,enumerable:!0,configurable:!0,writable:!0}):s[l]=u,s}class Ih{constructor(l,u){pc(this,"ctx",void 0),pc(this,"serviceHooks",void 0),this.ctx=l,this.serviceHooks=u,l._execCallableAPI("register-search-service",l.baseInfo.id,u.name,u.options),Object.entries({query:{f:"onQuery",args:["graph","q",!0],reply:!0,transformOutput:m=>(dt(m?.blocks)&&(m.blocks=m.blocks.map(b=>b&&Oh(b,(I,R)=>`block/${R}`))),m)},rebuildBlocksIndice:{f:"onIndiceInit",args:["graph","blocks"]},transactBlocks:{f:"onBlocksChanged",args:["graph","data"]},truncateBlocks:{f:"onIndiceReset",args:["graph"]},removeDb:{f:"onGraph",args:["graph"]}}).forEach(([m,b])=>{const I=(R=>`service:search:${R}:${u.name}`)(m);l.caller.on(I,async R=>{if(ze(u?.[b.f])){let z=null;try{z=await u[b.f].apply(u,(b.args||[]).map(V=>{if(R){if(V===!0)return R;if(R.hasOwnProperty(V)){const J=R[V];return delete R[V],J}}})),b.transformOutput&&(z=b.transformOutput(z))}catch(V){console.error("[SearchService] ",V),z=V}finally{b.reply&&l.caller.call(`${I}:reply`,z)}}})})}}function $t(s,l,u){return l in s?Object.defineProperty(s,l,{value:u,enumerable:!0,configurable:!0,writable:!0}):s[l]=u,s}const Lh=Symbol.for("proxy-continue"),jh=A()("LSPlugin:user"),hc=new S("",{console:!0});function Mr(s,l,u){var m;const{key:b,label:I,desc:R,palette:z,keybinding:V,extras:J}=l;if(typeof u!="function")return this.logger.error(`${b||I}: command action should be function.`),!1;const ne=function(le){if(typeof le=="string")return le.trim().replace(/\s/g,"_").toLowerCase()}(b);if(!ne)return this.logger.error(`${I}: command key is required.`),!1;const Z=`SimpleCommandHook${ne}${++yc}`;this.Editor["on"+Z](u),(m=this.caller)===null||m===void 0||m.call("api:call",{method:"register-plugin-simple-command",args:[this.baseInfo.id,[{key:ne,label:I,type:s,desc:R,keybinding:V,extras:J},["editor/hook",Z]],z]})}function mc(s){return!(typeof(l=s)!="string"||l.length!==36||!/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/gi.test(l))||(hc.error(`#${s} is not a valid UUID string.`),!1);var l}let Ui=null,gc=new Map;const Mh={async getInfo(s){return Ui||(Ui=await this._execCallableAPIAsync("get-app-info")),typeof s=="string"?Ui[s]:Ui},registerCommand:Mr,registerSearchService(s){if(gc.has(s.name))throw new Error(`SearchService: #${s.name} has registered!`);gc.set(s.name,new Ih(this,s))},registerCommandPalette(s,l){const{key:u,label:m,keybinding:b}=s;return Mr.call(this,"$palette$",{key:u,label:m,palette:!0,keybinding:b},l)},registerCommandShortcut(s,l,u={}){typeof s=="string"&&(s={mode:"global",binding:s});const{binding:m}=s,b="$shortcut$",I=u.key||b+g(m?.toString());return Mr.call(this,b,{...u,key:I,palette:!1,keybinding:s},l)},registerUIItem(s,l){var u;const m=this.baseInfo.id;(u=this.caller)===null||u===void 0||u.call("api:call",{method:"register-plugin-ui-item",args:[m,s,l]})},registerPageMenuItem(s,l){if(typeof l!="function")return!1;const u=s+"_"+this.baseInfo.id,m=s;Mr.call(this,"page-menu-item",{key:u,label:m},l)},onBlockRendererSlotted(s,l){if(!mc(s))return;const u=this.baseInfo.id,m=`hook:editor:${g(`slot:${s}`)}`;return this.caller.on(m,l),this.App._installPluginHook(u,m),()=>{this.caller.off(m,l),this.App._uninstallPluginHook(u,m)}},invokeExternalPlugin(s,...l){var u;if(!(s=(u=s)===null||u===void 0?void 0:u.trim()))return;let[m,b]=s.split(".");if(!["models","commands"].includes(b?.toLowerCase()))throw new Error("Type only support '.models' or '.commands' currently.");const I=s.replace(`${m}.${b}.`,"");if(!m||!b||!I)throw new Error(`Illegal type of #${s} to invoke external plugin.`);return this._execCallableAPIAsync("invoke_external_plugin_cmd",m,b.toLowerCase(),I,l)},setFullScreen(s){const l=(...u)=>this._callWin("setFullScreen",...u);s==="toggle"?this._callWin("isFullScreen").then(u=>{u?l():l(!0)}):s?l(!0):l()}};let yc=0;const Ah={newBlockUUID(){return this._execCallableAPIAsync("new_block_uuid")},registerSlashCommand(s,l){var u;jh("Register slash command #",this.baseInfo.id,s,l),typeof l=="function"&&(l=[["editor/clear-current-slash",!1],["editor/restore-saved-cursor"],["editor/hook",l]]),l=l.map(m=>{const[b,...I]=m;if(b==="editor/hook"){let R=I[0],z=()=>{var J;(J=this.caller)===null||J===void 0||J.callUserModel(R)};typeof R=="function"&&(z=R);const V=`SlashCommandHook${b}${++yc}`;m[1]=V,this.Editor["on"+V](z)}return m}),(u=this.caller)===null||u===void 0||u.call("api:call",{method:"register-plugin-slash-command",args:[this.baseInfo.id,[s,l]]})},registerBlockContextMenuItem(s,l){if(typeof l!="function")return!1;const u=s+"_"+this.baseInfo.id;Mr.call(this,"block-context-menu-item",{key:u,label:s},l)},registerHighlightContextMenuItem(s,l,u){if(typeof l!="function")return!1;const m=s+"_"+this.baseInfo.id;Mr.call(this,"highlight-context-menu-item",{key:m,label:s,extras:u},l)},scrollToBlockInPage(s,l,u){const m="block-content-"+l;u!=null&&u.replaceState?this.App.replaceState("page",{name:s},{anchor:m}):this.App.pushState("page",{name:s},{anchor:m})}},Rh={onBlockChanged(s,l){if(!mc(s))return;const u=this.baseInfo.id,m=`hook:db:${g(`block:${s}`)}`,b=({block:I,txData:R,txMeta:z})=>{I.uuid===s&&l(I,R,z)};return this.caller.on(m,b),this.App._installPluginHook(u,m),()=>{this.caller.off(m,b),this.App._uninstallPluginHook(u,m)}},datascriptQuery(s,...l){return l.pop(),l!=null&&l.some(u=>typeof u=="function")?this.Experiments.ensureHostScope().logseq.api.datascript_query(s,...l):this._execCallableAPIAsync("datascript_query",s,...l)}},zh={},Dh={},Fh={makeSandboxStorage(){return new me(this,{assets:!0})}};class Ls extends O(){constructor(l,u){super(),$t(this,"_baseInfo",void 0),$t(this,"_caller",void 0),$t(this,"_version","0.0.17"),$t(this,"_debugTag",""),$t(this,"_settingsSchema",void 0),$t(this,"_connected",!1),$t(this,"_ui",new Map),$t(this,"_mFileStorage",void 0),$t(this,"_mRequest",void 0),$t(this,"_mExperiments",void 0),$t(this,"_beforeunloadCallback",void 0),this._baseInfo=l,this._caller=u,u.on("sys:ui:visible",m=>{m!=null&&m.toggle&&this.toggleMainUI()}),u.on("settings:changed",m=>{const b=Object.assign({},this.settings),I=Object.assign(this._baseInfo.settings,m);this.emit("settings:changed",{...I},b)}),u.on("beforeunload",async m=>{const{actor:b,...I}=m,R=this._beforeunloadCallback;try{R&&await R(I),b?.resolve(null)}catch(z){this.logger.error("[beforeunload] ",z),b?.reject(z)}})}async ready(l,u){var m,b;if(!this._connected)try{var I;typeof l=="function"&&(u=l,l={});let R=await this._caller.connectToParent(l);this._connected=!0,m=this._baseInfo,b=R,R=f()(m,b,{arrayMerge:(z,V)=>V}),this._baseInfo=R,(I=R)!==null&&I!==void 0&&I.id&&(this._debugTag=this._caller.debugTag=`#${R.id} [${R.name}]`,this.logger.setTag(this._debugTag)),this._settingsSchema&&(R.settings=function(z,V){const J=(V||[]).reduce((ne,Z)=>("default"in Z&&(ne[Z.key]=Z.default),ne),{});return Object.assign(J,z)}(R.settings,this._settingsSchema),await this.useSettingsSchema(this._settingsSchema));try{await this._execCallableAPIAsync("setSDKMetadata",{version:this._version})}catch(z){console.warn(z)}u&&u.call(this,R)}catch(R){console.error(`${this._debugTag} [Ready Error]`,R)}}ensureConnected(){if(!this._connected)throw new Error("not connected")}beforeunload(l){typeof l=="function"&&(this._beforeunloadCallback=l)}provideModel(l){return this.caller._extendUserModel(l),this}provideTheme(l){return this.caller.call("provider:theme",l),this}provideStyle(l){return this.caller.call("provider:style",l),this}provideUI(l){return this.caller.call("provider:ui",l),this}useSettingsSchema(l){return this.connected&&this.caller.call("settings:schema",{schema:l,isSync:!0}),this._settingsSchema=l,this}updateSettings(l){this.caller.call("settings:update",l)}onSettingsChanged(l){const u="settings:changed";return this.on(u,l),()=>this.off(u,l)}showSettingsUI(){this.caller.call("settings:visible:changed",{visible:!0})}hideSettingsUI(){this.caller.call("settings:visible:changed",{visible:!1})}setMainUIAttrs(l){this.caller.call("main-ui:attrs",l)}setMainUIInlineStyle(l){this.caller.call("main-ui:style",l)}hideMainUI(l){const u={key:0,visible:!1,cursor:l?.restoreEditingCursor};this.caller.call("main-ui:visible",u),this.emit("ui:visible:changed",u),this._ui.set(u.key,u)}showMainUI(l){const u={key:0,visible:!0,autoFocus:l?.autoFocus};this.caller.call("main-ui:visible",u),this.emit("ui:visible:changed",u),this._ui.set(u.key,u)}toggleMainUI(){const u=this._ui.get(0);u&&u.visible?this.hideMainUI():this.showMainUI()}get version(){return this._version}get isMainUIVisible(){const l=this._ui.get(0);return!!(l&&l.visible)}get connected(){return this._connected}get baseInfo(){return this._baseInfo}get effect(){return(l=this)&&(((u=l.baseInfo)===null||u===void 0?void 0:u.effect)||!((m=l.baseInfo)!==null&&m!==void 0&&m.iir));var l,u,m}get logger(){return hc}get settings(){var l;return(l=this.baseInfo)===null||l===void 0?void 0:l.settings}get caller(){return this._caller}resolveResourceFullUrl(l){if(this.ensureConnected(),l)return l=l.replace(/^[.\\/]+/,""),P(this._baseInfo.lsr,l)}_makeUserProxy(l,u){const m=this,b=this.caller;return new Proxy(l,{get(I,R,z){const V=I[R];return function(...J){if(V){const Z=V.apply(m,J.concat(u));if(Z!==Lh)return Z}if(u){const Z=R.toString().match(/^(once|off|on)/i);if(Z!=null){const le=Z[0].toLowerCase(),ge=Z.input,ue=le==="off",Se=m.baseInfo.id;let xe=ge.slice(le.length),We=J[0],Tt=J[1];typeof We=="string"&&typeof Tt=="function"&&(We=We.replace(/^logseq./,":"),xe=`${xe}${We}`,We=Tt,Tt=J[2]),xe=`hook:${u}:${g(xe)}`,b[le](xe,We);const cr=()=>{b.off(xe,We),b.listenerCount(xe)||m.App._uninstallPluginHook(Se,xe)};return ue?void cr():(m.App._installPluginHook(Se,xe,Tt),cr)}}let ne=R;return["git","ui","assets"].includes(u)&&(ne=u+"_"+ne),b.callAsync("api:call",{tag:u,method:ne,args:J})}}})}_execCallableAPIAsync(l,...u){return this._caller.callAsync("api:call",{method:l,args:u})}_execCallableAPI(l,...u){this._caller.call("api:call",{method:l,args:u})}_callWin(...l){return this._execCallableAPIAsync("_callMainWin",...l)}get App(){return this._makeUserProxy(Mh,"app")}get Editor(){return this._makeUserProxy(Ah,"editor")}get DB(){return this._makeUserProxy(Rh,"db")}get Git(){return this._makeUserProxy(zh,"git")}get UI(){return this._makeUserProxy(Dh,"ui")}get Assets(){return this._makeUserProxy(Fh,"assets")}get FileStorage(){let l=this._mFileStorage;return l||(l=this._mFileStorage=new me(this)),l}get Request(){let l=this._mRequest;return l||(l=this._mRequest=new vt(this)),l}get Experiments(){let l=this._mExperiments;return l||(l=this._mExperiments=new Ue(this)),l}}function vc(s,l){return new Ls(s,l)}if(window.__LSP__HOST__==null){const s=new ie(null);window.logseq=vc({},s)}})(),i})())})(wl,wl.exports);wl.exports;var Bd={exports:{}},xt={},Hd={exports:{}},Wd={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(F,T){var M=F.length;F.push(T);e:for(;0<M;){var $=M-1>>>1,ee=F[$];if(0<o(ee,T))F[$]=T,F[M]=ee,M=$;else break e}}function n(F){return F.length===0?null:F[0]}function r(F){if(F.length===0)return null;var T=F[0],M=F.pop();if(M!==T){F[0]=M;e:for(var $=0,ee=F.length,ve=ee>>>1;$<ve;){var B=2*($+1)-1,oe=F[B],fe=B+1,Re=F[fe];if(0>o(oe,M))fe<ee&&0>o(Re,oe)?(F[$]=Re,F[fe]=M,$=fe):(F[$]=oe,F[B]=M,$=B);else if(fe<ee&&0>o(Re,M))F[$]=Re,F[fe]=M,$=fe;else break e}}return T}function o(F,T){var M=F.sortIndex-T.sortIndex;return M!==0?M:F.id-T.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var a=Date,c=a.now();e.unstable_now=function(){return a.now()-c}}var f=[],p=[],w=1,C=null,x=3,E=!1,v=!1,N=!1,O=typeof setTimeout=="function"?setTimeout:null,h=typeof clearTimeout=="function"?clearTimeout:null,d=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function g(F){for(var T=n(p);T!==null;){if(T.callback===null)r(p);else if(T.startTime<=F)r(p),T.sortIndex=T.expirationTime,t(f,T);else break;T=n(p)}}function S(F){if(N=!1,g(F),!v)if(n(f)!==null)v=!0,Ne(P);else{var T=n(p);T!==null&&Q(S,T.startTime-F)}}function P(F,T){v=!1,N&&(N=!1,h(_),_=-1),E=!0;var M=x;try{for(g(T),C=n(f);C!==null&&(!(C.expirationTime>T)||F&&!G());){var $=C.callback;if(typeof $=="function"){C.callback=null,x=C.priorityLevel;var ee=$(C.expirationTime<=T);T=e.unstable_now(),typeof ee=="function"?C.callback=ee:C===n(f)&&r(f),g(T)}else r(f);C=n(f)}if(C!==null)var ve=!0;else{var B=n(p);B!==null&&Q(S,B.startTime-T),ve=!1}return ve}finally{C=null,x=M,E=!1}}var L=!1,y=null,_=-1,A=5,j=-1;function G(){return!(e.unstable_now()-j<A)}function ce(){if(y!==null){var F=e.unstable_now();j=F;var T=!0;try{T=y(!0,F)}finally{T?de():(L=!1,y=null)}}else L=!1}var de;if(typeof d=="function")de=function(){d(ce)};else if(typeof MessageChannel<"u"){var te=new MessageChannel,Te=te.port2;te.port1.onmessage=ce,de=function(){Te.postMessage(null)}}else de=function(){O(ce,0)};function Ne(F){y=F,L||(L=!0,de())}function Q(F,T){_=O(function(){F(e.unstable_now())},T)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(F){F.callback=null},e.unstable_continueExecution=function(){v||E||(v=!0,Ne(P))},e.unstable_forceFrameRate=function(F){0>F||125<F?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):A=0<F?Math.floor(1e3/F):5},e.unstable_getCurrentPriorityLevel=function(){return x},e.unstable_getFirstCallbackNode=function(){return n(f)},e.unstable_next=function(F){switch(x){case 1:case 2:case 3:var T=3;break;default:T=x}var M=x;x=T;try{return F()}finally{x=M}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(F,T){switch(F){case 1:case 2:case 3:case 4:case 5:break;default:F=3}var M=x;x=F;try{return T()}finally{x=M}},e.unstable_scheduleCallback=function(F,T,M){var $=e.unstable_now();switch(typeof M=="object"&&M!==null?(M=M.delay,M=typeof M=="number"&&0<M?$+M:$):M=$,F){case 1:var ee=-1;break;case 2:ee=250;break;case 5:ee=**********;break;case 4:ee=1e4;break;default:ee=5e3}return ee=M+ee,F={id:w++,callback:T,priorityLevel:F,startTime:M,expirationTime:ee,sortIndex:-1},M>$?(F.sortIndex=M,t(p,F),n(f)===null&&F===n(p)&&(N?(h(_),_=-1):N=!0,Q(S,M-$))):(F.sortIndex=ee,t(f,F),v||E||(v=!0,Ne(P))),F},e.unstable_shouldYield=G,e.unstable_wrapCallback=function(F){var T=x;return function(){var M=x;x=T;try{return F.apply(this,arguments)}finally{x=M}}}})(Wd);Hd.exports=Wd;var um=Hd.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var cm=ae,_t=um;function U(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Vd=new Set,Ko={};function _r(e,t){to(e,t),to(e+"Capture",t)}function to(e,t){for(Ko[e]=t,e=0;e<t.length;e++)Vd.add(t[e])}var yn=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),la=Object.prototype.hasOwnProperty,dm=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Sc={},_c={};function fm(e){return la.call(_c,e)?!0:la.call(Sc,e)?!1:dm.test(e)?_c[e]=!0:(Sc[e]=!0,!1)}function pm(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function hm(e,t,n,r){if(t===null||typeof t>"u"||pm(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function ct(e,t,n,r,o,i,a){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=a}var tt={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){tt[e]=new ct(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];tt[t]=new ct(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){tt[e]=new ct(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){tt[e]=new ct(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){tt[e]=new ct(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){tt[e]=new ct(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){tt[e]=new ct(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){tt[e]=new ct(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){tt[e]=new ct(e,5,!1,e.toLowerCase(),null,!1,!1)});var ou=/[\-:]([a-z])/g;function iu(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(ou,iu);tt[t]=new ct(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(ou,iu);tt[t]=new ct(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(ou,iu);tt[t]=new ct(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){tt[e]=new ct(e,1,!1,e.toLowerCase(),null,!1,!1)});tt.xlinkHref=new ct("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){tt[e]=new ct(e,1,!1,e.toLowerCase(),null,!0,!0)});function lu(e,t,n,r){var o=tt.hasOwnProperty(t)?tt[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(hm(t,n,o,r)&&(n=null),r||o===null?fm(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var Cn=cm.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Hi=Symbol.for("react.element"),Rr=Symbol.for("react.portal"),zr=Symbol.for("react.fragment"),su=Symbol.for("react.strict_mode"),sa=Symbol.for("react.profiler"),qd=Symbol.for("react.provider"),Qd=Symbol.for("react.context"),au=Symbol.for("react.forward_ref"),aa=Symbol.for("react.suspense"),ua=Symbol.for("react.suspense_list"),uu=Symbol.for("react.memo"),Mn=Symbol.for("react.lazy"),Kd=Symbol.for("react.offscreen"),xc=Symbol.iterator;function To(e){return e===null||typeof e!="object"?null:(e=xc&&e[xc]||e["@@iterator"],typeof e=="function"?e:null)}var Ae=Object.assign,Ms;function Mo(e){if(Ms===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Ms=t&&t[1]||""}return`
`+Ms+e}var As=!1;function Rs(e,t){if(!e||As)return"";As=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(p){var r=p}Reflect.construct(e,[],t)}else{try{t.call()}catch(p){r=p}e.call(t.prototype)}else{try{throw Error()}catch(p){r=p}e()}}catch(p){if(p&&r&&typeof p.stack=="string"){for(var o=p.stack.split(`
`),i=r.stack.split(`
`),a=o.length-1,c=i.length-1;1<=a&&0<=c&&o[a]!==i[c];)c--;for(;1<=a&&0<=c;a--,c--)if(o[a]!==i[c]){if(a!==1||c!==1)do if(a--,c--,0>c||o[a]!==i[c]){var f=`
`+o[a].replace(" at new "," at ");return e.displayName&&f.includes("<anonymous>")&&(f=f.replace("<anonymous>",e.displayName)),f}while(1<=a&&0<=c);break}}}finally{As=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Mo(e):""}function mm(e){switch(e.tag){case 5:return Mo(e.type);case 16:return Mo("Lazy");case 13:return Mo("Suspense");case 19:return Mo("SuspenseList");case 0:case 2:case 15:return e=Rs(e.type,!1),e;case 11:return e=Rs(e.type.render,!1),e;case 1:return e=Rs(e.type,!0),e;default:return""}}function ca(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case zr:return"Fragment";case Rr:return"Portal";case sa:return"Profiler";case su:return"StrictMode";case aa:return"Suspense";case ua:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Qd:return(e.displayName||"Context")+".Consumer";case qd:return(e._context.displayName||"Context")+".Provider";case au:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case uu:return t=e.displayName||null,t!==null?t:ca(e.type)||"Memo";case Mn:t=e._payload,e=e._init;try{return ca(e(t))}catch{}}return null}function gm(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return ca(t);case 8:return t===su?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Kn(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Gd(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function ym(e){var t=Gd(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(a){r=""+a,i.call(this,a)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(a){r=""+a},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Wi(e){e._valueTracker||(e._valueTracker=ym(e))}function Yd(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Gd(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function kl(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function da(e,t){var n=t.checked;return Ae({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Ec(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Kn(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Xd(e,t){t=t.checked,t!=null&&lu(e,"checked",t,!1)}function fa(e,t){Xd(e,t);var n=Kn(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?pa(e,t.type,n):t.hasOwnProperty("defaultValue")&&pa(e,t.type,Kn(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Tc(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function pa(e,t,n){(t!=="number"||kl(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Ao=Array.isArray;function Gr(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Kn(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function ha(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(U(91));return Ae({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Nc(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(U(92));if(Ao(n)){if(1<n.length)throw Error(U(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Kn(n)}}function Zd(e,t){var n=Kn(t.value),r=Kn(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function bc(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Jd(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function ma(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Jd(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Vi,ef=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Vi=Vi||document.createElement("div"),Vi.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Vi.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Go(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Do={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},vm=["Webkit","ms","Moz","O"];Object.keys(Do).forEach(function(e){vm.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Do[t]=Do[e]})});function tf(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Do.hasOwnProperty(e)&&Do[e]?(""+t).trim():t+"px"}function nf(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=tf(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var wm=Ae({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ga(e,t){if(t){if(wm[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(U(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(U(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(U(61))}if(t.style!=null&&typeof t.style!="object")throw Error(U(62))}}function ya(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var va=null;function cu(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var wa=null,Yr=null,Xr=null;function Pc(e){if(e=mi(e)){if(typeof wa!="function")throw Error(U(280));var t=e.stateNode;t&&(t=Yl(t),wa(e.stateNode,e.type,t))}}function rf(e){Yr?Xr?Xr.push(e):Xr=[e]:Yr=e}function of(){if(Yr){var e=Yr,t=Xr;if(Xr=Yr=null,Pc(e),t)for(e=0;e<t.length;e++)Pc(t[e])}}function lf(e,t){return e(t)}function sf(){}var zs=!1;function af(e,t,n){if(zs)return e(t,n);zs=!0;try{return lf(e,t,n)}finally{zs=!1,(Yr!==null||Xr!==null)&&(sf(),of())}}function Yo(e,t){var n=e.stateNode;if(n===null)return null;var r=Yl(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(U(231,t,typeof n));return n}var ka=!1;if(yn)try{var No={};Object.defineProperty(No,"passive",{get:function(){ka=!0}}),window.addEventListener("test",No,No),window.removeEventListener("test",No,No)}catch{ka=!1}function km(e,t,n,r,o,i,a,c,f){var p=Array.prototype.slice.call(arguments,3);try{t.apply(n,p)}catch(w){this.onError(w)}}var Fo=!1,Cl=null,Sl=!1,Ca=null,Cm={onError:function(e){Fo=!0,Cl=e}};function Sm(e,t,n,r,o,i,a,c,f){Fo=!1,Cl=null,km.apply(Cm,arguments)}function _m(e,t,n,r,o,i,a,c,f){if(Sm.apply(this,arguments),Fo){if(Fo){var p=Cl;Fo=!1,Cl=null}else throw Error(U(198));Sl||(Sl=!0,Ca=p)}}function xr(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function uf(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Oc(e){if(xr(e)!==e)throw Error(U(188))}function xm(e){var t=e.alternate;if(!t){if(t=xr(e),t===null)throw Error(U(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var i=o.alternate;if(i===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return Oc(o),e;if(i===r)return Oc(o),t;i=i.sibling}throw Error(U(188))}if(n.return!==r.return)n=o,r=i;else{for(var a=!1,c=o.child;c;){if(c===n){a=!0,n=o,r=i;break}if(c===r){a=!0,r=o,n=i;break}c=c.sibling}if(!a){for(c=i.child;c;){if(c===n){a=!0,n=i,r=o;break}if(c===r){a=!0,r=i,n=o;break}c=c.sibling}if(!a)throw Error(U(189))}}if(n.alternate!==r)throw Error(U(190))}if(n.tag!==3)throw Error(U(188));return n.stateNode.current===n?e:t}function cf(e){return e=xm(e),e!==null?df(e):null}function df(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=df(e);if(t!==null)return t;e=e.sibling}return null}var ff=_t.unstable_scheduleCallback,Ic=_t.unstable_cancelCallback,Em=_t.unstable_shouldYield,Tm=_t.unstable_requestPaint,Fe=_t.unstable_now,Nm=_t.unstable_getCurrentPriorityLevel,du=_t.unstable_ImmediatePriority,pf=_t.unstable_UserBlockingPriority,_l=_t.unstable_NormalPriority,bm=_t.unstable_LowPriority,hf=_t.unstable_IdlePriority,ql=null,rn=null;function Pm(e){if(rn&&typeof rn.onCommitFiberRoot=="function")try{rn.onCommitFiberRoot(ql,e,void 0,(e.current.flags&128)===128)}catch{}}var qt=Math.clz32?Math.clz32:Lm,Om=Math.log,Im=Math.LN2;function Lm(e){return e>>>=0,e===0?32:31-(Om(e)/Im|0)|0}var qi=64,Qi=4194304;function Ro(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function xl(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,i=e.pingedLanes,a=n&268435455;if(a!==0){var c=a&~o;c!==0?r=Ro(c):(i&=a,i!==0&&(r=Ro(i)))}else a=n&~o,a!==0?r=Ro(a):i!==0&&(r=Ro(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,i=t&-t,o>=i||o===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-qt(t),o=1<<n,r|=e[n],t&=~o;return r}function jm(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Mm(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,i=e.pendingLanes;0<i;){var a=31-qt(i),c=1<<a,f=o[a];f===-1?(!(c&n)||c&r)&&(o[a]=jm(c,t)):f<=t&&(e.expiredLanes|=c),i&=~c}}function Sa(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function mf(){var e=qi;return qi<<=1,!(qi&4194240)&&(qi=64),e}function Ds(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function pi(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-qt(t),e[t]=n}function Am(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-qt(n),i=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~i}}function fu(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-qt(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var _e=0;function gf(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var yf,pu,vf,wf,kf,_a=!1,Ki=[],Un=null,$n=null,Bn=null,Xo=new Map,Zo=new Map,Rn=[],Rm="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Lc(e,t){switch(e){case"focusin":case"focusout":Un=null;break;case"dragenter":case"dragleave":$n=null;break;case"mouseover":case"mouseout":Bn=null;break;case"pointerover":case"pointerout":Xo.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Zo.delete(t.pointerId)}}function bo(e,t,n,r,o,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[o]},t!==null&&(t=mi(t),t!==null&&pu(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function zm(e,t,n,r,o){switch(t){case"focusin":return Un=bo(Un,e,t,n,r,o),!0;case"dragenter":return $n=bo($n,e,t,n,r,o),!0;case"mouseover":return Bn=bo(Bn,e,t,n,r,o),!0;case"pointerover":var i=o.pointerId;return Xo.set(i,bo(Xo.get(i)||null,e,t,n,r,o)),!0;case"gotpointercapture":return i=o.pointerId,Zo.set(i,bo(Zo.get(i)||null,e,t,n,r,o)),!0}return!1}function Cf(e){var t=pr(e.target);if(t!==null){var n=xr(t);if(n!==null){if(t=n.tag,t===13){if(t=uf(n),t!==null){e.blockedOn=t,kf(e.priority,function(){vf(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function ul(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=xa(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);va=r,n.target.dispatchEvent(r),va=null}else return t=mi(n),t!==null&&pu(t),e.blockedOn=n,!1;t.shift()}return!0}function jc(e,t,n){ul(e)&&n.delete(t)}function Dm(){_a=!1,Un!==null&&ul(Un)&&(Un=null),$n!==null&&ul($n)&&($n=null),Bn!==null&&ul(Bn)&&(Bn=null),Xo.forEach(jc),Zo.forEach(jc)}function Po(e,t){e.blockedOn===t&&(e.blockedOn=null,_a||(_a=!0,_t.unstable_scheduleCallback(_t.unstable_NormalPriority,Dm)))}function Jo(e){function t(o){return Po(o,e)}if(0<Ki.length){Po(Ki[0],e);for(var n=1;n<Ki.length;n++){var r=Ki[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Un!==null&&Po(Un,e),$n!==null&&Po($n,e),Bn!==null&&Po(Bn,e),Xo.forEach(t),Zo.forEach(t),n=0;n<Rn.length;n++)r=Rn[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Rn.length&&(n=Rn[0],n.blockedOn===null);)Cf(n),n.blockedOn===null&&Rn.shift()}var Zr=Cn.ReactCurrentBatchConfig,El=!0;function Fm(e,t,n,r){var o=_e,i=Zr.transition;Zr.transition=null;try{_e=1,hu(e,t,n,r)}finally{_e=o,Zr.transition=i}}function Um(e,t,n,r){var o=_e,i=Zr.transition;Zr.transition=null;try{_e=4,hu(e,t,n,r)}finally{_e=o,Zr.transition=i}}function hu(e,t,n,r){if(El){var o=xa(e,t,n,r);if(o===null)Ks(e,t,r,Tl,n),Lc(e,r);else if(zm(o,e,t,n,r))r.stopPropagation();else if(Lc(e,r),t&4&&-1<Rm.indexOf(e)){for(;o!==null;){var i=mi(o);if(i!==null&&yf(i),i=xa(e,t,n,r),i===null&&Ks(e,t,r,Tl,n),i===o)break;o=i}o!==null&&r.stopPropagation()}else Ks(e,t,r,null,n)}}var Tl=null;function xa(e,t,n,r){if(Tl=null,e=cu(r),e=pr(e),e!==null)if(t=xr(e),t===null)e=null;else if(n=t.tag,n===13){if(e=uf(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Tl=e,null}function Sf(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Nm()){case du:return 1;case pf:return 4;case _l:case bm:return 16;case hf:return 536870912;default:return 16}default:return 16}}var Dn=null,mu=null,cl=null;function _f(){if(cl)return cl;var e,t=mu,n=t.length,r,o="value"in Dn?Dn.value:Dn.textContent,i=o.length;for(e=0;e<n&&t[e]===o[e];e++);var a=n-e;for(r=1;r<=a&&t[n-r]===o[i-r];r++);return cl=o.slice(e,1<r?1-r:void 0)}function dl(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Gi(){return!0}function Mc(){return!1}function Et(e){function t(n,r,o,i,a){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=i,this.target=a,this.currentTarget=null;for(var c in e)e.hasOwnProperty(c)&&(n=e[c],this[c]=n?n(i):i[c]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Gi:Mc,this.isPropagationStopped=Mc,this}return Ae(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Gi)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Gi)},persist:function(){},isPersistent:Gi}),t}var uo={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},gu=Et(uo),hi=Ae({},uo,{view:0,detail:0}),$m=Et(hi),Fs,Us,Oo,Ql=Ae({},hi,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:yu,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Oo&&(Oo&&e.type==="mousemove"?(Fs=e.screenX-Oo.screenX,Us=e.screenY-Oo.screenY):Us=Fs=0,Oo=e),Fs)},movementY:function(e){return"movementY"in e?e.movementY:Us}}),Ac=Et(Ql),Bm=Ae({},Ql,{dataTransfer:0}),Hm=Et(Bm),Wm=Ae({},hi,{relatedTarget:0}),$s=Et(Wm),Vm=Ae({},uo,{animationName:0,elapsedTime:0,pseudoElement:0}),qm=Et(Vm),Qm=Ae({},uo,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Km=Et(Qm),Gm=Ae({},uo,{data:0}),Rc=Et(Gm),Ym={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Xm={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Zm={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Jm(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Zm[e])?!!t[e]:!1}function yu(){return Jm}var eg=Ae({},hi,{key:function(e){if(e.key){var t=Ym[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=dl(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Xm[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:yu,charCode:function(e){return e.type==="keypress"?dl(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?dl(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),tg=Et(eg),ng=Ae({},Ql,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),zc=Et(ng),rg=Ae({},hi,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:yu}),og=Et(rg),ig=Ae({},uo,{propertyName:0,elapsedTime:0,pseudoElement:0}),lg=Et(ig),sg=Ae({},Ql,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),ag=Et(sg),ug=[9,13,27,32],vu=yn&&"CompositionEvent"in window,Uo=null;yn&&"documentMode"in document&&(Uo=document.documentMode);var cg=yn&&"TextEvent"in window&&!Uo,xf=yn&&(!vu||Uo&&8<Uo&&11>=Uo),Dc=String.fromCharCode(32),Fc=!1;function Ef(e,t){switch(e){case"keyup":return ug.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Tf(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Dr=!1;function dg(e,t){switch(e){case"compositionend":return Tf(t);case"keypress":return t.which!==32?null:(Fc=!0,Dc);case"textInput":return e=t.data,e===Dc&&Fc?null:e;default:return null}}function fg(e,t){if(Dr)return e==="compositionend"||!vu&&Ef(e,t)?(e=_f(),cl=mu=Dn=null,Dr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return xf&&t.locale!=="ko"?null:t.data;default:return null}}var pg={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Uc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!pg[e.type]:t==="textarea"}function Nf(e,t,n,r){rf(r),t=Nl(t,"onChange"),0<t.length&&(n=new gu("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var $o=null,ei=null;function hg(e){Df(e,0)}function Kl(e){var t=$r(e);if(Yd(t))return e}function mg(e,t){if(e==="change")return t}var bf=!1;if(yn){var Bs;if(yn){var Hs="oninput"in document;if(!Hs){var $c=document.createElement("div");$c.setAttribute("oninput","return;"),Hs=typeof $c.oninput=="function"}Bs=Hs}else Bs=!1;bf=Bs&&(!document.documentMode||9<document.documentMode)}function Bc(){$o&&($o.detachEvent("onpropertychange",Pf),ei=$o=null)}function Pf(e){if(e.propertyName==="value"&&Kl(ei)){var t=[];Nf(t,ei,e,cu(e)),af(hg,t)}}function gg(e,t,n){e==="focusin"?(Bc(),$o=t,ei=n,$o.attachEvent("onpropertychange",Pf)):e==="focusout"&&Bc()}function yg(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Kl(ei)}function vg(e,t){if(e==="click")return Kl(t)}function wg(e,t){if(e==="input"||e==="change")return Kl(t)}function kg(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Kt=typeof Object.is=="function"?Object.is:kg;function ti(e,t){if(Kt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!la.call(t,o)||!Kt(e[o],t[o]))return!1}return!0}function Hc(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Wc(e,t){var n=Hc(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Hc(n)}}function Of(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Of(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function If(){for(var e=window,t=kl();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=kl(e.document)}return t}function wu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Cg(e){var t=If(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Of(n.ownerDocument.documentElement,n)){if(r!==null&&wu(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,i=Math.min(r.start,o);r=r.end===void 0?i:Math.min(r.end,o),!e.extend&&i>r&&(o=r,r=i,i=o),o=Wc(n,i);var a=Wc(n,r);o&&a&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==a.node||e.focusOffset!==a.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(a.node,a.offset)):(t.setEnd(a.node,a.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Sg=yn&&"documentMode"in document&&11>=document.documentMode,Fr=null,Ea=null,Bo=null,Ta=!1;function Vc(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Ta||Fr==null||Fr!==kl(r)||(r=Fr,"selectionStart"in r&&wu(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Bo&&ti(Bo,r)||(Bo=r,r=Nl(Ea,"onSelect"),0<r.length&&(t=new gu("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Fr)))}function Yi(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Ur={animationend:Yi("Animation","AnimationEnd"),animationiteration:Yi("Animation","AnimationIteration"),animationstart:Yi("Animation","AnimationStart"),transitionend:Yi("Transition","TransitionEnd")},Ws={},Lf={};yn&&(Lf=document.createElement("div").style,"AnimationEvent"in window||(delete Ur.animationend.animation,delete Ur.animationiteration.animation,delete Ur.animationstart.animation),"TransitionEvent"in window||delete Ur.transitionend.transition);function Gl(e){if(Ws[e])return Ws[e];if(!Ur[e])return e;var t=Ur[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Lf)return Ws[e]=t[n];return e}var jf=Gl("animationend"),Mf=Gl("animationiteration"),Af=Gl("animationstart"),Rf=Gl("transitionend"),zf=new Map,qc="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Yn(e,t){zf.set(e,t),_r(t,[e])}for(var Vs=0;Vs<qc.length;Vs++){var qs=qc[Vs],_g=qs.toLowerCase(),xg=qs[0].toUpperCase()+qs.slice(1);Yn(_g,"on"+xg)}Yn(jf,"onAnimationEnd");Yn(Mf,"onAnimationIteration");Yn(Af,"onAnimationStart");Yn("dblclick","onDoubleClick");Yn("focusin","onFocus");Yn("focusout","onBlur");Yn(Rf,"onTransitionEnd");to("onMouseEnter",["mouseout","mouseover"]);to("onMouseLeave",["mouseout","mouseover"]);to("onPointerEnter",["pointerout","pointerover"]);to("onPointerLeave",["pointerout","pointerover"]);_r("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));_r("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));_r("onBeforeInput",["compositionend","keypress","textInput","paste"]);_r("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));_r("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));_r("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var zo="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Eg=new Set("cancel close invalid load scroll toggle".split(" ").concat(zo));function Qc(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,_m(r,t,void 0,e),e.currentTarget=null}function Df(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var a=r.length-1;0<=a;a--){var c=r[a],f=c.instance,p=c.currentTarget;if(c=c.listener,f!==i&&o.isPropagationStopped())break e;Qc(o,c,p),i=f}else for(a=0;a<r.length;a++){if(c=r[a],f=c.instance,p=c.currentTarget,c=c.listener,f!==i&&o.isPropagationStopped())break e;Qc(o,c,p),i=f}}}if(Sl)throw e=Ca,Sl=!1,Ca=null,e}function be(e,t){var n=t[Ia];n===void 0&&(n=t[Ia]=new Set);var r=e+"__bubble";n.has(r)||(Ff(t,e,2,!1),n.add(r))}function Qs(e,t,n){var r=0;t&&(r|=4),Ff(n,e,r,t)}var Xi="_reactListening"+Math.random().toString(36).slice(2);function ni(e){if(!e[Xi]){e[Xi]=!0,Vd.forEach(function(n){n!=="selectionchange"&&(Eg.has(n)||Qs(n,!1,e),Qs(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Xi]||(t[Xi]=!0,Qs("selectionchange",!1,t))}}function Ff(e,t,n,r){switch(Sf(t)){case 1:var o=Fm;break;case 4:o=Um;break;default:o=hu}n=o.bind(null,t,n,e),o=void 0,!ka||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Ks(e,t,n,r,o){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var a=r.tag;if(a===3||a===4){var c=r.stateNode.containerInfo;if(c===o||c.nodeType===8&&c.parentNode===o)break;if(a===4)for(a=r.return;a!==null;){var f=a.tag;if((f===3||f===4)&&(f=a.stateNode.containerInfo,f===o||f.nodeType===8&&f.parentNode===o))return;a=a.return}for(;c!==null;){if(a=pr(c),a===null)return;if(f=a.tag,f===5||f===6){r=i=a;continue e}c=c.parentNode}}r=r.return}af(function(){var p=i,w=cu(n),C=[];e:{var x=zf.get(e);if(x!==void 0){var E=gu,v=e;switch(e){case"keypress":if(dl(n)===0)break e;case"keydown":case"keyup":E=tg;break;case"focusin":v="focus",E=$s;break;case"focusout":v="blur",E=$s;break;case"beforeblur":case"afterblur":E=$s;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":E=Ac;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":E=Hm;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":E=og;break;case jf:case Mf:case Af:E=qm;break;case Rf:E=lg;break;case"scroll":E=$m;break;case"wheel":E=ag;break;case"copy":case"cut":case"paste":E=Km;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":E=zc}var N=(t&4)!==0,O=!N&&e==="scroll",h=N?x!==null?x+"Capture":null:x;N=[];for(var d=p,g;d!==null;){g=d;var S=g.stateNode;if(g.tag===5&&S!==null&&(g=S,h!==null&&(S=Yo(d,h),S!=null&&N.push(ri(d,S,g)))),O)break;d=d.return}0<N.length&&(x=new E(x,v,null,n,w),C.push({event:x,listeners:N}))}}if(!(t&7)){e:{if(x=e==="mouseover"||e==="pointerover",E=e==="mouseout"||e==="pointerout",x&&n!==va&&(v=n.relatedTarget||n.fromElement)&&(pr(v)||v[vn]))break e;if((E||x)&&(x=w.window===w?w:(x=w.ownerDocument)?x.defaultView||x.parentWindow:window,E?(v=n.relatedTarget||n.toElement,E=p,v=v?pr(v):null,v!==null&&(O=xr(v),v!==O||v.tag!==5&&v.tag!==6)&&(v=null)):(E=null,v=p),E!==v)){if(N=Ac,S="onMouseLeave",h="onMouseEnter",d="mouse",(e==="pointerout"||e==="pointerover")&&(N=zc,S="onPointerLeave",h="onPointerEnter",d="pointer"),O=E==null?x:$r(E),g=v==null?x:$r(v),x=new N(S,d+"leave",E,n,w),x.target=O,x.relatedTarget=g,S=null,pr(w)===p&&(N=new N(h,d+"enter",v,n,w),N.target=g,N.relatedTarget=O,S=N),O=S,E&&v)t:{for(N=E,h=v,d=0,g=N;g;g=Ar(g))d++;for(g=0,S=h;S;S=Ar(S))g++;for(;0<d-g;)N=Ar(N),d--;for(;0<g-d;)h=Ar(h),g--;for(;d--;){if(N===h||h!==null&&N===h.alternate)break t;N=Ar(N),h=Ar(h)}N=null}else N=null;E!==null&&Kc(C,x,E,N,!1),v!==null&&O!==null&&Kc(C,O,v,N,!0)}}e:{if(x=p?$r(p):window,E=x.nodeName&&x.nodeName.toLowerCase(),E==="select"||E==="input"&&x.type==="file")var P=mg;else if(Uc(x))if(bf)P=wg;else{P=yg;var L=gg}else(E=x.nodeName)&&E.toLowerCase()==="input"&&(x.type==="checkbox"||x.type==="radio")&&(P=vg);if(P&&(P=P(e,p))){Nf(C,P,n,w);break e}L&&L(e,x,p),e==="focusout"&&(L=x._wrapperState)&&L.controlled&&x.type==="number"&&pa(x,"number",x.value)}switch(L=p?$r(p):window,e){case"focusin":(Uc(L)||L.contentEditable==="true")&&(Fr=L,Ea=p,Bo=null);break;case"focusout":Bo=Ea=Fr=null;break;case"mousedown":Ta=!0;break;case"contextmenu":case"mouseup":case"dragend":Ta=!1,Vc(C,n,w);break;case"selectionchange":if(Sg)break;case"keydown":case"keyup":Vc(C,n,w)}var y;if(vu)e:{switch(e){case"compositionstart":var _="onCompositionStart";break e;case"compositionend":_="onCompositionEnd";break e;case"compositionupdate":_="onCompositionUpdate";break e}_=void 0}else Dr?Ef(e,n)&&(_="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(_="onCompositionStart");_&&(xf&&n.locale!=="ko"&&(Dr||_!=="onCompositionStart"?_==="onCompositionEnd"&&Dr&&(y=_f()):(Dn=w,mu="value"in Dn?Dn.value:Dn.textContent,Dr=!0)),L=Nl(p,_),0<L.length&&(_=new Rc(_,e,null,n,w),C.push({event:_,listeners:L}),y?_.data=y:(y=Tf(n),y!==null&&(_.data=y)))),(y=cg?dg(e,n):fg(e,n))&&(p=Nl(p,"onBeforeInput"),0<p.length&&(w=new Rc("onBeforeInput","beforeinput",null,n,w),C.push({event:w,listeners:p}),w.data=y))}Df(C,t)})}function ri(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Nl(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,i=o.stateNode;o.tag===5&&i!==null&&(o=i,i=Yo(e,n),i!=null&&r.unshift(ri(e,i,o)),i=Yo(e,t),i!=null&&r.push(ri(e,i,o))),e=e.return}return r}function Ar(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Kc(e,t,n,r,o){for(var i=t._reactName,a=[];n!==null&&n!==r;){var c=n,f=c.alternate,p=c.stateNode;if(f!==null&&f===r)break;c.tag===5&&p!==null&&(c=p,o?(f=Yo(n,i),f!=null&&a.unshift(ri(n,f,c))):o||(f=Yo(n,i),f!=null&&a.push(ri(n,f,c)))),n=n.return}a.length!==0&&e.push({event:t,listeners:a})}var Tg=/\r\n?/g,Ng=/\u0000|\uFFFD/g;function Gc(e){return(typeof e=="string"?e:""+e).replace(Tg,`
`).replace(Ng,"")}function Zi(e,t,n){if(t=Gc(t),Gc(e)!==t&&n)throw Error(U(425))}function bl(){}var Na=null,ba=null;function Pa(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Oa=typeof setTimeout=="function"?setTimeout:void 0,bg=typeof clearTimeout=="function"?clearTimeout:void 0,Yc=typeof Promise=="function"?Promise:void 0,Pg=typeof queueMicrotask=="function"?queueMicrotask:typeof Yc<"u"?function(e){return Yc.resolve(null).then(e).catch(Og)}:Oa;function Og(e){setTimeout(function(){throw e})}function Gs(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),Jo(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);Jo(t)}function Hn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Xc(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var co=Math.random().toString(36).slice(2),nn="__reactFiber$"+co,oi="__reactProps$"+co,vn="__reactContainer$"+co,Ia="__reactEvents$"+co,Ig="__reactListeners$"+co,Lg="__reactHandles$"+co;function pr(e){var t=e[nn];if(t)return t;for(var n=e.parentNode;n;){if(t=n[vn]||n[nn]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Xc(e);e!==null;){if(n=e[nn])return n;e=Xc(e)}return t}e=n,n=e.parentNode}return null}function mi(e){return e=e[nn]||e[vn],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function $r(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(U(33))}function Yl(e){return e[oi]||null}var La=[],Br=-1;function Xn(e){return{current:e}}function Pe(e){0>Br||(e.current=La[Br],La[Br]=null,Br--)}function Ee(e,t){Br++,La[Br]=e.current,e.current=t}var Gn={},lt=Xn(Gn),mt=Xn(!1),vr=Gn;function no(e,t){var n=e.type.contextTypes;if(!n)return Gn;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},i;for(i in n)o[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function gt(e){return e=e.childContextTypes,e!=null}function Pl(){Pe(mt),Pe(lt)}function Zc(e,t,n){if(lt.current!==Gn)throw Error(U(168));Ee(lt,t),Ee(mt,n)}function Uf(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(U(108,gm(e)||"Unknown",o));return Ae({},n,r)}function Ol(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Gn,vr=lt.current,Ee(lt,e),Ee(mt,mt.current),!0}function Jc(e,t,n){var r=e.stateNode;if(!r)throw Error(U(169));n?(e=Uf(e,t,vr),r.__reactInternalMemoizedMergedChildContext=e,Pe(mt),Pe(lt),Ee(lt,e)):Pe(mt),Ee(mt,n)}var pn=null,Xl=!1,Ys=!1;function $f(e){pn===null?pn=[e]:pn.push(e)}function jg(e){Xl=!0,$f(e)}function Zn(){if(!Ys&&pn!==null){Ys=!0;var e=0,t=_e;try{var n=pn;for(_e=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}pn=null,Xl=!1}catch(o){throw pn!==null&&(pn=pn.slice(e+1)),ff(du,Zn),o}finally{_e=t,Ys=!1}}return null}var Hr=[],Wr=0,Il=null,Ll=0,Nt=[],bt=0,wr=null,hn=1,mn="";function dr(e,t){Hr[Wr++]=Ll,Hr[Wr++]=Il,Il=e,Ll=t}function Bf(e,t,n){Nt[bt++]=hn,Nt[bt++]=mn,Nt[bt++]=wr,wr=e;var r=hn;e=mn;var o=32-qt(r)-1;r&=~(1<<o),n+=1;var i=32-qt(t)+o;if(30<i){var a=o-o%5;i=(r&(1<<a)-1).toString(32),r>>=a,o-=a,hn=1<<32-qt(t)+o|n<<o|r,mn=i+e}else hn=1<<i|n<<o|r,mn=e}function ku(e){e.return!==null&&(dr(e,1),Bf(e,1,0))}function Cu(e){for(;e===Il;)Il=Hr[--Wr],Hr[Wr]=null,Ll=Hr[--Wr],Hr[Wr]=null;for(;e===wr;)wr=Nt[--bt],Nt[bt]=null,mn=Nt[--bt],Nt[bt]=null,hn=Nt[--bt],Nt[bt]=null}var St=null,Ct=null,Ie=!1,Vt=null;function Hf(e,t){var n=Pt(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function ed(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,St=e,Ct=Hn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,St=e,Ct=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=wr!==null?{id:hn,overflow:mn}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Pt(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,St=e,Ct=null,!0):!1;default:return!1}}function ja(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Ma(e){if(Ie){var t=Ct;if(t){var n=t;if(!ed(e,t)){if(ja(e))throw Error(U(418));t=Hn(n.nextSibling);var r=St;t&&ed(e,t)?Hf(r,n):(e.flags=e.flags&-4097|2,Ie=!1,St=e)}}else{if(ja(e))throw Error(U(418));e.flags=e.flags&-4097|2,Ie=!1,St=e}}}function td(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;St=e}function Ji(e){if(e!==St)return!1;if(!Ie)return td(e),Ie=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Pa(e.type,e.memoizedProps)),t&&(t=Ct)){if(ja(e))throw Wf(),Error(U(418));for(;t;)Hf(e,t),t=Hn(t.nextSibling)}if(td(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(U(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Ct=Hn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Ct=null}}else Ct=St?Hn(e.stateNode.nextSibling):null;return!0}function Wf(){for(var e=Ct;e;)e=Hn(e.nextSibling)}function ro(){Ct=St=null,Ie=!1}function Su(e){Vt===null?Vt=[e]:Vt.push(e)}var Mg=Cn.ReactCurrentBatchConfig;function Io(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(U(309));var r=n.stateNode}if(!r)throw Error(U(147,e));var o=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(a){var c=o.refs;a===null?delete c[i]:c[i]=a},t._stringRef=i,t)}if(typeof e!="string")throw Error(U(284));if(!n._owner)throw Error(U(290,e))}return e}function el(e,t){throw e=Object.prototype.toString.call(t),Error(U(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function nd(e){var t=e._init;return t(e._payload)}function Vf(e){function t(h,d){if(e){var g=h.deletions;g===null?(h.deletions=[d],h.flags|=16):g.push(d)}}function n(h,d){if(!e)return null;for(;d!==null;)t(h,d),d=d.sibling;return null}function r(h,d){for(h=new Map;d!==null;)d.key!==null?h.set(d.key,d):h.set(d.index,d),d=d.sibling;return h}function o(h,d){return h=Qn(h,d),h.index=0,h.sibling=null,h}function i(h,d,g){return h.index=g,e?(g=h.alternate,g!==null?(g=g.index,g<d?(h.flags|=2,d):g):(h.flags|=2,d)):(h.flags|=1048576,d)}function a(h){return e&&h.alternate===null&&(h.flags|=2),h}function c(h,d,g,S){return d===null||d.tag!==6?(d=ra(g,h.mode,S),d.return=h,d):(d=o(d,g),d.return=h,d)}function f(h,d,g,S){var P=g.type;return P===zr?w(h,d,g.props.children,S,g.key):d!==null&&(d.elementType===P||typeof P=="object"&&P!==null&&P.$$typeof===Mn&&nd(P)===d.type)?(S=o(d,g.props),S.ref=Io(h,d,g),S.return=h,S):(S=vl(g.type,g.key,g.props,null,h.mode,S),S.ref=Io(h,d,g),S.return=h,S)}function p(h,d,g,S){return d===null||d.tag!==4||d.stateNode.containerInfo!==g.containerInfo||d.stateNode.implementation!==g.implementation?(d=oa(g,h.mode,S),d.return=h,d):(d=o(d,g.children||[]),d.return=h,d)}function w(h,d,g,S,P){return d===null||d.tag!==7?(d=yr(g,h.mode,S,P),d.return=h,d):(d=o(d,g),d.return=h,d)}function C(h,d,g){if(typeof d=="string"&&d!==""||typeof d=="number")return d=ra(""+d,h.mode,g),d.return=h,d;if(typeof d=="object"&&d!==null){switch(d.$$typeof){case Hi:return g=vl(d.type,d.key,d.props,null,h.mode,g),g.ref=Io(h,null,d),g.return=h,g;case Rr:return d=oa(d,h.mode,g),d.return=h,d;case Mn:var S=d._init;return C(h,S(d._payload),g)}if(Ao(d)||To(d))return d=yr(d,h.mode,g,null),d.return=h,d;el(h,d)}return null}function x(h,d,g,S){var P=d!==null?d.key:null;if(typeof g=="string"&&g!==""||typeof g=="number")return P!==null?null:c(h,d,""+g,S);if(typeof g=="object"&&g!==null){switch(g.$$typeof){case Hi:return g.key===P?f(h,d,g,S):null;case Rr:return g.key===P?p(h,d,g,S):null;case Mn:return P=g._init,x(h,d,P(g._payload),S)}if(Ao(g)||To(g))return P!==null?null:w(h,d,g,S,null);el(h,g)}return null}function E(h,d,g,S,P){if(typeof S=="string"&&S!==""||typeof S=="number")return h=h.get(g)||null,c(d,h,""+S,P);if(typeof S=="object"&&S!==null){switch(S.$$typeof){case Hi:return h=h.get(S.key===null?g:S.key)||null,f(d,h,S,P);case Rr:return h=h.get(S.key===null?g:S.key)||null,p(d,h,S,P);case Mn:var L=S._init;return E(h,d,g,L(S._payload),P)}if(Ao(S)||To(S))return h=h.get(g)||null,w(d,h,S,P,null);el(d,S)}return null}function v(h,d,g,S){for(var P=null,L=null,y=d,_=d=0,A=null;y!==null&&_<g.length;_++){y.index>_?(A=y,y=null):A=y.sibling;var j=x(h,y,g[_],S);if(j===null){y===null&&(y=A);break}e&&y&&j.alternate===null&&t(h,y),d=i(j,d,_),L===null?P=j:L.sibling=j,L=j,y=A}if(_===g.length)return n(h,y),Ie&&dr(h,_),P;if(y===null){for(;_<g.length;_++)y=C(h,g[_],S),y!==null&&(d=i(y,d,_),L===null?P=y:L.sibling=y,L=y);return Ie&&dr(h,_),P}for(y=r(h,y);_<g.length;_++)A=E(y,h,_,g[_],S),A!==null&&(e&&A.alternate!==null&&y.delete(A.key===null?_:A.key),d=i(A,d,_),L===null?P=A:L.sibling=A,L=A);return e&&y.forEach(function(G){return t(h,G)}),Ie&&dr(h,_),P}function N(h,d,g,S){var P=To(g);if(typeof P!="function")throw Error(U(150));if(g=P.call(g),g==null)throw Error(U(151));for(var L=P=null,y=d,_=d=0,A=null,j=g.next();y!==null&&!j.done;_++,j=g.next()){y.index>_?(A=y,y=null):A=y.sibling;var G=x(h,y,j.value,S);if(G===null){y===null&&(y=A);break}e&&y&&G.alternate===null&&t(h,y),d=i(G,d,_),L===null?P=G:L.sibling=G,L=G,y=A}if(j.done)return n(h,y),Ie&&dr(h,_),P;if(y===null){for(;!j.done;_++,j=g.next())j=C(h,j.value,S),j!==null&&(d=i(j,d,_),L===null?P=j:L.sibling=j,L=j);return Ie&&dr(h,_),P}for(y=r(h,y);!j.done;_++,j=g.next())j=E(y,h,_,j.value,S),j!==null&&(e&&j.alternate!==null&&y.delete(j.key===null?_:j.key),d=i(j,d,_),L===null?P=j:L.sibling=j,L=j);return e&&y.forEach(function(ce){return t(h,ce)}),Ie&&dr(h,_),P}function O(h,d,g,S){if(typeof g=="object"&&g!==null&&g.type===zr&&g.key===null&&(g=g.props.children),typeof g=="object"&&g!==null){switch(g.$$typeof){case Hi:e:{for(var P=g.key,L=d;L!==null;){if(L.key===P){if(P=g.type,P===zr){if(L.tag===7){n(h,L.sibling),d=o(L,g.props.children),d.return=h,h=d;break e}}else if(L.elementType===P||typeof P=="object"&&P!==null&&P.$$typeof===Mn&&nd(P)===L.type){n(h,L.sibling),d=o(L,g.props),d.ref=Io(h,L,g),d.return=h,h=d;break e}n(h,L);break}else t(h,L);L=L.sibling}g.type===zr?(d=yr(g.props.children,h.mode,S,g.key),d.return=h,h=d):(S=vl(g.type,g.key,g.props,null,h.mode,S),S.ref=Io(h,d,g),S.return=h,h=S)}return a(h);case Rr:e:{for(L=g.key;d!==null;){if(d.key===L)if(d.tag===4&&d.stateNode.containerInfo===g.containerInfo&&d.stateNode.implementation===g.implementation){n(h,d.sibling),d=o(d,g.children||[]),d.return=h,h=d;break e}else{n(h,d);break}else t(h,d);d=d.sibling}d=oa(g,h.mode,S),d.return=h,h=d}return a(h);case Mn:return L=g._init,O(h,d,L(g._payload),S)}if(Ao(g))return v(h,d,g,S);if(To(g))return N(h,d,g,S);el(h,g)}return typeof g=="string"&&g!==""||typeof g=="number"?(g=""+g,d!==null&&d.tag===6?(n(h,d.sibling),d=o(d,g),d.return=h,h=d):(n(h,d),d=ra(g,h.mode,S),d.return=h,h=d),a(h)):n(h,d)}return O}var oo=Vf(!0),qf=Vf(!1),jl=Xn(null),Ml=null,Vr=null,_u=null;function xu(){_u=Vr=Ml=null}function Eu(e){var t=jl.current;Pe(jl),e._currentValue=t}function Aa(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Jr(e,t){Ml=e,_u=Vr=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(ht=!0),e.firstContext=null)}function It(e){var t=e._currentValue;if(_u!==e)if(e={context:e,memoizedValue:t,next:null},Vr===null){if(Ml===null)throw Error(U(308));Vr=e,Ml.dependencies={lanes:0,firstContext:e}}else Vr=Vr.next=e;return t}var hr=null;function Tu(e){hr===null?hr=[e]:hr.push(e)}function Qf(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,Tu(t)):(n.next=o.next,o.next=n),t.interleaved=n,wn(e,r)}function wn(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var An=!1;function Nu(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Kf(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function gn(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Wn(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,ye&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,wn(e,n)}return o=r.interleaved,o===null?(t.next=t,Tu(r)):(t.next=o.next,o.next=t),r.interleaved=t,wn(e,n)}function fl(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,fu(e,n)}}function rd(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var a={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?o=i=a:i=i.next=a,n=n.next}while(n!==null);i===null?o=i=t:i=i.next=t}else o=i=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Al(e,t,n,r){var o=e.updateQueue;An=!1;var i=o.firstBaseUpdate,a=o.lastBaseUpdate,c=o.shared.pending;if(c!==null){o.shared.pending=null;var f=c,p=f.next;f.next=null,a===null?i=p:a.next=p,a=f;var w=e.alternate;w!==null&&(w=w.updateQueue,c=w.lastBaseUpdate,c!==a&&(c===null?w.firstBaseUpdate=p:c.next=p,w.lastBaseUpdate=f))}if(i!==null){var C=o.baseState;a=0,w=p=f=null,c=i;do{var x=c.lane,E=c.eventTime;if((r&x)===x){w!==null&&(w=w.next={eventTime:E,lane:0,tag:c.tag,payload:c.payload,callback:c.callback,next:null});e:{var v=e,N=c;switch(x=t,E=n,N.tag){case 1:if(v=N.payload,typeof v=="function"){C=v.call(E,C,x);break e}C=v;break e;case 3:v.flags=v.flags&-65537|128;case 0:if(v=N.payload,x=typeof v=="function"?v.call(E,C,x):v,x==null)break e;C=Ae({},C,x);break e;case 2:An=!0}}c.callback!==null&&c.lane!==0&&(e.flags|=64,x=o.effects,x===null?o.effects=[c]:x.push(c))}else E={eventTime:E,lane:x,tag:c.tag,payload:c.payload,callback:c.callback,next:null},w===null?(p=w=E,f=C):w=w.next=E,a|=x;if(c=c.next,c===null){if(c=o.shared.pending,c===null)break;x=c,c=x.next,x.next=null,o.lastBaseUpdate=x,o.shared.pending=null}}while(1);if(w===null&&(f=C),o.baseState=f,o.firstBaseUpdate=p,o.lastBaseUpdate=w,t=o.shared.interleaved,t!==null){o=t;do a|=o.lane,o=o.next;while(o!==t)}else i===null&&(o.shared.lanes=0);Cr|=a,e.lanes=a,e.memoizedState=C}}function od(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(U(191,o));o.call(r)}}}var gi={},on=Xn(gi),ii=Xn(gi),li=Xn(gi);function mr(e){if(e===gi)throw Error(U(174));return e}function bu(e,t){switch(Ee(li,t),Ee(ii,e),Ee(on,gi),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:ma(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=ma(t,e)}Pe(on),Ee(on,t)}function io(){Pe(on),Pe(ii),Pe(li)}function Gf(e){mr(li.current);var t=mr(on.current),n=ma(t,e.type);t!==n&&(Ee(ii,e),Ee(on,n))}function Pu(e){ii.current===e&&(Pe(on),Pe(ii))}var je=Xn(0);function Rl(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Xs=[];function Ou(){for(var e=0;e<Xs.length;e++)Xs[e]._workInProgressVersionPrimary=null;Xs.length=0}var pl=Cn.ReactCurrentDispatcher,Zs=Cn.ReactCurrentBatchConfig,kr=0,Me=null,Qe=null,Xe=null,zl=!1,Ho=!1,si=0,Ag=0;function rt(){throw Error(U(321))}function Iu(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Kt(e[n],t[n]))return!1;return!0}function Lu(e,t,n,r,o,i){if(kr=i,Me=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,pl.current=e===null||e.memoizedState===null?Fg:Ug,e=n(r,o),Ho){i=0;do{if(Ho=!1,si=0,25<=i)throw Error(U(301));i+=1,Xe=Qe=null,t.updateQueue=null,pl.current=$g,e=n(r,o)}while(Ho)}if(pl.current=Dl,t=Qe!==null&&Qe.next!==null,kr=0,Xe=Qe=Me=null,zl=!1,t)throw Error(U(300));return e}function ju(){var e=si!==0;return si=0,e}function tn(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Xe===null?Me.memoizedState=Xe=e:Xe=Xe.next=e,Xe}function Lt(){if(Qe===null){var e=Me.alternate;e=e!==null?e.memoizedState:null}else e=Qe.next;var t=Xe===null?Me.memoizedState:Xe.next;if(t!==null)Xe=t,Qe=e;else{if(e===null)throw Error(U(310));Qe=e,e={memoizedState:Qe.memoizedState,baseState:Qe.baseState,baseQueue:Qe.baseQueue,queue:Qe.queue,next:null},Xe===null?Me.memoizedState=Xe=e:Xe=Xe.next=e}return Xe}function ai(e,t){return typeof t=="function"?t(e):t}function Js(e){var t=Lt(),n=t.queue;if(n===null)throw Error(U(311));n.lastRenderedReducer=e;var r=Qe,o=r.baseQueue,i=n.pending;if(i!==null){if(o!==null){var a=o.next;o.next=i.next,i.next=a}r.baseQueue=o=i,n.pending=null}if(o!==null){i=o.next,r=r.baseState;var c=a=null,f=null,p=i;do{var w=p.lane;if((kr&w)===w)f!==null&&(f=f.next={lane:0,action:p.action,hasEagerState:p.hasEagerState,eagerState:p.eagerState,next:null}),r=p.hasEagerState?p.eagerState:e(r,p.action);else{var C={lane:w,action:p.action,hasEagerState:p.hasEagerState,eagerState:p.eagerState,next:null};f===null?(c=f=C,a=r):f=f.next=C,Me.lanes|=w,Cr|=w}p=p.next}while(p!==null&&p!==i);f===null?a=r:f.next=c,Kt(r,t.memoizedState)||(ht=!0),t.memoizedState=r,t.baseState=a,t.baseQueue=f,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do i=o.lane,Me.lanes|=i,Cr|=i,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function ea(e){var t=Lt(),n=t.queue;if(n===null)throw Error(U(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,i=t.memoizedState;if(o!==null){n.pending=null;var a=o=o.next;do i=e(i,a.action),a=a.next;while(a!==o);Kt(i,t.memoizedState)||(ht=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function Yf(){}function Xf(e,t){var n=Me,r=Lt(),o=t(),i=!Kt(r.memoizedState,o);if(i&&(r.memoizedState=o,ht=!0),r=r.queue,Mu(ep.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||Xe!==null&&Xe.memoizedState.tag&1){if(n.flags|=2048,ui(9,Jf.bind(null,n,r,o,t),void 0,null),Ze===null)throw Error(U(349));kr&30||Zf(n,t,o)}return o}function Zf(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=Me.updateQueue,t===null?(t={lastEffect:null,stores:null},Me.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Jf(e,t,n,r){t.value=n,t.getSnapshot=r,tp(t)&&np(e)}function ep(e,t,n){return n(function(){tp(t)&&np(e)})}function tp(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Kt(e,n)}catch{return!0}}function np(e){var t=wn(e,1);t!==null&&Qt(t,e,1,-1)}function id(e){var t=tn();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:ai,lastRenderedState:e},t.queue=e,e=e.dispatch=Dg.bind(null,Me,e),[t.memoizedState,e]}function ui(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=Me.updateQueue,t===null?(t={lastEffect:null,stores:null},Me.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function rp(){return Lt().memoizedState}function hl(e,t,n,r){var o=tn();Me.flags|=e,o.memoizedState=ui(1|t,n,void 0,r===void 0?null:r)}function Zl(e,t,n,r){var o=Lt();r=r===void 0?null:r;var i=void 0;if(Qe!==null){var a=Qe.memoizedState;if(i=a.destroy,r!==null&&Iu(r,a.deps)){o.memoizedState=ui(t,n,i,r);return}}Me.flags|=e,o.memoizedState=ui(1|t,n,i,r)}function ld(e,t){return hl(8390656,8,e,t)}function Mu(e,t){return Zl(2048,8,e,t)}function op(e,t){return Zl(4,2,e,t)}function ip(e,t){return Zl(4,4,e,t)}function lp(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function sp(e,t,n){return n=n!=null?n.concat([e]):null,Zl(4,4,lp.bind(null,t,e),n)}function Au(){}function ap(e,t){var n=Lt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Iu(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function up(e,t){var n=Lt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Iu(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function cp(e,t,n){return kr&21?(Kt(n,t)||(n=mf(),Me.lanes|=n,Cr|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,ht=!0),e.memoizedState=n)}function Rg(e,t){var n=_e;_e=n!==0&&4>n?n:4,e(!0);var r=Zs.transition;Zs.transition={};try{e(!1),t()}finally{_e=n,Zs.transition=r}}function dp(){return Lt().memoizedState}function zg(e,t,n){var r=qn(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},fp(e))pp(t,n);else if(n=Qf(e,t,n,r),n!==null){var o=at();Qt(n,e,r,o),hp(n,t,r)}}function Dg(e,t,n){var r=qn(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(fp(e))pp(t,o);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var a=t.lastRenderedState,c=i(a,n);if(o.hasEagerState=!0,o.eagerState=c,Kt(c,a)){var f=t.interleaved;f===null?(o.next=o,Tu(t)):(o.next=f.next,f.next=o),t.interleaved=o;return}}catch{}finally{}n=Qf(e,t,o,r),n!==null&&(o=at(),Qt(n,e,r,o),hp(n,t,r))}}function fp(e){var t=e.alternate;return e===Me||t!==null&&t===Me}function pp(e,t){Ho=zl=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function hp(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,fu(e,n)}}var Dl={readContext:It,useCallback:rt,useContext:rt,useEffect:rt,useImperativeHandle:rt,useInsertionEffect:rt,useLayoutEffect:rt,useMemo:rt,useReducer:rt,useRef:rt,useState:rt,useDebugValue:rt,useDeferredValue:rt,useTransition:rt,useMutableSource:rt,useSyncExternalStore:rt,useId:rt,unstable_isNewReconciler:!1},Fg={readContext:It,useCallback:function(e,t){return tn().memoizedState=[e,t===void 0?null:t],e},useContext:It,useEffect:ld,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,hl(4194308,4,lp.bind(null,t,e),n)},useLayoutEffect:function(e,t){return hl(4194308,4,e,t)},useInsertionEffect:function(e,t){return hl(4,2,e,t)},useMemo:function(e,t){var n=tn();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=tn();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=zg.bind(null,Me,e),[r.memoizedState,e]},useRef:function(e){var t=tn();return e={current:e},t.memoizedState=e},useState:id,useDebugValue:Au,useDeferredValue:function(e){return tn().memoizedState=e},useTransition:function(){var e=id(!1),t=e[0];return e=Rg.bind(null,e[1]),tn().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=Me,o=tn();if(Ie){if(n===void 0)throw Error(U(407));n=n()}else{if(n=t(),Ze===null)throw Error(U(349));kr&30||Zf(r,t,n)}o.memoizedState=n;var i={value:n,getSnapshot:t};return o.queue=i,ld(ep.bind(null,r,i,e),[e]),r.flags|=2048,ui(9,Jf.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=tn(),t=Ze.identifierPrefix;if(Ie){var n=mn,r=hn;n=(r&~(1<<32-qt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=si++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Ag++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Ug={readContext:It,useCallback:ap,useContext:It,useEffect:Mu,useImperativeHandle:sp,useInsertionEffect:op,useLayoutEffect:ip,useMemo:up,useReducer:Js,useRef:rp,useState:function(){return Js(ai)},useDebugValue:Au,useDeferredValue:function(e){var t=Lt();return cp(t,Qe.memoizedState,e)},useTransition:function(){var e=Js(ai)[0],t=Lt().memoizedState;return[e,t]},useMutableSource:Yf,useSyncExternalStore:Xf,useId:dp,unstable_isNewReconciler:!1},$g={readContext:It,useCallback:ap,useContext:It,useEffect:Mu,useImperativeHandle:sp,useInsertionEffect:op,useLayoutEffect:ip,useMemo:up,useReducer:ea,useRef:rp,useState:function(){return ea(ai)},useDebugValue:Au,useDeferredValue:function(e){var t=Lt();return Qe===null?t.memoizedState=e:cp(t,Qe.memoizedState,e)},useTransition:function(){var e=ea(ai)[0],t=Lt().memoizedState;return[e,t]},useMutableSource:Yf,useSyncExternalStore:Xf,useId:dp,unstable_isNewReconciler:!1};function Ht(e,t){if(e&&e.defaultProps){t=Ae({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Ra(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:Ae({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Jl={isMounted:function(e){return(e=e._reactInternals)?xr(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=at(),o=qn(e),i=gn(r,o);i.payload=t,n!=null&&(i.callback=n),t=Wn(e,i,o),t!==null&&(Qt(t,e,o,r),fl(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=at(),o=qn(e),i=gn(r,o);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=Wn(e,i,o),t!==null&&(Qt(t,e,o,r),fl(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=at(),r=qn(e),o=gn(n,r);o.tag=2,t!=null&&(o.callback=t),t=Wn(e,o,r),t!==null&&(Qt(t,e,r,n),fl(t,e,r))}};function sd(e,t,n,r,o,i,a){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,a):t.prototype&&t.prototype.isPureReactComponent?!ti(n,r)||!ti(o,i):!0}function mp(e,t,n){var r=!1,o=Gn,i=t.contextType;return typeof i=="object"&&i!==null?i=It(i):(o=gt(t)?vr:lt.current,r=t.contextTypes,i=(r=r!=null)?no(e,o):Gn),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Jl,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=i),t}function ad(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Jl.enqueueReplaceState(t,t.state,null)}function za(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},Nu(e);var i=t.contextType;typeof i=="object"&&i!==null?o.context=It(i):(i=gt(t)?vr:lt.current,o.context=no(e,i)),o.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(Ra(e,t,i,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&Jl.enqueueReplaceState(o,o.state,null),Al(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function lo(e,t){try{var n="",r=t;do n+=mm(r),r=r.return;while(r);var o=n}catch(i){o=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:o,digest:null}}function ta(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Da(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Bg=typeof WeakMap=="function"?WeakMap:Map;function gp(e,t,n){n=gn(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Ul||(Ul=!0,Ka=r),Da(e,t)},n}function yp(e,t,n){n=gn(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){Da(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){Da(e,t),typeof r!="function"&&(Vn===null?Vn=new Set([this]):Vn.add(this));var a=t.stack;this.componentDidCatch(t.value,{componentStack:a!==null?a:""})}),n}function ud(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Bg;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=ny.bind(null,e,t,n),t.then(e,e))}function cd(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function dd(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=gn(-1,1),t.tag=2,Wn(n,t,1))),n.lanes|=1),e)}var Hg=Cn.ReactCurrentOwner,ht=!1;function st(e,t,n,r){t.child=e===null?qf(t,null,n,r):oo(t,e.child,n,r)}function fd(e,t,n,r,o){n=n.render;var i=t.ref;return Jr(t,o),r=Lu(e,t,n,r,i,o),n=ju(),e!==null&&!ht?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,kn(e,t,o)):(Ie&&n&&ku(t),t.flags|=1,st(e,t,r,o),t.child)}function pd(e,t,n,r,o){if(e===null){var i=n.type;return typeof i=="function"&&!Hu(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,vp(e,t,i,r,o)):(e=vl(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&o)){var a=i.memoizedProps;if(n=n.compare,n=n!==null?n:ti,n(a,r)&&e.ref===t.ref)return kn(e,t,o)}return t.flags|=1,e=Qn(i,r),e.ref=t.ref,e.return=t,t.child=e}function vp(e,t,n,r,o){if(e!==null){var i=e.memoizedProps;if(ti(i,r)&&e.ref===t.ref)if(ht=!1,t.pendingProps=r=i,(e.lanes&o)!==0)e.flags&131072&&(ht=!0);else return t.lanes=e.lanes,kn(e,t,o)}return Fa(e,t,n,r,o)}function wp(e,t,n){var r=t.pendingProps,o=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Ee(Qr,kt),kt|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Ee(Qr,kt),kt|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,Ee(Qr,kt),kt|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,Ee(Qr,kt),kt|=r;return st(e,t,o,n),t.child}function kp(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Fa(e,t,n,r,o){var i=gt(n)?vr:lt.current;return i=no(t,i),Jr(t,o),n=Lu(e,t,n,r,i,o),r=ju(),e!==null&&!ht?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,kn(e,t,o)):(Ie&&r&&ku(t),t.flags|=1,st(e,t,n,o),t.child)}function hd(e,t,n,r,o){if(gt(n)){var i=!0;Ol(t)}else i=!1;if(Jr(t,o),t.stateNode===null)ml(e,t),mp(t,n,r),za(t,n,r,o),r=!0;else if(e===null){var a=t.stateNode,c=t.memoizedProps;a.props=c;var f=a.context,p=n.contextType;typeof p=="object"&&p!==null?p=It(p):(p=gt(n)?vr:lt.current,p=no(t,p));var w=n.getDerivedStateFromProps,C=typeof w=="function"||typeof a.getSnapshotBeforeUpdate=="function";C||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(c!==r||f!==p)&&ad(t,a,r,p),An=!1;var x=t.memoizedState;a.state=x,Al(t,r,a,o),f=t.memoizedState,c!==r||x!==f||mt.current||An?(typeof w=="function"&&(Ra(t,n,w,r),f=t.memoizedState),(c=An||sd(t,n,c,r,x,f,p))?(C||typeof a.UNSAFE_componentWillMount!="function"&&typeof a.componentWillMount!="function"||(typeof a.componentWillMount=="function"&&a.componentWillMount(),typeof a.UNSAFE_componentWillMount=="function"&&a.UNSAFE_componentWillMount()),typeof a.componentDidMount=="function"&&(t.flags|=4194308)):(typeof a.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=f),a.props=r,a.state=f,a.context=p,r=c):(typeof a.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{a=t.stateNode,Kf(e,t),c=t.memoizedProps,p=t.type===t.elementType?c:Ht(t.type,c),a.props=p,C=t.pendingProps,x=a.context,f=n.contextType,typeof f=="object"&&f!==null?f=It(f):(f=gt(n)?vr:lt.current,f=no(t,f));var E=n.getDerivedStateFromProps;(w=typeof E=="function"||typeof a.getSnapshotBeforeUpdate=="function")||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(c!==C||x!==f)&&ad(t,a,r,f),An=!1,x=t.memoizedState,a.state=x,Al(t,r,a,o);var v=t.memoizedState;c!==C||x!==v||mt.current||An?(typeof E=="function"&&(Ra(t,n,E,r),v=t.memoizedState),(p=An||sd(t,n,p,r,x,v,f)||!1)?(w||typeof a.UNSAFE_componentWillUpdate!="function"&&typeof a.componentWillUpdate!="function"||(typeof a.componentWillUpdate=="function"&&a.componentWillUpdate(r,v,f),typeof a.UNSAFE_componentWillUpdate=="function"&&a.UNSAFE_componentWillUpdate(r,v,f)),typeof a.componentDidUpdate=="function"&&(t.flags|=4),typeof a.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof a.componentDidUpdate!="function"||c===e.memoizedProps&&x===e.memoizedState||(t.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||c===e.memoizedProps&&x===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=v),a.props=r,a.state=v,a.context=f,r=p):(typeof a.componentDidUpdate!="function"||c===e.memoizedProps&&x===e.memoizedState||(t.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||c===e.memoizedProps&&x===e.memoizedState||(t.flags|=1024),r=!1)}return Ua(e,t,n,r,i,o)}function Ua(e,t,n,r,o,i){kp(e,t);var a=(t.flags&128)!==0;if(!r&&!a)return o&&Jc(t,n,!1),kn(e,t,i);r=t.stateNode,Hg.current=t;var c=a&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&a?(t.child=oo(t,e.child,null,i),t.child=oo(t,null,c,i)):st(e,t,c,i),t.memoizedState=r.state,o&&Jc(t,n,!0),t.child}function Cp(e){var t=e.stateNode;t.pendingContext?Zc(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Zc(e,t.context,!1),bu(e,t.containerInfo)}function md(e,t,n,r,o){return ro(),Su(o),t.flags|=256,st(e,t,n,r),t.child}var $a={dehydrated:null,treeContext:null,retryLane:0};function Ba(e){return{baseLanes:e,cachePool:null,transitions:null}}function Sp(e,t,n){var r=t.pendingProps,o=je.current,i=!1,a=(t.flags&128)!==0,c;if((c=a)||(c=e!==null&&e.memoizedState===null?!1:(o&2)!==0),c?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),Ee(je,o&1),e===null)return Ma(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(a=r.children,e=r.fallback,i?(r=t.mode,i=t.child,a={mode:"hidden",children:a},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=a):i=ns(a,r,0,null),e=yr(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=Ba(n),t.memoizedState=$a,e):Ru(t,a));if(o=e.memoizedState,o!==null&&(c=o.dehydrated,c!==null))return Wg(e,t,a,r,c,o,n);if(i){i=r.fallback,a=t.mode,o=e.child,c=o.sibling;var f={mode:"hidden",children:r.children};return!(a&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=f,t.deletions=null):(r=Qn(o,f),r.subtreeFlags=o.subtreeFlags&14680064),c!==null?i=Qn(c,i):(i=yr(i,a,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,a=e.child.memoizedState,a=a===null?Ba(n):{baseLanes:a.baseLanes|n,cachePool:null,transitions:a.transitions},i.memoizedState=a,i.childLanes=e.childLanes&~n,t.memoizedState=$a,r}return i=e.child,e=i.sibling,r=Qn(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Ru(e,t){return t=ns({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function tl(e,t,n,r){return r!==null&&Su(r),oo(t,e.child,null,n),e=Ru(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Wg(e,t,n,r,o,i,a){if(n)return t.flags&256?(t.flags&=-257,r=ta(Error(U(422))),tl(e,t,a,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,o=t.mode,r=ns({mode:"visible",children:r.children},o,0,null),i=yr(i,o,a,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&oo(t,e.child,null,a),t.child.memoizedState=Ba(a),t.memoizedState=$a,i);if(!(t.mode&1))return tl(e,t,a,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var c=r.dgst;return r=c,i=Error(U(419)),r=ta(i,r,void 0),tl(e,t,a,r)}if(c=(a&e.childLanes)!==0,ht||c){if(r=Ze,r!==null){switch(a&-a){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|a)?0:o,o!==0&&o!==i.retryLane&&(i.retryLane=o,wn(e,o),Qt(r,e,o,-1))}return Bu(),r=ta(Error(U(421))),tl(e,t,a,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=ry.bind(null,e),o._reactRetry=t,null):(e=i.treeContext,Ct=Hn(o.nextSibling),St=t,Ie=!0,Vt=null,e!==null&&(Nt[bt++]=hn,Nt[bt++]=mn,Nt[bt++]=wr,hn=e.id,mn=e.overflow,wr=t),t=Ru(t,r.children),t.flags|=4096,t)}function gd(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Aa(e.return,t,n)}function na(e,t,n,r,o){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=o)}function _p(e,t,n){var r=t.pendingProps,o=r.revealOrder,i=r.tail;if(st(e,t,r.children,n),r=je.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&gd(e,n,t);else if(e.tag===19)gd(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Ee(je,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&Rl(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),na(t,!1,o,n,i);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&Rl(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}na(t,!0,n,null,i);break;case"together":na(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function ml(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function kn(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Cr|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(U(153));if(t.child!==null){for(e=t.child,n=Qn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Qn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Vg(e,t,n){switch(t.tag){case 3:Cp(t),ro();break;case 5:Gf(t);break;case 1:gt(t.type)&&Ol(t);break;case 4:bu(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;Ee(jl,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(Ee(je,je.current&1),t.flags|=128,null):n&t.child.childLanes?Sp(e,t,n):(Ee(je,je.current&1),e=kn(e,t,n),e!==null?e.sibling:null);Ee(je,je.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return _p(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),Ee(je,je.current),r)break;return null;case 22:case 23:return t.lanes=0,wp(e,t,n)}return kn(e,t,n)}var xp,Ha,Ep,Tp;xp=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Ha=function(){};Ep=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,mr(on.current);var i=null;switch(n){case"input":o=da(e,o),r=da(e,r),i=[];break;case"select":o=Ae({},o,{value:void 0}),r=Ae({},r,{value:void 0}),i=[];break;case"textarea":o=ha(e,o),r=ha(e,r),i=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=bl)}ga(n,r);var a;n=null;for(p in o)if(!r.hasOwnProperty(p)&&o.hasOwnProperty(p)&&o[p]!=null)if(p==="style"){var c=o[p];for(a in c)c.hasOwnProperty(a)&&(n||(n={}),n[a]="")}else p!=="dangerouslySetInnerHTML"&&p!=="children"&&p!=="suppressContentEditableWarning"&&p!=="suppressHydrationWarning"&&p!=="autoFocus"&&(Ko.hasOwnProperty(p)?i||(i=[]):(i=i||[]).push(p,null));for(p in r){var f=r[p];if(c=o?.[p],r.hasOwnProperty(p)&&f!==c&&(f!=null||c!=null))if(p==="style")if(c){for(a in c)!c.hasOwnProperty(a)||f&&f.hasOwnProperty(a)||(n||(n={}),n[a]="");for(a in f)f.hasOwnProperty(a)&&c[a]!==f[a]&&(n||(n={}),n[a]=f[a])}else n||(i||(i=[]),i.push(p,n)),n=f;else p==="dangerouslySetInnerHTML"?(f=f?f.__html:void 0,c=c?c.__html:void 0,f!=null&&c!==f&&(i=i||[]).push(p,f)):p==="children"?typeof f!="string"&&typeof f!="number"||(i=i||[]).push(p,""+f):p!=="suppressContentEditableWarning"&&p!=="suppressHydrationWarning"&&(Ko.hasOwnProperty(p)?(f!=null&&p==="onScroll"&&be("scroll",e),i||c===f||(i=[])):(i=i||[]).push(p,f))}n&&(i=i||[]).push("style",n);var p=i;(t.updateQueue=p)&&(t.flags|=4)}};Tp=function(e,t,n,r){n!==r&&(t.flags|=4)};function Lo(e,t){if(!Ie)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function ot(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function qg(e,t,n){var r=t.pendingProps;switch(Cu(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ot(t),null;case 1:return gt(t.type)&&Pl(),ot(t),null;case 3:return r=t.stateNode,io(),Pe(mt),Pe(lt),Ou(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Ji(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Vt!==null&&(Xa(Vt),Vt=null))),Ha(e,t),ot(t),null;case 5:Pu(t);var o=mr(li.current);if(n=t.type,e!==null&&t.stateNode!=null)Ep(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(U(166));return ot(t),null}if(e=mr(on.current),Ji(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[nn]=t,r[oi]=i,e=(t.mode&1)!==0,n){case"dialog":be("cancel",r),be("close",r);break;case"iframe":case"object":case"embed":be("load",r);break;case"video":case"audio":for(o=0;o<zo.length;o++)be(zo[o],r);break;case"source":be("error",r);break;case"img":case"image":case"link":be("error",r),be("load",r);break;case"details":be("toggle",r);break;case"input":Ec(r,i),be("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},be("invalid",r);break;case"textarea":Nc(r,i),be("invalid",r)}ga(n,i),o=null;for(var a in i)if(i.hasOwnProperty(a)){var c=i[a];a==="children"?typeof c=="string"?r.textContent!==c&&(i.suppressHydrationWarning!==!0&&Zi(r.textContent,c,e),o=["children",c]):typeof c=="number"&&r.textContent!==""+c&&(i.suppressHydrationWarning!==!0&&Zi(r.textContent,c,e),o=["children",""+c]):Ko.hasOwnProperty(a)&&c!=null&&a==="onScroll"&&be("scroll",r)}switch(n){case"input":Wi(r),Tc(r,i,!0);break;case"textarea":Wi(r),bc(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=bl)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{a=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Jd(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=a.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=a.createElement(n,{is:r.is}):(e=a.createElement(n),n==="select"&&(a=e,r.multiple?a.multiple=!0:r.size&&(a.size=r.size))):e=a.createElementNS(e,n),e[nn]=t,e[oi]=r,xp(e,t,!1,!1),t.stateNode=e;e:{switch(a=ya(n,r),n){case"dialog":be("cancel",e),be("close",e),o=r;break;case"iframe":case"object":case"embed":be("load",e),o=r;break;case"video":case"audio":for(o=0;o<zo.length;o++)be(zo[o],e);o=r;break;case"source":be("error",e),o=r;break;case"img":case"image":case"link":be("error",e),be("load",e),o=r;break;case"details":be("toggle",e),o=r;break;case"input":Ec(e,r),o=da(e,r),be("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=Ae({},r,{value:void 0}),be("invalid",e);break;case"textarea":Nc(e,r),o=ha(e,r),be("invalid",e);break;default:o=r}ga(n,o),c=o;for(i in c)if(c.hasOwnProperty(i)){var f=c[i];i==="style"?nf(e,f):i==="dangerouslySetInnerHTML"?(f=f?f.__html:void 0,f!=null&&ef(e,f)):i==="children"?typeof f=="string"?(n!=="textarea"||f!=="")&&Go(e,f):typeof f=="number"&&Go(e,""+f):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(Ko.hasOwnProperty(i)?f!=null&&i==="onScroll"&&be("scroll",e):f!=null&&lu(e,i,f,a))}switch(n){case"input":Wi(e),Tc(e,r,!1);break;case"textarea":Wi(e),bc(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Kn(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?Gr(e,!!r.multiple,i,!1):r.defaultValue!=null&&Gr(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=bl)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return ot(t),null;case 6:if(e&&t.stateNode!=null)Tp(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(U(166));if(n=mr(li.current),mr(on.current),Ji(t)){if(r=t.stateNode,n=t.memoizedProps,r[nn]=t,(i=r.nodeValue!==n)&&(e=St,e!==null))switch(e.tag){case 3:Zi(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Zi(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[nn]=t,t.stateNode=r}return ot(t),null;case 13:if(Pe(je),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(Ie&&Ct!==null&&t.mode&1&&!(t.flags&128))Wf(),ro(),t.flags|=98560,i=!1;else if(i=Ji(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(U(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(U(317));i[nn]=t}else ro(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;ot(t),i=!1}else Vt!==null&&(Xa(Vt),Vt=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||je.current&1?Ke===0&&(Ke=3):Bu())),t.updateQueue!==null&&(t.flags|=4),ot(t),null);case 4:return io(),Ha(e,t),e===null&&ni(t.stateNode.containerInfo),ot(t),null;case 10:return Eu(t.type._context),ot(t),null;case 17:return gt(t.type)&&Pl(),ot(t),null;case 19:if(Pe(je),i=t.memoizedState,i===null)return ot(t),null;if(r=(t.flags&128)!==0,a=i.rendering,a===null)if(r)Lo(i,!1);else{if(Ke!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(a=Rl(e),a!==null){for(t.flags|=128,Lo(i,!1),r=a.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,a=i.alternate,a===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=a.childLanes,i.lanes=a.lanes,i.child=a.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=a.memoizedProps,i.memoizedState=a.memoizedState,i.updateQueue=a.updateQueue,i.type=a.type,e=a.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Ee(je,je.current&1|2),t.child}e=e.sibling}i.tail!==null&&Fe()>so&&(t.flags|=128,r=!0,Lo(i,!1),t.lanes=4194304)}else{if(!r)if(e=Rl(a),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Lo(i,!0),i.tail===null&&i.tailMode==="hidden"&&!a.alternate&&!Ie)return ot(t),null}else 2*Fe()-i.renderingStartTime>so&&n!==1073741824&&(t.flags|=128,r=!0,Lo(i,!1),t.lanes=4194304);i.isBackwards?(a.sibling=t.child,t.child=a):(n=i.last,n!==null?n.sibling=a:t.child=a,i.last=a)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=Fe(),t.sibling=null,n=je.current,Ee(je,r?n&1|2:n&1),t):(ot(t),null);case 22:case 23:return $u(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?kt&1073741824&&(ot(t),t.subtreeFlags&6&&(t.flags|=8192)):ot(t),null;case 24:return null;case 25:return null}throw Error(U(156,t.tag))}function Qg(e,t){switch(Cu(t),t.tag){case 1:return gt(t.type)&&Pl(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return io(),Pe(mt),Pe(lt),Ou(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Pu(t),null;case 13:if(Pe(je),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(U(340));ro()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return Pe(je),null;case 4:return io(),null;case 10:return Eu(t.type._context),null;case 22:case 23:return $u(),null;case 24:return null;default:return null}}var nl=!1,it=!1,Kg=typeof WeakSet=="function"?WeakSet:Set,X=null;function qr(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){De(e,t,r)}else n.current=null}function Wa(e,t,n){try{n()}catch(r){De(e,t,r)}}var yd=!1;function Gg(e,t){if(Na=El,e=If(),wu(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var a=0,c=-1,f=-1,p=0,w=0,C=e,x=null;t:for(;;){for(var E;C!==n||o!==0&&C.nodeType!==3||(c=a+o),C!==i||r!==0&&C.nodeType!==3||(f=a+r),C.nodeType===3&&(a+=C.nodeValue.length),(E=C.firstChild)!==null;)x=C,C=E;for(;;){if(C===e)break t;if(x===n&&++p===o&&(c=a),x===i&&++w===r&&(f=a),(E=C.nextSibling)!==null)break;C=x,x=C.parentNode}C=E}n=c===-1||f===-1?null:{start:c,end:f}}else n=null}n=n||{start:0,end:0}}else n=null;for(ba={focusedElem:e,selectionRange:n},El=!1,X=t;X!==null;)if(t=X,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,X=e;else for(;X!==null;){t=X;try{var v=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(v!==null){var N=v.memoizedProps,O=v.memoizedState,h=t.stateNode,d=h.getSnapshotBeforeUpdate(t.elementType===t.type?N:Ht(t.type,N),O);h.__reactInternalSnapshotBeforeUpdate=d}break;case 3:var g=t.stateNode.containerInfo;g.nodeType===1?g.textContent="":g.nodeType===9&&g.documentElement&&g.removeChild(g.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(U(163))}}catch(S){De(t,t.return,S)}if(e=t.sibling,e!==null){e.return=t.return,X=e;break}X=t.return}return v=yd,yd=!1,v}function Wo(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var i=o.destroy;o.destroy=void 0,i!==void 0&&Wa(t,n,i)}o=o.next}while(o!==r)}}function es(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Va(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Np(e){var t=e.alternate;t!==null&&(e.alternate=null,Np(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[nn],delete t[oi],delete t[Ia],delete t[Ig],delete t[Lg])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function bp(e){return e.tag===5||e.tag===3||e.tag===4}function vd(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||bp(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function qa(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=bl));else if(r!==4&&(e=e.child,e!==null))for(qa(e,t,n),e=e.sibling;e!==null;)qa(e,t,n),e=e.sibling}function Qa(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Qa(e,t,n),e=e.sibling;e!==null;)Qa(e,t,n),e=e.sibling}var Je=null,Wt=!1;function jn(e,t,n){for(n=n.child;n!==null;)Pp(e,t,n),n=n.sibling}function Pp(e,t,n){if(rn&&typeof rn.onCommitFiberUnmount=="function")try{rn.onCommitFiberUnmount(ql,n)}catch{}switch(n.tag){case 5:it||qr(n,t);case 6:var r=Je,o=Wt;Je=null,jn(e,t,n),Je=r,Wt=o,Je!==null&&(Wt?(e=Je,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Je.removeChild(n.stateNode));break;case 18:Je!==null&&(Wt?(e=Je,n=n.stateNode,e.nodeType===8?Gs(e.parentNode,n):e.nodeType===1&&Gs(e,n),Jo(e)):Gs(Je,n.stateNode));break;case 4:r=Je,o=Wt,Je=n.stateNode.containerInfo,Wt=!0,jn(e,t,n),Je=r,Wt=o;break;case 0:case 11:case 14:case 15:if(!it&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var i=o,a=i.destroy;i=i.tag,a!==void 0&&(i&2||i&4)&&Wa(n,t,a),o=o.next}while(o!==r)}jn(e,t,n);break;case 1:if(!it&&(qr(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(c){De(n,t,c)}jn(e,t,n);break;case 21:jn(e,t,n);break;case 22:n.mode&1?(it=(r=it)||n.memoizedState!==null,jn(e,t,n),it=r):jn(e,t,n);break;default:jn(e,t,n)}}function wd(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Kg),t.forEach(function(r){var o=oy.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function Bt(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var i=e,a=t,c=a;e:for(;c!==null;){switch(c.tag){case 5:Je=c.stateNode,Wt=!1;break e;case 3:Je=c.stateNode.containerInfo,Wt=!0;break e;case 4:Je=c.stateNode.containerInfo,Wt=!0;break e}c=c.return}if(Je===null)throw Error(U(160));Pp(i,a,o),Je=null,Wt=!1;var f=o.alternate;f!==null&&(f.return=null),o.return=null}catch(p){De(o,t,p)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Op(t,e),t=t.sibling}function Op(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Bt(t,e),en(e),r&4){try{Wo(3,e,e.return),es(3,e)}catch(N){De(e,e.return,N)}try{Wo(5,e,e.return)}catch(N){De(e,e.return,N)}}break;case 1:Bt(t,e),en(e),r&512&&n!==null&&qr(n,n.return);break;case 5:if(Bt(t,e),en(e),r&512&&n!==null&&qr(n,n.return),e.flags&32){var o=e.stateNode;try{Go(o,"")}catch(N){De(e,e.return,N)}}if(r&4&&(o=e.stateNode,o!=null)){var i=e.memoizedProps,a=n!==null?n.memoizedProps:i,c=e.type,f=e.updateQueue;if(e.updateQueue=null,f!==null)try{c==="input"&&i.type==="radio"&&i.name!=null&&Xd(o,i),ya(c,a);var p=ya(c,i);for(a=0;a<f.length;a+=2){var w=f[a],C=f[a+1];w==="style"?nf(o,C):w==="dangerouslySetInnerHTML"?ef(o,C):w==="children"?Go(o,C):lu(o,w,C,p)}switch(c){case"input":fa(o,i);break;case"textarea":Zd(o,i);break;case"select":var x=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var E=i.value;E!=null?Gr(o,!!i.multiple,E,!1):x!==!!i.multiple&&(i.defaultValue!=null?Gr(o,!!i.multiple,i.defaultValue,!0):Gr(o,!!i.multiple,i.multiple?[]:"",!1))}o[oi]=i}catch(N){De(e,e.return,N)}}break;case 6:if(Bt(t,e),en(e),r&4){if(e.stateNode===null)throw Error(U(162));o=e.stateNode,i=e.memoizedProps;try{o.nodeValue=i}catch(N){De(e,e.return,N)}}break;case 3:if(Bt(t,e),en(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Jo(t.containerInfo)}catch(N){De(e,e.return,N)}break;case 4:Bt(t,e),en(e);break;case 13:Bt(t,e),en(e),o=e.child,o.flags&8192&&(i=o.memoizedState!==null,o.stateNode.isHidden=i,!i||o.alternate!==null&&o.alternate.memoizedState!==null||(Fu=Fe())),r&4&&wd(e);break;case 22:if(w=n!==null&&n.memoizedState!==null,e.mode&1?(it=(p=it)||w,Bt(t,e),it=p):Bt(t,e),en(e),r&8192){if(p=e.memoizedState!==null,(e.stateNode.isHidden=p)&&!w&&e.mode&1)for(X=e,w=e.child;w!==null;){for(C=X=w;X!==null;){switch(x=X,E=x.child,x.tag){case 0:case 11:case 14:case 15:Wo(4,x,x.return);break;case 1:qr(x,x.return);var v=x.stateNode;if(typeof v.componentWillUnmount=="function"){r=x,n=x.return;try{t=r,v.props=t.memoizedProps,v.state=t.memoizedState,v.componentWillUnmount()}catch(N){De(r,n,N)}}break;case 5:qr(x,x.return);break;case 22:if(x.memoizedState!==null){Cd(C);continue}}E!==null?(E.return=x,X=E):Cd(C)}w=w.sibling}e:for(w=null,C=e;;){if(C.tag===5){if(w===null){w=C;try{o=C.stateNode,p?(i=o.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(c=C.stateNode,f=C.memoizedProps.style,a=f!=null&&f.hasOwnProperty("display")?f.display:null,c.style.display=tf("display",a))}catch(N){De(e,e.return,N)}}}else if(C.tag===6){if(w===null)try{C.stateNode.nodeValue=p?"":C.memoizedProps}catch(N){De(e,e.return,N)}}else if((C.tag!==22&&C.tag!==23||C.memoizedState===null||C===e)&&C.child!==null){C.child.return=C,C=C.child;continue}if(C===e)break e;for(;C.sibling===null;){if(C.return===null||C.return===e)break e;w===C&&(w=null),C=C.return}w===C&&(w=null),C.sibling.return=C.return,C=C.sibling}}break;case 19:Bt(t,e),en(e),r&4&&wd(e);break;case 21:break;default:Bt(t,e),en(e)}}function en(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(bp(n)){var r=n;break e}n=n.return}throw Error(U(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(Go(o,""),r.flags&=-33);var i=vd(e);Qa(e,i,o);break;case 3:case 4:var a=r.stateNode.containerInfo,c=vd(e);qa(e,c,a);break;default:throw Error(U(161))}}catch(f){De(e,e.return,f)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Yg(e,t,n){X=e,Ip(e)}function Ip(e,t,n){for(var r=(e.mode&1)!==0;X!==null;){var o=X,i=o.child;if(o.tag===22&&r){var a=o.memoizedState!==null||nl;if(!a){var c=o.alternate,f=c!==null&&c.memoizedState!==null||it;c=nl;var p=it;if(nl=a,(it=f)&&!p)for(X=o;X!==null;)a=X,f=a.child,a.tag===22&&a.memoizedState!==null?Sd(o):f!==null?(f.return=a,X=f):Sd(o);for(;i!==null;)X=i,Ip(i),i=i.sibling;X=o,nl=c,it=p}kd(e)}else o.subtreeFlags&8772&&i!==null?(i.return=o,X=i):kd(e)}}function kd(e){for(;X!==null;){var t=X;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:it||es(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!it)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:Ht(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&od(t,i,r);break;case 3:var a=t.updateQueue;if(a!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}od(t,a,n)}break;case 5:var c=t.stateNode;if(n===null&&t.flags&4){n=c;var f=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":f.autoFocus&&n.focus();break;case"img":f.src&&(n.src=f.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var p=t.alternate;if(p!==null){var w=p.memoizedState;if(w!==null){var C=w.dehydrated;C!==null&&Jo(C)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(U(163))}it||t.flags&512&&Va(t)}catch(x){De(t,t.return,x)}}if(t===e){X=null;break}if(n=t.sibling,n!==null){n.return=t.return,X=n;break}X=t.return}}function Cd(e){for(;X!==null;){var t=X;if(t===e){X=null;break}var n=t.sibling;if(n!==null){n.return=t.return,X=n;break}X=t.return}}function Sd(e){for(;X!==null;){var t=X;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{es(4,t)}catch(f){De(t,n,f)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(f){De(t,o,f)}}var i=t.return;try{Va(t)}catch(f){De(t,i,f)}break;case 5:var a=t.return;try{Va(t)}catch(f){De(t,a,f)}}}catch(f){De(t,t.return,f)}if(t===e){X=null;break}var c=t.sibling;if(c!==null){c.return=t.return,X=c;break}X=t.return}}var Xg=Math.ceil,Fl=Cn.ReactCurrentDispatcher,zu=Cn.ReactCurrentOwner,Ot=Cn.ReactCurrentBatchConfig,ye=0,Ze=null,Ve=null,et=0,kt=0,Qr=Xn(0),Ke=0,ci=null,Cr=0,ts=0,Du=0,Vo=null,pt=null,Fu=0,so=1/0,dn=null,Ul=!1,Ka=null,Vn=null,rl=!1,Fn=null,$l=0,qo=0,Ga=null,gl=-1,yl=0;function at(){return ye&6?Fe():gl!==-1?gl:gl=Fe()}function qn(e){return e.mode&1?ye&2&&et!==0?et&-et:Mg.transition!==null?(yl===0&&(yl=mf()),yl):(e=_e,e!==0||(e=window.event,e=e===void 0?16:Sf(e.type)),e):1}function Qt(e,t,n,r){if(50<qo)throw qo=0,Ga=null,Error(U(185));pi(e,n,r),(!(ye&2)||e!==Ze)&&(e===Ze&&(!(ye&2)&&(ts|=n),Ke===4&&zn(e,et)),yt(e,r),n===1&&ye===0&&!(t.mode&1)&&(so=Fe()+500,Xl&&Zn()))}function yt(e,t){var n=e.callbackNode;Mm(e,t);var r=xl(e,e===Ze?et:0);if(r===0)n!==null&&Ic(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Ic(n),t===1)e.tag===0?jg(_d.bind(null,e)):$f(_d.bind(null,e)),Pg(function(){!(ye&6)&&Zn()}),n=null;else{switch(gf(r)){case 1:n=du;break;case 4:n=pf;break;case 16:n=_l;break;case 536870912:n=hf;break;default:n=_l}n=Fp(n,Lp.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Lp(e,t){if(gl=-1,yl=0,ye&6)throw Error(U(327));var n=e.callbackNode;if(eo()&&e.callbackNode!==n)return null;var r=xl(e,e===Ze?et:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Bl(e,r);else{t=r;var o=ye;ye|=2;var i=Mp();(Ze!==e||et!==t)&&(dn=null,so=Fe()+500,gr(e,t));do try{ey();break}catch(c){jp(e,c)}while(1);xu(),Fl.current=i,ye=o,Ve!==null?t=0:(Ze=null,et=0,t=Ke)}if(t!==0){if(t===2&&(o=Sa(e),o!==0&&(r=o,t=Ya(e,o))),t===1)throw n=ci,gr(e,0),zn(e,r),yt(e,Fe()),n;if(t===6)zn(e,r);else{if(o=e.current.alternate,!(r&30)&&!Zg(o)&&(t=Bl(e,r),t===2&&(i=Sa(e),i!==0&&(r=i,t=Ya(e,i))),t===1))throw n=ci,gr(e,0),zn(e,r),yt(e,Fe()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(U(345));case 2:fr(e,pt,dn);break;case 3:if(zn(e,r),(r&130023424)===r&&(t=Fu+500-Fe(),10<t)){if(xl(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){at(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=Oa(fr.bind(null,e,pt,dn),t);break}fr(e,pt,dn);break;case 4:if(zn(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var a=31-qt(r);i=1<<a,a=t[a],a>o&&(o=a),r&=~i}if(r=o,r=Fe()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Xg(r/1960))-r,10<r){e.timeoutHandle=Oa(fr.bind(null,e,pt,dn),r);break}fr(e,pt,dn);break;case 5:fr(e,pt,dn);break;default:throw Error(U(329))}}}return yt(e,Fe()),e.callbackNode===n?Lp.bind(null,e):null}function Ya(e,t){var n=Vo;return e.current.memoizedState.isDehydrated&&(gr(e,t).flags|=256),e=Bl(e,t),e!==2&&(t=pt,pt=n,t!==null&&Xa(t)),e}function Xa(e){pt===null?pt=e:pt.push.apply(pt,e)}function Zg(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],i=o.getSnapshot;o=o.value;try{if(!Kt(i(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function zn(e,t){for(t&=~Du,t&=~ts,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-qt(t),r=1<<n;e[n]=-1,t&=~r}}function _d(e){if(ye&6)throw Error(U(327));eo();var t=xl(e,0);if(!(t&1))return yt(e,Fe()),null;var n=Bl(e,t);if(e.tag!==0&&n===2){var r=Sa(e);r!==0&&(t=r,n=Ya(e,r))}if(n===1)throw n=ci,gr(e,0),zn(e,t),yt(e,Fe()),n;if(n===6)throw Error(U(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,fr(e,pt,dn),yt(e,Fe()),null}function Uu(e,t){var n=ye;ye|=1;try{return e(t)}finally{ye=n,ye===0&&(so=Fe()+500,Xl&&Zn())}}function Sr(e){Fn!==null&&Fn.tag===0&&!(ye&6)&&eo();var t=ye;ye|=1;var n=Ot.transition,r=_e;try{if(Ot.transition=null,_e=1,e)return e()}finally{_e=r,Ot.transition=n,ye=t,!(ye&6)&&Zn()}}function $u(){kt=Qr.current,Pe(Qr)}function gr(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,bg(n)),Ve!==null)for(n=Ve.return;n!==null;){var r=n;switch(Cu(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Pl();break;case 3:io(),Pe(mt),Pe(lt),Ou();break;case 5:Pu(r);break;case 4:io();break;case 13:Pe(je);break;case 19:Pe(je);break;case 10:Eu(r.type._context);break;case 22:case 23:$u()}n=n.return}if(Ze=e,Ve=e=Qn(e.current,null),et=kt=t,Ke=0,ci=null,Du=ts=Cr=0,pt=Vo=null,hr!==null){for(t=0;t<hr.length;t++)if(n=hr[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,i=n.pending;if(i!==null){var a=i.next;i.next=o,r.next=a}n.pending=r}hr=null}return e}function jp(e,t){do{var n=Ve;try{if(xu(),pl.current=Dl,zl){for(var r=Me.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}zl=!1}if(kr=0,Xe=Qe=Me=null,Ho=!1,si=0,zu.current=null,n===null||n.return===null){Ke=1,ci=t,Ve=null;break}e:{var i=e,a=n.return,c=n,f=t;if(t=et,c.flags|=32768,f!==null&&typeof f=="object"&&typeof f.then=="function"){var p=f,w=c,C=w.tag;if(!(w.mode&1)&&(C===0||C===11||C===15)){var x=w.alternate;x?(w.updateQueue=x.updateQueue,w.memoizedState=x.memoizedState,w.lanes=x.lanes):(w.updateQueue=null,w.memoizedState=null)}var E=cd(a);if(E!==null){E.flags&=-257,dd(E,a,c,i,t),E.mode&1&&ud(i,p,t),t=E,f=p;var v=t.updateQueue;if(v===null){var N=new Set;N.add(f),t.updateQueue=N}else v.add(f);break e}else{if(!(t&1)){ud(i,p,t),Bu();break e}f=Error(U(426))}}else if(Ie&&c.mode&1){var O=cd(a);if(O!==null){!(O.flags&65536)&&(O.flags|=256),dd(O,a,c,i,t),Su(lo(f,c));break e}}i=f=lo(f,c),Ke!==4&&(Ke=2),Vo===null?Vo=[i]:Vo.push(i),i=a;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var h=gp(i,f,t);rd(i,h);break e;case 1:c=f;var d=i.type,g=i.stateNode;if(!(i.flags&128)&&(typeof d.getDerivedStateFromError=="function"||g!==null&&typeof g.componentDidCatch=="function"&&(Vn===null||!Vn.has(g)))){i.flags|=65536,t&=-t,i.lanes|=t;var S=yp(i,c,t);rd(i,S);break e}}i=i.return}while(i!==null)}Rp(n)}catch(P){t=P,Ve===n&&n!==null&&(Ve=n=n.return);continue}break}while(1)}function Mp(){var e=Fl.current;return Fl.current=Dl,e===null?Dl:e}function Bu(){(Ke===0||Ke===3||Ke===2)&&(Ke=4),Ze===null||!(Cr&268435455)&&!(ts&268435455)||zn(Ze,et)}function Bl(e,t){var n=ye;ye|=2;var r=Mp();(Ze!==e||et!==t)&&(dn=null,gr(e,t));do try{Jg();break}catch(o){jp(e,o)}while(1);if(xu(),ye=n,Fl.current=r,Ve!==null)throw Error(U(261));return Ze=null,et=0,Ke}function Jg(){for(;Ve!==null;)Ap(Ve)}function ey(){for(;Ve!==null&&!Em();)Ap(Ve)}function Ap(e){var t=Dp(e.alternate,e,kt);e.memoizedProps=e.pendingProps,t===null?Rp(e):Ve=t,zu.current=null}function Rp(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Qg(n,t),n!==null){n.flags&=32767,Ve=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Ke=6,Ve=null;return}}else if(n=qg(n,t,kt),n!==null){Ve=n;return}if(t=t.sibling,t!==null){Ve=t;return}Ve=t=e}while(t!==null);Ke===0&&(Ke=5)}function fr(e,t,n){var r=_e,o=Ot.transition;try{Ot.transition=null,_e=1,ty(e,t,n,r)}finally{Ot.transition=o,_e=r}return null}function ty(e,t,n,r){do eo();while(Fn!==null);if(ye&6)throw Error(U(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(U(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(Am(e,i),e===Ze&&(Ve=Ze=null,et=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||rl||(rl=!0,Fp(_l,function(){return eo(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=Ot.transition,Ot.transition=null;var a=_e;_e=1;var c=ye;ye|=4,zu.current=null,Gg(e,n),Op(n,e),Cg(ba),El=!!Na,ba=Na=null,e.current=n,Yg(n),Tm(),ye=c,_e=a,Ot.transition=i}else e.current=n;if(rl&&(rl=!1,Fn=e,$l=o),i=e.pendingLanes,i===0&&(Vn=null),Pm(n.stateNode),yt(e,Fe()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(Ul)throw Ul=!1,e=Ka,Ka=null,e;return $l&1&&e.tag!==0&&eo(),i=e.pendingLanes,i&1?e===Ga?qo++:(qo=0,Ga=e):qo=0,Zn(),null}function eo(){if(Fn!==null){var e=gf($l),t=Ot.transition,n=_e;try{if(Ot.transition=null,_e=16>e?16:e,Fn===null)var r=!1;else{if(e=Fn,Fn=null,$l=0,ye&6)throw Error(U(331));var o=ye;for(ye|=4,X=e.current;X!==null;){var i=X,a=i.child;if(X.flags&16){var c=i.deletions;if(c!==null){for(var f=0;f<c.length;f++){var p=c[f];for(X=p;X!==null;){var w=X;switch(w.tag){case 0:case 11:case 15:Wo(8,w,i)}var C=w.child;if(C!==null)C.return=w,X=C;else for(;X!==null;){w=X;var x=w.sibling,E=w.return;if(Np(w),w===p){X=null;break}if(x!==null){x.return=E,X=x;break}X=E}}}var v=i.alternate;if(v!==null){var N=v.child;if(N!==null){v.child=null;do{var O=N.sibling;N.sibling=null,N=O}while(N!==null)}}X=i}}if(i.subtreeFlags&2064&&a!==null)a.return=i,X=a;else e:for(;X!==null;){if(i=X,i.flags&2048)switch(i.tag){case 0:case 11:case 15:Wo(9,i,i.return)}var h=i.sibling;if(h!==null){h.return=i.return,X=h;break e}X=i.return}}var d=e.current;for(X=d;X!==null;){a=X;var g=a.child;if(a.subtreeFlags&2064&&g!==null)g.return=a,X=g;else e:for(a=d;X!==null;){if(c=X,c.flags&2048)try{switch(c.tag){case 0:case 11:case 15:es(9,c)}}catch(P){De(c,c.return,P)}if(c===a){X=null;break e}var S=c.sibling;if(S!==null){S.return=c.return,X=S;break e}X=c.return}}if(ye=o,Zn(),rn&&typeof rn.onPostCommitFiberRoot=="function")try{rn.onPostCommitFiberRoot(ql,e)}catch{}r=!0}return r}finally{_e=n,Ot.transition=t}}return!1}function xd(e,t,n){t=lo(n,t),t=gp(e,t,1),e=Wn(e,t,1),t=at(),e!==null&&(pi(e,1,t),yt(e,t))}function De(e,t,n){if(e.tag===3)xd(e,e,n);else for(;t!==null;){if(t.tag===3){xd(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Vn===null||!Vn.has(r))){e=lo(n,e),e=yp(t,e,1),t=Wn(t,e,1),e=at(),t!==null&&(pi(t,1,e),yt(t,e));break}}t=t.return}}function ny(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=at(),e.pingedLanes|=e.suspendedLanes&n,Ze===e&&(et&n)===n&&(Ke===4||Ke===3&&(et&130023424)===et&&500>Fe()-Fu?gr(e,0):Du|=n),yt(e,t)}function zp(e,t){t===0&&(e.mode&1?(t=Qi,Qi<<=1,!(Qi&130023424)&&(Qi=4194304)):t=1);var n=at();e=wn(e,t),e!==null&&(pi(e,t,n),yt(e,n))}function ry(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),zp(e,n)}function oy(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(U(314))}r!==null&&r.delete(t),zp(e,n)}var Dp;Dp=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||mt.current)ht=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return ht=!1,Vg(e,t,n);ht=!!(e.flags&131072)}else ht=!1,Ie&&t.flags&1048576&&Bf(t,Ll,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;ml(e,t),e=t.pendingProps;var o=no(t,lt.current);Jr(t,n),o=Lu(null,t,r,e,o,n);var i=ju();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,gt(r)?(i=!0,Ol(t)):i=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,Nu(t),o.updater=Jl,t.stateNode=o,o._reactInternals=t,za(t,r,e,n),t=Ua(null,t,r,!0,i,n)):(t.tag=0,Ie&&i&&ku(t),st(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(ml(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=ly(r),e=Ht(r,e),o){case 0:t=Fa(null,t,r,e,n);break e;case 1:t=hd(null,t,r,e,n);break e;case 11:t=fd(null,t,r,e,n);break e;case 14:t=pd(null,t,r,Ht(r.type,e),n);break e}throw Error(U(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ht(r,o),Fa(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ht(r,o),hd(e,t,r,o,n);case 3:e:{if(Cp(t),e===null)throw Error(U(387));r=t.pendingProps,i=t.memoizedState,o=i.element,Kf(e,t),Al(t,r,null,n);var a=t.memoizedState;if(r=a.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:a.cache,pendingSuspenseBoundaries:a.pendingSuspenseBoundaries,transitions:a.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){o=lo(Error(U(423)),t),t=md(e,t,r,n,o);break e}else if(r!==o){o=lo(Error(U(424)),t),t=md(e,t,r,n,o);break e}else for(Ct=Hn(t.stateNode.containerInfo.firstChild),St=t,Ie=!0,Vt=null,n=qf(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(ro(),r===o){t=kn(e,t,n);break e}st(e,t,r,n)}t=t.child}return t;case 5:return Gf(t),e===null&&Ma(t),r=t.type,o=t.pendingProps,i=e!==null?e.memoizedProps:null,a=o.children,Pa(r,o)?a=null:i!==null&&Pa(r,i)&&(t.flags|=32),kp(e,t),st(e,t,a,n),t.child;case 6:return e===null&&Ma(t),null;case 13:return Sp(e,t,n);case 4:return bu(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=oo(t,null,r,n):st(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ht(r,o),fd(e,t,r,o,n);case 7:return st(e,t,t.pendingProps,n),t.child;case 8:return st(e,t,t.pendingProps.children,n),t.child;case 12:return st(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,i=t.memoizedProps,a=o.value,Ee(jl,r._currentValue),r._currentValue=a,i!==null)if(Kt(i.value,a)){if(i.children===o.children&&!mt.current){t=kn(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var c=i.dependencies;if(c!==null){a=i.child;for(var f=c.firstContext;f!==null;){if(f.context===r){if(i.tag===1){f=gn(-1,n&-n),f.tag=2;var p=i.updateQueue;if(p!==null){p=p.shared;var w=p.pending;w===null?f.next=f:(f.next=w.next,w.next=f),p.pending=f}}i.lanes|=n,f=i.alternate,f!==null&&(f.lanes|=n),Aa(i.return,n,t),c.lanes|=n;break}f=f.next}}else if(i.tag===10)a=i.type===t.type?null:i.child;else if(i.tag===18){if(a=i.return,a===null)throw Error(U(341));a.lanes|=n,c=a.alternate,c!==null&&(c.lanes|=n),Aa(a,n,t),a=i.sibling}else a=i.child;if(a!==null)a.return=i;else for(a=i;a!==null;){if(a===t){a=null;break}if(i=a.sibling,i!==null){i.return=a.return,a=i;break}a=a.return}i=a}st(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,Jr(t,n),o=It(o),r=r(o),t.flags|=1,st(e,t,r,n),t.child;case 14:return r=t.type,o=Ht(r,t.pendingProps),o=Ht(r.type,o),pd(e,t,r,o,n);case 15:return vp(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ht(r,o),ml(e,t),t.tag=1,gt(r)?(e=!0,Ol(t)):e=!1,Jr(t,n),mp(t,r,o),za(t,r,o,n),Ua(null,t,r,!0,e,n);case 19:return _p(e,t,n);case 22:return wp(e,t,n)}throw Error(U(156,t.tag))};function Fp(e,t){return ff(e,t)}function iy(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Pt(e,t,n,r){return new iy(e,t,n,r)}function Hu(e){return e=e.prototype,!(!e||!e.isReactComponent)}function ly(e){if(typeof e=="function")return Hu(e)?1:0;if(e!=null){if(e=e.$$typeof,e===au)return 11;if(e===uu)return 14}return 2}function Qn(e,t){var n=e.alternate;return n===null?(n=Pt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function vl(e,t,n,r,o,i){var a=2;if(r=e,typeof e=="function")Hu(e)&&(a=1);else if(typeof e=="string")a=5;else e:switch(e){case zr:return yr(n.children,o,i,t);case su:a=8,o|=8;break;case sa:return e=Pt(12,n,t,o|2),e.elementType=sa,e.lanes=i,e;case aa:return e=Pt(13,n,t,o),e.elementType=aa,e.lanes=i,e;case ua:return e=Pt(19,n,t,o),e.elementType=ua,e.lanes=i,e;case Kd:return ns(n,o,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case qd:a=10;break e;case Qd:a=9;break e;case au:a=11;break e;case uu:a=14;break e;case Mn:a=16,r=null;break e}throw Error(U(130,e==null?e:typeof e,""))}return t=Pt(a,n,t,o),t.elementType=e,t.type=r,t.lanes=i,t}function yr(e,t,n,r){return e=Pt(7,e,r,t),e.lanes=n,e}function ns(e,t,n,r){return e=Pt(22,e,r,t),e.elementType=Kd,e.lanes=n,e.stateNode={isHidden:!1},e}function ra(e,t,n){return e=Pt(6,e,null,t),e.lanes=n,e}function oa(e,t,n){return t=Pt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function sy(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Ds(0),this.expirationTimes=Ds(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ds(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function Wu(e,t,n,r,o,i,a,c,f){return e=new sy(e,t,n,c,f),t===1?(t=1,i===!0&&(t|=8)):t=0,i=Pt(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Nu(i),e}function ay(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Rr,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Up(e){if(!e)return Gn;e=e._reactInternals;e:{if(xr(e)!==e||e.tag!==1)throw Error(U(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(gt(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(U(171))}if(e.tag===1){var n=e.type;if(gt(n))return Uf(e,n,t)}return t}function $p(e,t,n,r,o,i,a,c,f){return e=Wu(n,r,!0,e,o,i,a,c,f),e.context=Up(null),n=e.current,r=at(),o=qn(n),i=gn(r,o),i.callback=t??null,Wn(n,i,o),e.current.lanes=o,pi(e,o,r),yt(e,r),e}function rs(e,t,n,r){var o=t.current,i=at(),a=qn(o);return n=Up(n),t.context===null?t.context=n:t.pendingContext=n,t=gn(i,a),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Wn(o,t,a),e!==null&&(Qt(e,o,a,i),fl(e,o,a)),a}function Hl(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Ed(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Vu(e,t){Ed(e,t),(e=e.alternate)&&Ed(e,t)}function uy(){return null}var Bp=typeof reportError=="function"?reportError:function(e){console.error(e)};function qu(e){this._internalRoot=e}os.prototype.render=qu.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(U(409));rs(e,t,null,null)};os.prototype.unmount=qu.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Sr(function(){rs(null,e,null,null)}),t[vn]=null}};function os(e){this._internalRoot=e}os.prototype.unstable_scheduleHydration=function(e){if(e){var t=wf();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Rn.length&&t!==0&&t<Rn[n].priority;n++);Rn.splice(n,0,e),n===0&&Cf(e)}};function Qu(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function is(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Td(){}function cy(e,t,n,r,o){if(o){if(typeof r=="function"){var i=r;r=function(){var p=Hl(a);i.call(p)}}var a=$p(t,r,e,0,null,!1,!1,"",Td);return e._reactRootContainer=a,e[vn]=a.current,ni(e.nodeType===8?e.parentNode:e),Sr(),a}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var c=r;r=function(){var p=Hl(f);c.call(p)}}var f=Wu(e,0,!1,null,null,!1,!1,"",Td);return e._reactRootContainer=f,e[vn]=f.current,ni(e.nodeType===8?e.parentNode:e),Sr(function(){rs(t,f,n,r)}),f}function ls(e,t,n,r,o){var i=n._reactRootContainer;if(i){var a=i;if(typeof o=="function"){var c=o;o=function(){var f=Hl(a);c.call(f)}}rs(t,a,e,o)}else a=cy(n,t,e,o,r);return Hl(a)}yf=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Ro(t.pendingLanes);n!==0&&(fu(t,n|1),yt(t,Fe()),!(ye&6)&&(so=Fe()+500,Zn()))}break;case 13:Sr(function(){var r=wn(e,1);if(r!==null){var o=at();Qt(r,e,1,o)}}),Vu(e,1)}};pu=function(e){if(e.tag===13){var t=wn(e,134217728);if(t!==null){var n=at();Qt(t,e,134217728,n)}Vu(e,134217728)}};vf=function(e){if(e.tag===13){var t=qn(e),n=wn(e,t);if(n!==null){var r=at();Qt(n,e,t,r)}Vu(e,t)}};wf=function(){return _e};kf=function(e,t){var n=_e;try{return _e=e,t()}finally{_e=n}};wa=function(e,t,n){switch(t){case"input":if(fa(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=Yl(r);if(!o)throw Error(U(90));Yd(r),fa(r,o)}}}break;case"textarea":Zd(e,n);break;case"select":t=n.value,t!=null&&Gr(e,!!n.multiple,t,!1)}};lf=Uu;sf=Sr;var dy={usingClientEntryPoint:!1,Events:[mi,$r,Yl,rf,of,Uu]},jo={findFiberByHostInstance:pr,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},fy={bundleType:jo.bundleType,version:jo.version,rendererPackageName:jo.rendererPackageName,rendererConfig:jo.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Cn.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=cf(e),e===null?null:e.stateNode},findFiberByHostInstance:jo.findFiberByHostInstance||uy,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ol=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ol.isDisabled&&ol.supportsFiber)try{ql=ol.inject(fy),rn=ol}catch{}}xt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=dy;xt.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Qu(t))throw Error(U(200));return ay(e,t,null,n)};xt.createRoot=function(e,t){if(!Qu(e))throw Error(U(299));var n=!1,r="",o=Bp;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=Wu(e,1,!1,null,null,n,!1,r,o),e[vn]=t.current,ni(e.nodeType===8?e.parentNode:e),new qu(t)};xt.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(U(188)):(e=Object.keys(e).join(","),Error(U(268,e)));return e=cf(t),e=e===null?null:e.stateNode,e};xt.flushSync=function(e){return Sr(e)};xt.hydrate=function(e,t,n){if(!is(t))throw Error(U(200));return ls(null,e,t,!0,n)};xt.hydrateRoot=function(e,t,n){if(!Qu(e))throw Error(U(405));var r=n!=null&&n.hydratedSources||null,o=!1,i="",a=Bp;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(a=n.onRecoverableError)),t=$p(t,null,e,1,n??null,o,!1,i,a),e[vn]=t.current,ni(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new os(t)};xt.render=function(e,t,n){if(!is(t))throw Error(U(200));return ls(null,e,t,!1,n)};xt.unmountComponentAtNode=function(e){if(!is(e))throw Error(U(40));return e._reactRootContainer?(Sr(function(){ls(null,null,e,!1,function(){e._reactRootContainer=null,e[vn]=null})}),!0):!1};xt.unstable_batchedUpdates=Uu;xt.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!is(n))throw Error(U(200));if(e==null||e._reactInternals===void 0)throw Error(U(38));return ls(e,t,n,!1,r)};xt.version="18.3.1-next-f1338f8080-20240426";function Hp(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Hp)}catch(e){console.error(e)}}Hp(),Bd.exports=xt;var py=Bd.exports,Wp,Nd=py;Wp=Nd.createRoot,Nd.hydrateRoot;let Vp=logseq.isMainUIVisible;function hy(e,t){return logseq.on(e,t),()=>{logseq.off(e,t)}}const my=e=>hy("ui:visible:changed",({visible:t})=>{Vp=t,e()}),gy=()=>Ud.useSyncExternalStore(my,()=>Vp),fn={apiUrl:"https://api.openai.com/v1/chat/completions",apiKey:"",modelName:"gpt-3.5-turbo",temperature:.7,maxTokens:2e3,enableHistory:!1,customPrompts:[{name:"总结",prompt:"请总结以下内容的要点："},{name:"扩展",prompt:"请基于以下内容进行扩展和补充："}],keybindings:{openChat:{binding:"ctrl+g",mac:"cmd+g",description:"打开AI聊天"},quickReply:{binding:"ctrl+shift+g",mac:"cmd+shift+g",description:"快速AI回复"}}};async function yy(){const e=logseq.settings||{},t=Object.assign({},fn,e);return Array.isArray(t.customPrompts)||(t.customPrompts=fn.customPrompts),t.temperature===void 0&&(t.temperature=fn.temperature),t.maxTokens===void 0&&(t.maxTokens=fn.maxTokens),t.keybindings?(t.keybindings.openChat||(t.keybindings.openChat=fn.keybindings.openChat),t.keybindings.quickReply||(t.keybindings.quickReply=fn.keybindings.quickReply)):t.keybindings=fn.keybindings,await logseq.updateSettings(t),t}async function vy(e){await logseq.updateSettings(e)}async function ss(){const e=logseq.settings||{};return Object.assign({},fn,e)}function wy(e){if(!e||typeof e!="string")return{isValid:!1,error:"快捷键不能为空"};const t=e.trim();if(!t)return{isValid:!1,error:"快捷键不能为空"};const n=["ctrl","cmd","alt","shift","meta"],r=t.toLowerCase().split("+");if(r.length===0)return{isValid:!1,error:"快捷键格式无效"};const o=r[r.length-1];if(!o||o.length===0)return{isValid:!1,error:"缺少按键部分"};const i=r.slice(0,-1);for(const f of i)if(!n.includes(f))return{isValid:!1,error:`无效的修饰键: ${f}`};return[...new Set(i)].length!==i.length?{isValid:!1,error:"不能有重复的修饰键"}:/^[a-z0-9]$|^f[1-9]$|^f1[0-2]$|^(space|enter|tab|escape|backspace|delete|home|end|pageup|pagedown|insert|up|down|left|right)$/.test(o)?{isValid:!0}:{isValid:!1,error:`无效的按键: ${o}`}}function bd(e,t=!1){const n=t?e.mac:e.binding;return n?n.split("+").map(r=>{switch(r.toLowerCase()){case"ctrl":return t?"⌃":"Ctrl";case"cmd":return"⌘";case"alt":return t?"⌥":"Alt";case"shift":return t?"⇧":"Shift";case"meta":return t?"⌘":"Win";default:return r.toUpperCase()}}).join(t?"":"+"):""}function ky(e,t){const n=e.toLowerCase().trim();if(!n)return{hasConflict:!1};const r=["ctrl+c","cmd+c","ctrl+v","cmd+v","ctrl+x","cmd+x","ctrl+z","cmd+z","ctrl+y","cmd+y","ctrl+a","cmd+a","ctrl+s","cmd+s","ctrl+f","cmd+f","ctrl+n","cmd+n","ctrl+o","cmd+o","ctrl+p","cmd+p","ctrl+w","cmd+w","ctrl+t","cmd+t","ctrl+r","cmd+r","ctrl+l","cmd+l","ctrl+d","cmd+d","ctrl+h","cmd+h","ctrl+j","cmd+j","ctrl+k","cmd+k","ctrl+u","cmd+u","alt+f4","cmd+q","f5","cmd+r","f11","f12"],o=["ctrl+shift+p","cmd+shift+p","ctrl+k","cmd+k","ctrl+shift+k","cmd+shift+k","ctrl+/","cmd+/","ctrl+shift+/","cmd+shift+/","ctrl+e","cmd+e","ctrl+shift+e","cmd+shift+e"];if(r.includes(n))return{hasConflict:!0,type:"system",message:`可能与系统快捷键冲突: ${n}`};if(o.includes(n))return{hasConflict:!0,type:"system",message:`可能与Logseq快捷键冲突: ${n}`};if(t){for(const[i,a]of Object.entries(t))if(a.binding.toLowerCase()===n||a.mac.toLowerCase()===n)return{hasConflict:!0,type:"internal",message:`与 "${a.description}" 快捷键冲突`}}return{hasConflict:!1}}class Kr{static instance;currentTheme;listeners=[];mediaQuery;constructor(){this.mediaQuery=window.matchMedia("(prefers-color-scheme: dark)"),this.currentTheme={mode:"auto",isDark:!1,systemPrefersDark:this.mediaQuery.matches},this.mediaQuery.addEventListener("change",this.handleSystemThemeChange.bind(this)),this.initializeTheme()}static getInstance(){return Kr.instance||(Kr.instance=new Kr),Kr.instance}async initializeTheme(){try{const t=await logseq.App.getStateFromStore("ui/theme");this.currentTheme.isDark=t==="dark",this.currentTheme.mode=t==="dark"?"dark":"light",this.applyTheme(),logseq.App.onThemeModeChanged(({mode:n})=>{this.currentTheme.isDark=n==="dark",this.currentTheme.mode=n==="dark"?"dark":"light",this.applyTheme(),this.notifyListeners()})}catch(t){console.error("主题初始化失败:",t),this.currentTheme.isDark=this.currentTheme.systemPrefersDark,this.applyTheme()}}handleSystemThemeChange=t=>{this.currentTheme.systemPrefersDark=t.matches,this.currentTheme.mode==="auto"&&(this.currentTheme.isDark=t.matches,this.applyTheme(),this.notifyListeners())};applyTheme(){const t=document.documentElement;this.currentTheme.isDark?t.classList.add("dark"):t.classList.remove("dark"),this.setCSSVariables()}setCSSVariables(){const t=document.documentElement;this.currentTheme.isDark?(t.style.setProperty("--theme-bg-primary","#1a1a1a"),t.style.setProperty("--theme-bg-secondary","#2a2a2a"),t.style.setProperty("--theme-bg-tertiary","#3a3a3a"),t.style.setProperty("--theme-text-primary","#e0e0e0"),t.style.setProperty("--theme-text-secondary","#a0a0a0"),t.style.setProperty("--theme-text-muted","#666666"),t.style.setProperty("--theme-border-primary","#404040"),t.style.setProperty("--theme-border-secondary","#505050"),t.style.setProperty("--theme-accent","#0A84FF"),t.style.setProperty("--theme-accent-hover","#0066CC")):(t.style.setProperty("--theme-bg-primary","#ffffff"),t.style.setProperty("--theme-bg-secondary","#f8f9fa"),t.style.setProperty("--theme-bg-tertiary","#e9ecef"),t.style.setProperty("--theme-text-primary","#333333"),t.style.setProperty("--theme-text-secondary","#666666"),t.style.setProperty("--theme-text-muted","#999999"),t.style.setProperty("--theme-border-primary","#e0e0e0"),t.style.setProperty("--theme-border-secondary","#f0f0f0"),t.style.setProperty("--theme-accent","#007AFF"),t.style.setProperty("--theme-accent-hover","#0056CC"))}getTheme(){return{...this.currentTheme}}setTheme(t){switch(this.currentTheme.mode=t,t){case"light":this.currentTheme.isDark=!1;break;case"dark":this.currentTheme.isDark=!0;break;case"auto":this.currentTheme.isDark=this.currentTheme.systemPrefersDark;break}this.applyTheme(),this.notifyListeners()}toggleTheme(){const t=this.currentTheme.isDark?"light":"dark";this.setTheme(t)}addListener(t){this.listeners.push(t)}removeListener(t){const n=this.listeners.indexOf(t);n>-1&&this.listeners.splice(n,1)}notifyListeners(){this.listeners.forEach(t=>{try{t(this.getTheme())}catch(n){console.error("主题监听器执行失败:",n)}})}getThemeClasses(){const t=["theme-transition"];return this.currentTheme.isDark&&t.push("dark"),t.join(" ")}isDarkMode(){return this.currentTheme.isDark}getThemeColor(t){const n=this.currentTheme.isDark?{primary:"#e0e0e0",secondary:"#a0a0a0",muted:"#666666",accent:"#0A84FF",background:"#1a1a1a",surface:"#2a2a2a",border:"#404040"}:{primary:"#333333",secondary:"#666666",muted:"#999999",accent:"#007AFF",background:"#ffffff",surface:"#f8f9fa",border:"#e0e0e0"};return n[t]||n.primary}destroy(){this.mediaQuery.removeEventListener("change",this.handleSystemThemeChange),this.listeners=[]}}const fo=()=>Kr.getInstance(),Cy=({className:e="",showLabel:t=!0})=>{const n=fo(),[r,o]=ae.useState(n.getTheme());ae.useEffect(()=>{const f=p=>{o(p)};return n.addListener(f),()=>{n.removeListener(f)}},[n]);const i=f=>{n.setTheme(f)},a=f=>{switch(f){case"light":return"☀️";case"dark":return"🌙";case"auto":return"🔄";default:return"☀️"}},c=f=>{switch(f){case"light":return"亮色模式";case"dark":return"暗色模式";case"auto":return"跟随系统";default:return"亮色模式"}};return se("div",{className:`theme-toggle ${e}`,children:[t&&D("label",{className:"block text-sm font-medium mb-2",style:{color:n.getThemeColor("primary")},children:"主题设置"}),D("div",{className:"flex items-center space-x-2",children:["light","dark","auto"].map(f=>se("button",{onClick:()=>i(f),className:`
              flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200
              ${r.mode===f?"ring-2 ring-offset-2":"hover:opacity-80"}
            `,style:{backgroundColor:r.mode===f?n.getThemeColor("accent"):n.getThemeColor("surface"),color:r.mode===f?"#ffffff":n.getThemeColor("primary")},children:[D("span",{className:"text-base",children:a(f)}),D("span",{children:c(f)})]},f))}),r.mode==="auto"&&se("div",{className:"mt-2 text-xs opacity-75",style:{color:n.getThemeColor("secondary")},children:["当前: ",r.isDark?"暗色":"亮色",r.systemPrefersDark?" (系统偏好暗色)":" (系统偏好亮色)"]})]})},Sy=({className:e=""})=>{const t=fo(),[n,r]=ae.useState(t.getTheme());return ae.useEffect(()=>{const o=i=>{r(i)};return t.addListener(o),()=>{t.removeListener(o)}},[t]),se("div",{className:`flex items-center space-x-2 text-sm ${e}`,style:{color:t.getThemeColor("secondary")},children:[D("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:n.isDark?"#4A90E2":"#F5A623"}}),D("span",{children:n.mode==="auto"?`自动 (${n.isDark?"暗色":"亮色"})`:n.isDark?"暗色模式":"亮色模式"})]})},_y=()=>{const[e,t]=ae.useState(fn),[n,r]=ae.useState(!0),[o,i]=ae.useState(!1),[a,c]=ae.useState(""),[f,p]=ae.useState({name:"",prompt:""}),[w,C]=ae.useState(null),[x,E]=ae.useState({}),[v,N]=ae.useState({}),[O,h]=ae.useState(!1),d=fo(),[g,S]=ae.useState(d.isDarkMode());ae.useEffect(()=>{const Q=F=>{S(F.isDark)};return d.addListener(Q),()=>{d.removeListener(Q)}},[d]),ae.useEffect(()=>{h(navigator.platform.toUpperCase().indexOf("MAC")>=0)},[]),ae.useEffect(()=>{(async()=>{try{const F=await ss();t(F)}catch(F){console.error("加载设置出错:",F)}finally{r(!1)}})()},[]),ae.useEffect(()=>{const Q=F=>{F.key==="Escape"&&Ne()};return window.addEventListener("keydown",Q),()=>{window.removeEventListener("keydown",Q)}},[]);const P=Q=>{const{name:F,value:T}=Q.target;t(M=>({...M,[F]:T}))},L=Q=>{const{name:F,checked:T}=Q.target;t(M=>({...M,[F]:T}))},y=(Q,F,T)=>{const M={...e.keybindings[Q],[F]:T},$=wy(T),ee=`${Q}.${F}`;!$.isValid&&T.trim()!==""?E(B=>({...B,[ee]:$.error||"快捷键格式无效"})):E(B=>{const oe={...B};return delete oe[ee],oe});const ve=ky(T,e.keybindings);ve.hasConflict?N(B=>({...B,[ee]:ve.message||"存在快捷键冲突"})):N(B=>{const oe={...B};return delete oe[ee],oe}),t(B=>({...B,keybindings:{...B.keybindings,[Q]:M}}))},_=Q=>{const{name:F,value:T}=Q.target;p(M=>({...M,[F]:T}))},A=(Q,F)=>{const{name:T,value:M}=Q.target;t($=>{const ee=[...$.customPrompts];return ee[F]={...ee[F],[T]:M},{...$,customPrompts:ee}})},j=()=>{!f.name||!f.prompt||(t(Q=>({...Q,customPrompts:[...Q.customPrompts,{...f}]})),p({name:"",prompt:""}))},G=Q=>{t(F=>({...F,customPrompts:F.customPrompts.filter((T,M)=>M!==Q)})),w===Q&&C(null)},ce=Q=>{C(Q)},de=()=>{C(null)},te=()=>{C(null)},Te=async()=>{i(!0),c("");try{await vy(e),window.reloadKeybindings&&await window.reloadKeybindings(),c("设置已保存！快捷键将在下次使用时生效。"),setTimeout(()=>c(""),5e3)}catch(Q){console.error("保存设置出错:",Q),c("保存设置失败，请重试。")}finally{i(!1)}},Ne=()=>{window.logseq&&window.logseq.hideMainUI()};return n?D("div",{className:"p-4 text-center",style:{color:d.getThemeColor("primary")},children:"加载中..."}):se("div",{className:"p-4 max-w-2xl mx-auto theme-transition",style:{backgroundColor:d.getThemeColor("background"),color:d.getThemeColor("primary")},children:[se("div",{className:"flex justify-between items-center mb-6",children:[D("h1",{className:"text-2xl font-bold",style:{color:d.getThemeColor("primary")},children:"AI 聊天设置"}),D("button",{onClick:Ne,className:"p-2 rounded-full transition-colors",style:{color:d.getThemeColor("secondary"),backgroundColor:"transparent"},onMouseEnter:Q=>{Q.currentTarget.style.backgroundColor=d.getThemeColor("surface")},onMouseLeave:Q=>{Q.currentTarget.style.backgroundColor="transparent"},title:"关闭设置",children:D("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:D("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),se("div",{className:"mb-6",children:[D("h2",{className:"text-xl font-semibold mb-3",style:{color:d.getThemeColor("primary")},children:"界面设置"}),D("div",{className:"mb-4",children:D(Cy,{})}),D("div",{className:"mb-4",children:D(Sy,{})})]}),se("div",{className:"mb-6",children:[D("h2",{className:"text-xl font-semibold mb-3",style:{color:d.getThemeColor("primary")},children:"快捷键设置"}),se("div",{className:"mb-4",children:[D("label",{className:"block mb-2 font-medium",style:{color:d.getThemeColor("primary")},children:"打开AI聊天"}),se("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[se("div",{children:[D("label",{className:"block mb-1 text-sm",style:{color:d.getThemeColor("secondary")},children:"Windows/Linux"}),D("input",{type:"text",value:e.keybindings.openChat.binding,onChange:Q=>y("openChat","binding",Q.target.value),className:"apple-input w-full p-2 rounded text-sm",style:{backgroundColor:d.getThemeColor("surface"),color:d.getThemeColor("primary"),borderColor:x["openChat.binding"]?"#ef4444":d.getThemeColor("border")},placeholder:"ctrl+g"}),x["openChat.binding"]&&D("p",{className:"text-red-500 text-xs mt-1",children:x["openChat.binding"]}),v["openChat.binding"]&&D("p",{className:"text-yellow-500 text-xs mt-1",children:v["openChat.binding"]})]}),se("div",{children:[D("label",{className:"block mb-1 text-sm",style:{color:d.getThemeColor("secondary")},children:"Mac"}),D("input",{type:"text",value:e.keybindings.openChat.mac,onChange:Q=>y("openChat","mac",Q.target.value),className:"apple-input w-full p-2 rounded text-sm",style:{backgroundColor:d.getThemeColor("surface"),color:d.getThemeColor("primary"),borderColor:x["openChat.mac"]?"#ef4444":d.getThemeColor("border")},placeholder:"cmd+g"}),x["openChat.mac"]&&D("p",{className:"text-red-500 text-xs mt-1",children:x["openChat.mac"]}),v["openChat.mac"]&&D("p",{className:"text-yellow-500 text-xs mt-1",children:v["openChat.mac"]})]})]}),se("div",{className:"mt-2",children:[D("span",{className:"text-sm",style:{color:d.getThemeColor("secondary")},children:"当前快捷键:"}),D("span",{className:"text-sm font-mono ml-1 px-2 py-1 rounded",style:{backgroundColor:d.getThemeColor("surface"),color:d.getThemeColor("accent")},children:bd(e.keybindings.openChat,O)})]})]}),se("div",{className:"mb-4",children:[D("label",{className:"block mb-2 font-medium",style:{color:d.getThemeColor("primary")},children:"快速AI回复"}),se("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[se("div",{children:[D("label",{className:"block mb-1 text-sm",style:{color:d.getThemeColor("secondary")},children:"Windows/Linux"}),D("input",{type:"text",value:e.keybindings.quickReply.binding,onChange:Q=>y("quickReply","binding",Q.target.value),className:"apple-input w-full p-2 rounded text-sm",style:{backgroundColor:d.getThemeColor("surface"),color:d.getThemeColor("primary"),borderColor:x["quickReply.binding"]?"#ef4444":d.getThemeColor("border")},placeholder:"ctrl+shift+g"}),x["quickReply.binding"]&&D("p",{className:"text-red-500 text-xs mt-1",children:x["quickReply.binding"]}),v["quickReply.binding"]&&D("p",{className:"text-yellow-500 text-xs mt-1",children:v["quickReply.binding"]})]}),se("div",{children:[D("label",{className:"block mb-1 text-sm",style:{color:d.getThemeColor("secondary")},children:"Mac"}),D("input",{type:"text",value:e.keybindings.quickReply.mac,onChange:Q=>y("quickReply","mac",Q.target.value),className:"apple-input w-full p-2 rounded text-sm",style:{backgroundColor:d.getThemeColor("surface"),color:d.getThemeColor("primary"),borderColor:x["quickReply.mac"]?"#ef4444":d.getThemeColor("border")},placeholder:"cmd+shift+g"}),x["quickReply.mac"]&&D("p",{className:"text-red-500 text-xs mt-1",children:x["quickReply.mac"]}),v["quickReply.mac"]&&D("p",{className:"text-yellow-500 text-xs mt-1",children:v["quickReply.mac"]})]})]}),se("div",{className:"mt-2",children:[D("span",{className:"text-sm",style:{color:d.getThemeColor("secondary")},children:"当前快捷键:"}),D("span",{className:"text-sm font-mono ml-1 px-2 py-1 rounded",style:{backgroundColor:d.getThemeColor("surface"),color:d.getThemeColor("accent")},children:bd(e.keybindings.quickReply,O)})]})]}),se("div",{className:"p-3 rounded-lg text-sm",style:{backgroundColor:d.getThemeColor("surface"),color:d.getThemeColor("secondary")},children:[D("p",{className:"mb-2",children:D("strong",{children:"快捷键格式说明："})}),se("ul",{className:"list-disc list-inside space-y-1",children:[D("li",{children:"使用 + 号连接修饰键和按键，如：ctrl+g"}),D("li",{children:"支持的修饰键：ctrl, cmd, alt, shift, meta"}),D("li",{children:"按键可以是字母、数字或功能键"}),D("li",{children:"修改后需要重新加载插件才能生效"})]})]})]}),se("div",{className:"mb-6",children:[D("h2",{className:"text-xl font-semibold mb-3",style:{color:d.getThemeColor("primary")},children:"API 配置"}),se("div",{className:"mb-4",children:[D("label",{className:"block mb-2 font-medium",style:{color:d.getThemeColor("primary")},children:"API URL"}),D("input",{type:"text",name:"apiUrl",value:e.apiUrl,onChange:P,className:"apple-input w-full p-2 rounded",style:{backgroundColor:d.getThemeColor("surface"),color:d.getThemeColor("primary"),borderColor:d.getThemeColor("border")},placeholder:"https://api.openai.com/v1/chat/completions"})]}),se("div",{className:"mb-4",children:[D("label",{className:"block mb-2 font-medium",style:{color:d.getThemeColor("primary")},children:"API Key"}),D("input",{type:"password",name:"apiKey",value:e.apiKey,onChange:P,className:"apple-input w-full p-2 rounded",style:{backgroundColor:d.getThemeColor("surface"),color:d.getThemeColor("primary"),borderColor:d.getThemeColor("border")},placeholder:"sk-..."})]}),se("div",{className:"mb-4",children:[D("label",{className:"block mb-2 font-medium",style:{color:d.getThemeColor("primary")},children:"模型名称"}),D("input",{type:"text",name:"modelName",value:e.modelName,onChange:P,className:"apple-input w-full p-2 rounded",style:{backgroundColor:d.getThemeColor("surface"),color:d.getThemeColor("primary"),borderColor:d.getThemeColor("border")},placeholder:"gpt-3.5-turbo"})]})]}),se("div",{className:"mb-6",children:[D("h2",{className:"text-xl font-semibold mb-3",style:{color:d.getThemeColor("primary")},children:"历史记录设置"}),se("div",{className:"flex items-center",children:[D("input",{type:"checkbox",id:"enableHistory",name:"enableHistory",checked:e.enableHistory,onChange:L,className:"mr-2",style:{accentColor:d.getThemeColor("accent")}}),D("label",{htmlFor:"enableHistory",className:"font-medium",style:{color:d.getThemeColor("primary")},children:"启用会话历史记录"})]}),D("p",{className:"text-sm mt-1",style:{color:d.getThemeColor("secondary")},children:"启用后，将在同一聊天会话中保存历史对话记录。"})]}),se("div",{className:"mb-6",children:[D("h2",{className:"text-xl font-semibold mb-3",style:{color:d.getThemeColor("primary")},children:"自定义提示词"}),se("div",{className:"p-4 rounded mb-4",style:{backgroundColor:d.getThemeColor("surface")},children:[D("h3",{className:"font-medium mb-2",style:{color:d.getThemeColor("primary")},children:"添加新提示词"}),se("div",{className:"mb-3",children:[D("label",{className:"block mb-1 text-sm",style:{color:d.getThemeColor("primary")},children:"提示词名称"}),D("input",{type:"text",name:"name",value:f.name,onChange:_,className:"apple-input w-full p-2 rounded",style:{backgroundColor:d.getThemeColor("background"),color:d.getThemeColor("primary"),borderColor:d.getThemeColor("border")},placeholder:"提示名称，如：总结、翻译等"})]}),se("div",{className:"mb-3",children:[D("label",{className:"block mb-1 text-sm",style:{color:d.getThemeColor("primary")},children:"提示词内容"}),D("textarea",{name:"prompt",value:f.prompt,onChange:_,className:"apple-input w-full p-2 rounded",style:{backgroundColor:d.getThemeColor("background"),color:d.getThemeColor("primary"),borderColor:d.getThemeColor("border")},rows:3,placeholder:"提示词内容，如：请总结以下内容的要点："})]}),D("button",{onClick:j,disabled:!f.name||!f.prompt,className:"apple-button px-3 py-1 rounded",style:{backgroundColor:!f.name||!f.prompt?d.getThemeColor("muted"):d.getThemeColor("accent"),color:"#ffffff",opacity:!f.name||!f.prompt?.5:1},children:"添加提示词"})]}),se("div",{children:[D("h3",{className:"font-medium mb-2",style:{color:d.getThemeColor("primary")},children:"现有提示词"}),e.customPrompts.length===0?D("p",{style:{color:d.getThemeColor("secondary")},children:"暂无自定义提示词"}):D("div",{className:"space-y-3",children:e.customPrompts.map((Q,F)=>D("div",{className:"rounded-lg overflow-hidden",style:{border:`1px solid ${d.getThemeColor("border")}`,backgroundColor:d.getThemeColor("surface")},children:w===F?se("div",{className:"p-3",children:[se("div",{className:"mb-2",children:[D("label",{className:"block mb-1 text-sm",children:"提示词名称"}),D("input",{type:"text",name:"name",value:Q.name,onChange:T=>A(T,F),className:"w-full p-2 border border-gray-300 rounded"})]}),se("div",{className:"mb-2",children:[D("label",{className:"block mb-1 text-sm",children:"提示词内容"}),D("textarea",{name:"prompt",value:Q.prompt,onChange:T=>A(T,F),className:"w-full p-2 border border-gray-300 rounded",rows:3})]}),se("div",{className:"flex space-x-2",children:[D("button",{onClick:te,className:"px-2 py-1 bg-green-500 text-white text-sm rounded",children:"完成"}),D("button",{onClick:de,className:"px-2 py-1 bg-gray-300 text-gray-700 text-sm rounded",children:"取消"})]})]}):se("div",{className:"flex justify-between p-3",children:[se("div",{children:[D("div",{className:"font-medium",style:{color:d.getThemeColor("primary")},children:Q.name}),D("div",{className:"text-sm mt-1 line-clamp-2",style:{color:d.getThemeColor("secondary")},children:Q.prompt})]}),se("div",{className:"flex items-start space-x-2",children:[D("button",{onClick:()=>ce(F),className:"transition-colors",style:{color:d.getThemeColor("accent")},title:"编辑提示词",onMouseEnter:T=>{T.currentTarget.style.opacity="0.7"},onMouseLeave:T=>{T.currentTarget.style.opacity="1"},children:D("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:D("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})})}),D("button",{onClick:()=>G(F),className:"transition-colors",style:{color:"#EF4444"},title:"删除提示词",onMouseEnter:T=>{T.currentTarget.style.opacity="0.7"},onMouseLeave:T=>{T.currentTarget.style.opacity="1"},children:D("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:D("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]})]})},F))})]})]}),se("div",{className:"mt-6 flex items-center",children:[D("button",{onClick:Te,disabled:o,className:"apple-button px-4 py-2 rounded font-medium",style:{backgroundColor:o?d.getThemeColor("muted"):"#10B981",color:"#ffffff",opacity:o?.5:1,cursor:o?"not-allowed":"pointer"},children:o?"保存中...":"保存设置"}),a&&D("span",{className:"ml-3",style:{color:"#10B981"},children:a})]})]})};class il extends Error{constructor(t){super(t),this.name="ApiConnectionError"}}function xy(e,t,n){const r=[];n?r.push({role:"system",content:n}):r.push({role:"system",content:"你是一个有用的助手，可以帮助用户处理和分析文本内容。提供简洁、准确的回答。"});let o=e;if(t.content&&!e.includes(t.content)){let i="";switch(t.type){case"selection":i=`以下是选中的文本：

`;break;case"block":i=`以下是当前块的内容：

`;break;case"blocks":i=`以下是选中的多个块：

`;break}o=`${i}${t.content}

${e}`}return r.push({role:"user",content:o}),r}function ia(e){try{const t=e.split(`
`).filter(r=>r.trim()!==""&&r.trim()!=="data: [DONE]");let n="";for(const r of t)if(r.startsWith("data: ")){const o=r.replace(/^data: /,"");if(!o||o==="[DONE]")continue;try{const i=JSON.parse(o);i.choices&&i.choices[0]?i.choices[0].delta&&i.choices[0].delta.content?n+=i.choices[0].delta.content:i.choices[0].message&&i.choices[0].message.content&&(n+=i.choices[0].message.content):i.content?n+=i.content:i.text&&(n+=i.text)}catch{console.warn("解析 JSON 失败:",o)}}return n}catch(t){return console.error("解析数据块失败:",t),""}}async function Ey(e,t,n,r){try{const o=await ss();if(!o.apiUrl||!o.apiKey)throw new il("API URL 或 API Key 未配置");const a={messages:xy(e,t,r),stream:!0,model:o.modelName};o.temperature!==void 0&&(a.temperature=o.temperature),o.maxTokens!==void 0&&o.maxTokens>0&&(a.max_tokens=o.maxTokens);const c=await fetch(o.apiUrl,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${o.apiKey}`},body:JSON.stringify(a)});if(!c.ok){let f=`API 请求失败: ${c.status} ${c.statusText}`;try{const p=await c.json();f=`${f}. ${p.error?.message||JSON.stringify(p)}`}catch{}throw new il(f)}if(c.body){const f=c.body.getReader(),p=new TextDecoder("utf-8");let w="",C="",x=Date.now();const E=50;for(;;){const{done:v,value:N}=await f.read();if(v){if(C.length>0){const S=ia(C);S&&(w+=S,n.onChunk(S))}n.onComplete(w);break}const O=p.decode(N,{stream:!0});C+=O;const h=C.split(`

`),d=h.pop()||"";if(h.length>0){const S=ia(h.join(`

`));S&&(w+=S,n.onChunk(S))}C=d;const g=Date.now();if(g-x>=E&&C.includes("data: ")){const S=ia(C);S&&(w+=S,n.onChunk(S),C=""),x=g}}}else throw new il("API 响应中没有正文")}catch(o){o instanceof il?n.onError(o.message):n.onError(`请求失败: ${o instanceof Error?o.message:String(o)}`)}}function Wl(e){if(!e||typeof e!="string")return[];e=e.replace(/^>+\s/gm,"");const t=e.split(/\r?\n/),n=[];let r="",o=!1,i="",a=0;const c=t.filter(p=>p.trim().length>0).map(p=>{const w=p.match(/^(\s+)/);return w?w[1].length:0}).filter(p=>p>0);let f=2;if(c.length>0){const p=c.filter(w=>w>0);p.length>0&&(f=Math.min(...p))}for(let p=0;p<t.length;p++){const w=t[p],C=w.match(/^(\s+)/),x=C?C[1].length:0,E=Math.floor(x/f);if(w.trim().startsWith("```")){if(o){o=!1;const v=a>0?"  ".repeat(a):"";n.push(`${v}\`\`\``)}else{if(r.trim()){const h=a>0?"  ".repeat(a):"";n.push(`${h}${r.trim()}`),r=""}o=!0,i="    ";const v=w.trim().match(/^```(.*)$/),N=v?v[1].trim():"",O=E>0?"  ".repeat(E):"";N?r=`\`\`\` ${N}`:r="```",n.push(`${O}${r}`),r="",a=E}continue}if(o){const v=a>0?"  ".repeat(a):"";n.push(`${v}${i}${w.trimStart()}`);continue}if(w.trim().length>0&&(a=E),w.trim().match(/^(\d+\.|-|\*|\+)\s/)){if(r.trim()){const O=a>0?"  ".repeat(a):"";n.push(`${O}${r.trim()}`),r=""}const v=E>0?"  ".repeat(E):"",N=w.trim().replace(/^(\d+\.|-|\*|\+)\s+/,"");n.push(`${v}${N}`);continue}if(w.trim().match(/^#{1,6}\s/)){if(r.trim()){const N=a>0?"  ".repeat(a):"";n.push(`${N}${r.trim()}`),r=""}const v=E>0?"  ".repeat(E):"";n.push(`${v}${w.trim()}`);continue}if(!w.trim()){if(r.trim()){const v=a>0?"  ".repeat(a):"";n.push(`${v}${r.trim()}`),r=""}continue}r?r+=" "+w.trim():r=w.trim()}if(r.trim()){const p=a>0?"  ".repeat(a):"";n.push(`${p}${r.trim()}`)}return n}const Ty=({context:e,onClose:t,onReplace:n,onInsert:r})=>{const[o,i]=ae.useState(""),[a,c]=ae.useState(!1),[f,p]=ae.useState(""),[w,C]=ae.useState(null),[x,E]=ae.useState("custom"),[v,N]=ae.useState(""),[O,h]=ae.useState(!0),[d,g]=ae.useState(0),[S,P]=ae.useState([]),[L,y]=ae.useState(-1),[_,A]=ae.useState(!1),j=ae.useRef(null),G=ae.useRef(null),ce=ae.useRef(null),de=ae.useRef(null),te=fo(),[Te,Ne]=ae.useState(te.isDarkMode());ae.useEffect(()=>{const Y=ie=>{Ne(ie.isDark)};return te.addListener(Y),()=>{te.removeListener(Y)}},[te]),ae.useEffect(()=>{(async()=>{try{const ie=await ss();C(ie)}catch(ie){console.error("加载设置出错:",ie),p("无法加载插件设置，请检查配置")}})()},[]),ae.useEffect(()=>{x!=="default"&&x!=="custom"&&e?.content&&ve(),x==="custom"?(h(!0),setTimeout(()=>{G.current?.focus()},50)):h(!1)},[x]),ae.useEffect(()=>{O&&setTimeout(()=>{G.current?.focus()},50)},[]),ae.useEffect(()=>{const Y=ie=>{de.current&&!de.current.contains(ie.target)&&A(!1)};return document.addEventListener("mousedown",Y),()=>{document.removeEventListener("mousedown",Y)}},[]);const Q=Y=>{E(Y.target.value)},F=Y=>{const ie=Y.target.value;if(N(ie),w&&ie.trim()!==""){const we=w.customPrompts.filter(me=>me.name.toLowerCase().includes(ie.toLowerCase()));P(we),A(we.length>0),y(-1)}else P([]),A(!1)},T=(Y,ie)=>{const we=`使用提示词: ${Y}`;N(we),E("custom"),A(!1),setTimeout(()=>{ve(ie)},100)},M=Y=>{if(Y.key==="Escape"){A(!1);return}if(!_){Y.key==="Enter"&&v.trim()&&!a&&(Y.preventDefault(),ve());return}if(Y.key==="ArrowDown")Y.preventDefault(),y(ie=>ie<S.length-1?ie+1:ie);else if(Y.key==="ArrowUp")Y.preventDefault(),y(ie=>ie>0?ie-1:0);else if(Y.key==="Enter"&&L>=0){Y.preventDefault();const ie=S[L];T(ie.name,ie.prompt)}},$=()=>{v.trim()&&!a&&ve()},ee=Y=>{Y.key==="Enter"&&v.trim()&&!a&&!_&&(Y.preventDefault(),ve())},ve=async Y=>{if(!(!e?.content||a)){if(!w||!w.apiKey){p("请先在设置中配置API密钥");return}c(!0),p(""),i(""),g(0),A(!1);try{let ie;if(Y)ie=Y;else if(x==="custom"){if(!v.trim()){p("请输入自定义提示词"),c(!1);return}ie=v}else if(x!=="default"&&w){const me=w.customPrompts.find(Ue=>Ue.name===x);me&&(ie=me.prompt)}const we={onChunk:me=>{i(Ue=>{const Ge=Ue+me;return setTimeout(()=>{g(Ge.length)},10),Ge})},onComplete:me=>{c(!1),g(me.length)},onError:me=>{p(`API 请求失败: ${me}`),c(!1)}};await Ey(e.content,e,we,ie)}catch(ie){console.error("发送消息出错:",ie),p("发送消息失败，请重试"),c(!1)}}},B=()=>o?se("div",{className:"typewriter-text",children:[o.slice(0,d),a&&d===o.length&&D("span",{className:"blinking-cursor",children:"|"})]}):null,oe=()=>w?se(am,{children:[D("option",{value:"custom",children:"自定义提示词"}),w.customPrompts.map((Y,ie)=>D("option",{value:Y.name,children:Y.name},ie))]}):null,fe=()=>!_||S.length===0?null:D("div",{ref:de,className:"absolute z-10 left-4 right-16 top-full mt-1 bg-white dark:bg-gray-800 shadow-lg rounded-lg border border-gray-200 dark:border-gray-700 max-h-60 overflow-y-auto",children:S.map((Y,ie)=>D("div",{className:`px-4 py-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 ${ie===L?"bg-gray-100 dark:bg-gray-700":""}`,onClick:()=>T(Y.name,Y.prompt),children:D("div",{className:"font-medium text-gray-900 dark:text-white",children:Y.name})},ie))}),Re=async Y=>{try{if(Wl(Y).length===0||e.blockUUIDs.length===0){p("无法替换原文：无有效块数据或无原始块 UUID");return}n(Y)}catch(ie){console.error("替换原文出错:",ie),p("替换原文失败，请重试")}},qe=async Y=>{try{if(Wl(Y).length===0||e.blockUUIDs.length===0){p("无法插入子块：无有效块数据或无原始块 UUID");return}r(Y)}catch(ie){console.error("插入子块出错:",ie),p("插入子块失败，请重试")}};return se("div",{ref:j,className:`apple-modal rounded-xl shadow-lg w-full flex flex-col ${te.getThemeClasses()}`,style:{backdropFilter:"blur(20px)",WebkitBackdropFilter:"blur(20px)",maxHeight:"60vh",border:`1px solid ${te.getThemeColor("border")}`,backgroundColor:te.getThemeColor("background"),color:te.getThemeColor("primary")},children:[se("div",{className:"p-4 border-b flex justify-between items-center",style:{borderColor:te.getThemeColor("border")},children:[D("h2",{className:"text-lg font-medium",style:{color:te.getThemeColor("primary")},children:"AI 聊天"}),se("div",{className:"flex items-center",children:[D("select",{value:x,onChange:Q,className:"apple-select mr-3 text-sm rounded-md border-none h-8 px-3",style:{backgroundColor:te.getThemeColor("surface"),color:te.getThemeColor("primary")},children:oe()}),D("button",{onClick:t,className:"h-8 w-8 flex items-center justify-center rounded-full transition-colors",style:{color:te.getThemeColor("secondary"),backgroundColor:"transparent"},onMouseEnter:Y=>{Y.currentTarget.style.backgroundColor=te.getThemeColor("surface")},onMouseLeave:Y=>{Y.currentTarget.style.backgroundColor="transparent"},children:"✕"})]})]}),O&&se("div",{className:"p-4 border-b relative",style:{borderColor:te.getThemeColor("border")},children:[se("div",{className:"flex items-center",children:[D("input",{ref:G,type:"text",value:v,onChange:F,onKeyPress:ee,onKeyDown:M,placeholder:"输入提示词，支持自动补全",className:"apple-input flex-grow text-sm rounded-lg px-4 py-2",style:{backgroundColor:te.getThemeColor("surface"),color:te.getThemeColor("primary"),borderColor:te.getThemeColor("border")}}),D("button",{onClick:$,disabled:!v.trim()||a,className:"apple-button ml-2 px-4 py-2 rounded-lg text-sm font-medium",style:{backgroundColor:!v.trim()||a?te.getThemeColor("muted"):te.getThemeColor("accent"),color:"#ffffff",opacity:!v.trim()||a?.5:1,cursor:!v.trim()||a?"not-allowed":"pointer"},children:"发送"})]}),fe()]}),se("div",{ref:ce,className:"p-4 flex-grow overflow-y-auto max-h-[40vh] relative",children:[f&&D("div",{className:"mb-4 p-3 rounded-lg text-sm",style:{backgroundColor:"rgba(239, 68, 68, 0.1)",borderColor:"rgba(239, 68, 68, 0.3)",color:Te?"#FCA5A5":"#DC2626",border:"1px solid"},children:f}),a&&!o&&D("div",{className:"flex justify-center items-center py-8",children:D("div",{className:"apple-spinner w-6 h-6 border-2 rounded-full animate-spin",style:{borderColor:te.getThemeColor("border"),borderTopColor:te.getThemeColor("accent")}})}),o&&D("div",{className:"ai-response rounded-lg p-4 text-sm whitespace-pre-wrap relative",style:{backgroundColor:te.getThemeColor("surface"),color:te.getThemeColor("primary")},children:B()})]}),o&&!a&&se("div",{className:"p-3 border-t flex justify-end space-x-2",style:{borderColor:te.getThemeColor("border")},children:[D("button",{onClick:()=>Re(o),className:"apple-button-secondary px-4 py-2 rounded-lg text-sm font-medium transition-colors",style:{backgroundColor:te.getThemeColor("surface"),color:te.getThemeColor("primary"),border:`1px solid ${te.getThemeColor("border")}`},onMouseEnter:Y=>{Y.currentTarget.style.backgroundColor=te.getThemeColor("border")},onMouseLeave:Y=>{Y.currentTarget.style.backgroundColor=te.getThemeColor("surface")},children:"替换原文"}),D("button",{onClick:()=>qe(o),className:"apple-button-primary px-4 py-2 rounded-lg text-sm font-medium transition-colors",style:{backgroundColor:te.getThemeColor("accent"),color:"#ffffff"},onMouseEnter:Y=>{Y.currentTarget.style.opacity="0.9"},onMouseLeave:Y=>{Y.currentTarget.style.opacity="1"},children:"插入子块"})]})]})};function Ny({chatContext:e}={}){const t=ae.useRef(null),n=gy(),[r,o]=ae.useState("settings"),[i,a]=ae.useState(e),[c,f]=ae.useState(null),[p,w]=ae.useState(!1),C=fo();ae.useEffect(()=>{e&&(a(e),o("chat"),setTimeout(x,50))},[e]),ae.useEffect(()=>{w(C.isDarkMode());const d=g=>{w(g.isDark)};return C.addListener(d),()=>{C.removeListener(d)}},[C]);const x=async()=>{if(!(!i||!i.blockUUIDs.length))try{const d=i.blockUUIDs[0],g=document.querySelector(`[blockid="${d}"]`);if(g){const S=g.getBoundingClientRect();f({top:S.bottom,left:S.left,width:S.width})}}catch(d){console.error("获取块位置失败:",d)}},E=async d=>{if(!(!i||!i.blockUUIDs.length))try{const g=Wl(d);for(const L of i.blockUUIDs)await logseq.Editor.removeBlock(L);const S=i.blockUUIDs[0],P=await logseq.Editor.getBlock(S);if(!P){console.error("无法获取块信息");return}if(P.parent){const L=P.parent.id||P.parent.uuid;for(const y of g)await logseq.Editor.insertBlock(L,y,{sibling:!0,before:!1})}else if(P.page){const L=P.page.originalName||P.page.name;for(const y of g)await logseq.Editor.insertBlock(L,y)}logseq.hideMainUI()}catch(g){console.error("替换内容失败:",g)}},v=async d=>{if(!(!i||!i.blockUUIDs.length))try{const g=Wl(d),S=i.blockUUIDs[0],P=N(g);await O(S,P),logseq.hideMainUI()}catch(g){console.error("插入内容失败:",g)}},N=d=>{if(!d.length)return[];const g=[],S=[];for(const P of d){const L=P.match(/^(\s+)/),y=L?Math.floor(L[1].length/2):0,j={content:P.trimStart().replace(/^(\d+\.|-|\*|\+)\s+/,"")};if(S.length===0)g.push(j),S.push({node:j,indent:y});else{for(;S.length>0&&S[S.length-1].indent>=y;)S.pop();if(S.length===0)g.push(j);else{const G=S[S.length-1].node;G.children||(G.children=[]),G.children.push(j)}S.push({node:j,indent:y})}}return g},O=async(d,g)=>{for(const S of g){const P=await logseq.Editor.insertBlock(d,S.content,{sibling:!1});S.children&&S.children.length>0&&P&&await O(P.uuid,S.children)}},h=()=>{logseq.hideMainUI()};return n?se("main",{className:"fixed inset-0 z-50 flex items-center justify-center",style:{backdropFilter:"none",WebkitBackdropFilter:"none",backgroundColor:"transparent"},onClick:d=>{t.current?.contains(d.target)||window.logseq.hideMainUI()},children:[r==="settings"&&D("div",{ref:t,className:`apple-modal rounded-xl shadow-2xl w-full max-w-3xl max-h-[90vh] overflow-y-auto pointer-events-auto mx-auto ${C.getThemeClasses()}`,style:{backdropFilter:"blur(20px)",WebkitBackdropFilter:"blur(20px)",backgroundColor:C.getThemeColor("background"),color:C.getThemeColor("primary")},onClick:d=>d.stopPropagation(),children:D(_y,{})}),r==="chat"&&i&&D("div",{ref:t,className:"pointer-events-auto",style:c?{position:"absolute",top:`${c.top+10}px`,left:`${c.left}px`,zIndex:9999,maxWidth:"700px",width:`${Math.max(580,Math.min(c.width*1.5,700))}px`}:{maxWidth:"700px",width:"100%"},onClick:d=>d.stopPropagation(),children:D(Ty,{context:i,onClose:h,onReplace:E,onInsert:v})})]}):null}async function by(){try{const e=await logseq.Editor.getEditingCursorPosition();if(e&&e.pos){const t=await logseq.Editor.getCurrentBlock();if(t){const n=t.content,r=e.pos;if(r.start!==r.end)return n.substring(r.start,r.end)}}return null}catch(e){return console.error("获取选中文本失败:",e),null}}async function Py(){try{return await logseq.Editor.getSelectedBlocks()||[]}catch(e){return console.error("获取选中块失败:",e),[]}}async function Pd(){try{return await logseq.Editor.getCurrentBlock()}catch(e){return console.error("获取当前块失败:",e),null}}async function Za(){const e=await by();if(e){const r=await Pd();return{type:"selection",content:e,blockUUIDs:r?[r.uuid]:[]}}const t=await Py();if(t&&t.length>0)return{type:"blocks",content:t.map(o=>o.content).join(`

`),blockUUIDs:t.map(o=>o.uuid)};const n=await Pd();return n?{type:"block",content:n.content,blockUUIDs:[n.uuid]}:{type:"none",content:"",blockUUIDs:[]}}const Oy={id:"logseq-plugin-ai-chat",icon:"./logo.svg",title:"AI 聊天助手",description:"在Logseq中使用自定义API进行AI聊天，支持智能上下文感知和完整暗色模式"},Iy=(e,...t)=>String.raw(e,...t),qp=Oy.id,Ly=Wp(document.getElementById("app"));let di,ll=[];async function Qp(){try{const e=await ss();for(const r of ll)try{console.log(`尝试清除命令: ${r}`)}catch(o){console.warn(`清除命令失败: ${r}`,o)}ll=[];const t="open-ai-chat";logseq.App.registerCommandPalette({key:t,label:e.keybindings.openChat.description,keybinding:{binding:e.keybindings.openChat.binding,mac:e.keybindings.openChat.mac}},async()=>{const r=await Za();console.log("获取到的上下文:",r),di=r,Qo(),logseq.showMainUI()}),ll.push(t);const n="quick-ai-reply";logseq.App.registerCommandPalette({key:n,label:e.keybindings.quickReply.description,keybinding:{binding:e.keybindings.quickReply.binding,mac:e.keybindings.quickReply.mac}},async()=>{const r=await Za();console.log("快速回复上下文:",r),di=r,Qo(),logseq.showMainUI()}),ll.push(n),console.log("快捷键注册完成:",e.keybindings)}catch(e){console.error("注册快捷键失败:",e)}}async function jy(){console.log("重新加载快捷键..."),await Qp()}async function My(){console.info(`#${qp}: MAIN`),await yy(),await Ay(),logseq.useSettingsSchema([{key:"apiUrl",type:"string",default:"https://api.openai.com/v1/chat/completions",title:"API URL",description:"AI服务的API地址"},{key:"apiKey",type:"string",default:"",title:"API Key",description:"访问AI服务所需的API密钥"},{key:"modelName",type:"string",default:"gpt-3.5-turbo",title:"模型名称",description:"使用的AI模型名称"},{key:"enableHistory",type:"boolean",default:!1,title:"启用历史记录",description:"是否保存聊天历史记录"}]),Qo();function e(){return{show(){di=void 0,Qo(),logseq.showMainUI()},async openAIChat(){const r=await Za();console.log("获取到的上下文:",r),di=r,Qo(),logseq.showMainUI()}}}const t=e();logseq.provideModel(t),logseq.setMainUIInlineStyle({zIndex:9}),logseq.Editor.registerSlashCommand("AI聊天",async()=>t.openAIChat()),await Qp(),window.reloadKeybindings=jy;const n="ai-chat-plugin-open";logseq.provideStyle(Iy`
    .${n} {
      opacity: 0.8;
      font-size: 22px;
      margin-top: 4px;
      color: var(--ls-primary-text-color);
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
    }

    .${n}:hover {
      opacity: 1;
      color: var(--ls-active-primary-color);
      transform: scale(1.1);
    }
  `),logseq.App.registerUIItem("toolbar",{key:n,template:`
    <a data-on-click="show">
        <div class="${n}">🤖</div>
    </a>    
`})}function Qo(){Ly.render(D(Ud.StrictMode,{children:D(Ny,{chatContext:di})}))}async function Ay(){try{const e=fo();console.info(`#${qp}: 主题管理器已初始化`)}catch(e){console.error("主题初始化失败:",e)}}logseq.ready(My).catch(console.error);
