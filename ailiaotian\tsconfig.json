{"compilerOptions": {"target": "ESNext", "lib": ["DOM", "DOM.Iterable", "ESNext"], "types": ["vite/client"], "allowJs": false, "skipLibCheck": true, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "Node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react"}, "include": ["./src"]}