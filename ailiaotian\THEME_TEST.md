# 暗色模式测试指南

## 🧪 测试清单

### 基础功能测试

#### 1. 主题检测
- [ ] 插件启动时正确检测Logseq当前主题
- [ ] 在Logseq中切换主题时插件能实时响应
- [ ] 主题状态在插件重新加载后保持正确

#### 2. UI组件适配
- [ ] 聊天模态框在暗色模式下显示正确
- [ ] 设置页面在暗色模式下显示正确
- [ ] 输入框和按钮在暗色模式下样式正确
- [ ] 下拉选择框在暗色模式下样式正确
- [ ] 滚动条在暗色模式下样式正确

#### 3. 文本和颜色对比度
- [ ] 主要文本在暗色背景下清晰可读
- [ ] 次要文本在暗色背景下清晰可读
- [ ] 链接颜色在暗色模式下清晰可见
- [ ] 按钮文字在暗色模式下清晰可读
- [ ] 错误和状态消息在暗色模式下清晰可见

### 高级功能测试

#### 4. AI响应内容样式
- [ ] 代码块在暗色模式下高亮正确
- [ ] 引用块在暗色模式下样式正确
- [ ] 表格在暗色模式下边框和背景正确
- [ ] 列表在暗色模式下样式正确
- [ ] 分隔线在暗色模式下颜色正确

#### 5. 动画和过渡
- [ ] 主题切换时有平滑的过渡动画
- [ ] 按钮悬停效果在暗色模式下正常
- [ ] 模态框打开/关闭动画在暗色模式下正常
- [ ] 加载动画在暗色模式下显示正确

#### 6. 主题管理器
- [ ] ThemeManager单例模式工作正常
- [ ] 主题监听器正确添加和移除
- [ ] 主题状态变化时所有监听器都被通知
- [ ] CSS变量在主题切换时正确更新

## 🔍 测试步骤

### 步骤1：基础主题切换测试
1. 在Logseq中使用亮色主题
2. 打开AI聊天插件
3. 验证插件界面为亮色模式
4. 在Logseq中切换到暗色主题
5. 验证插件界面自动切换到暗色模式
6. 检查所有UI元素是否正确显示

### 步骤2：设置页面测试
1. 打开插件设置页面
2. 在亮色和暗色模式之间切换
3. 验证所有设置项在两种模式下都清晰可读
4. 测试主题切换组件是否正常工作
5. 验证主题状态指示器显示正确

### 步骤3：聊天功能测试
1. 在不同主题模式下打开聊天窗口
2. 发送消息并接收AI回复
3. 验证AI回复内容在暗色模式下格式正确
4. 测试代码块、引用等特殊格式
5. 验证操作按钮在暗色模式下可用

### 步骤4：边界情况测试
1. 在主题切换过程中打开插件
2. 快速连续切换主题
3. 在插件运行时重启Logseq
4. 测试系统主题偏好设置的影响
5. 验证插件在不同操作系统下的表现

## 🐛 已知问题和解决方案

### 问题1：主题切换延迟
**现象**：主题切换时有轻微延迟
**原因**：CSS过渡动画和DOM更新
**解决方案**：已优化过渡时间，正常现象

### 问题2：某些元素颜色不一致
**现象**：个别UI元素在暗色模式下颜色不够统一
**原因**：CSS变量覆盖不完整
**解决方案**：检查CSS变量定义，确保完整覆盖

### 问题3：滚动条样式问题
**现象**：在某些浏览器中滚动条样式可能不一致
**原因**：浏览器兼容性差异
**解决方案**：使用标准CSS属性，提供降级方案

## 📊 测试结果记录

### 测试环境
- **操作系统**：_____________
- **浏览器**：_____________
- **Logseq版本**：_____________
- **插件版本**：v0.1.0

### 测试结果
| 测试项目 | 通过 | 失败 | 备注 |
|---------|------|------|------|
| 主题检测 | ⬜ | ⬜ | |
| UI组件适配 | ⬜ | ⬜ | |
| 文本对比度 | ⬜ | ⬜ | |
| AI响应样式 | ⬜ | ⬜ | |
| 动画过渡 | ⬜ | ⬜ | |
| 主题管理器 | ⬜ | ⬜ | |

### 问题报告
如发现问题，请记录：
1. **问题描述**：
2. **重现步骤**：
3. **预期结果**：
4. **实际结果**：
5. **截图**（如有）：

## 🔧 调试技巧

### 浏览器开发者工具
1. 打开开发者工具（F12）
2. 查看Console标签页的错误信息
3. 在Elements标签页检查CSS样式
4. 使用Network标签页检查资源加载

### CSS变量检查
```javascript
// 在控制台中检查CSS变量
getComputedStyle(document.documentElement).getPropertyValue('--theme-bg-primary')
```

### 主题状态检查
```javascript
// 检查主题管理器状态
window.themeManager?.getTheme()
```

### Logseq主题API
```javascript
// 检查Logseq主题状态
logseq.App.getStateFromStore("ui/theme")
```

## ✅ 测试完成确认

完成所有测试项目后，请确认：
- [ ] 所有基础功能正常工作
- [ ] 暗色模式UI显示正确
- [ ] 主题切换响应及时
- [ ] 没有明显的样式问题
- [ ] 用户体验良好

测试完成日期：___________
测试人员：___________
测试结果：通过 / 需要修复

---

感谢您的测试！您的反馈将帮助我们不断改进插件的质量。🙏
