// eslint-disable-next-line no-undef
module.exports = {
  content: [
    './src/**/*.{js,ts,jsx,tsx}'
  ],
  darkMode: 'class', // 启用基于类的暗色模式
  theme: {
    extend: {
      colors: {
        // 自定义暗色模式颜色
        dark: {
          50: '#f8fafc',
          100: '#f1f5f9',
          200: '#e2e8f0',
          300: '#cbd5e1',
          400: '#94a3b8',
          500: '#64748b',
          600: '#475569',
          700: '#334155',
          800: '#1e293b',
          900: '#0f172a',
        }
      },
      backgroundColor: {
        // 主题相关的背景色
        'theme-primary': 'var(--ls-primary-background-color, #ffffff)',
        'theme-secondary': 'var(--ls-secondary-background-color, #f8f9fa)',
        'theme-tertiary': 'var(--ls-tertiary-background-color, #e9ecef)',
      },
      textColor: {
        // 主题相关的文字色
        'theme-primary': 'var(--ls-primary-text-color, #333333)',
        'theme-secondary': 'var(--ls-secondary-text-color, #666666)',
        'theme-muted': 'var(--ls-muted-text-color, #999999)',
      },
      borderColor: {
        // 主题相关的边框色
        'theme-primary': 'var(--ls-border-color, #e0e0e0)',
        'theme-secondary': 'var(--ls-secondary-border-color, #f0f0f0)',
      }
    },
  },
  plugins: [],
}
