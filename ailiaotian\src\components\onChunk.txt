      // 定义响应处理器
      const responseHandlers: ApiResponseHandlers = {
        onChunk: (chunk: string) => {
          setAiResponse(prev => {
            let newResponse;

            // 如果有原始响应，说明是追问模式
            if (originalResponse !== '') {
              // 检查是否已经有追问的回答内容
              if (prev.includes('_正在生成回答..._')) {
                // 替换"正在生成回答..."为实际的回答
                newResponse = prev.replace('_正在生成回答..._', chunk);
              } else if (prev.includes('**回答：**')) {
                // 已经有回答标记，检查是否已经有部分回答内容
                const answerMarkerPos = prev.lastIndexOf('**回答：**');
                const answerStartPos = answerMarkerPos + '**回答：**'.length;
                const beforeAnswer = prev.substring(0, answerStartPos);
                const existingAnswer = prev.substring(answerStartPos);
                
                // 将新内容添加到现有回答后面
                newResponse = beforeAnswer + existingAnswer + chunk;
              } else {
                // 已经有一部分回答，继续添加
                const lastResponsePart = prev.substring(prev.lastIndexOf('**追问：**'));
                const responseStart = prev.substring(0, prev.lastIndexOf('**追问：**'));
                const questionPart = lastResponsePart.substring(0, lastResponsePart.indexOf('\n\n') + 2);
                const existingResponse = lastResponsePart.substring(lastResponsePart.indexOf('\n\n') + 2);

                newResponse = responseStart + questionPart + existingResponse + chunk;
              }
            } else {
              // 非追问模式，直接添加内容
              newResponse = prev + chunk;
            }

            // 更新当前位置以实现打字机效果
            setTimeout(() => {
              setStreamPosition(newResponse.length);
            }, 10);
            return newResponse;
          });
        },
