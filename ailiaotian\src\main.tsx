import "@logseq/libs";

import React from "react";
import * as ReactDOM from "react-dom/client";
import App from "./App";
import "./index.css";
import { initializeSettings, getSettings } from "./settings";
import { getContext, ContextData } from "./contextManager";
import { getThemeManager } from "./themeManager";

import { logseq as PL } from "../package.json";

// @ts-expect-error
const css = (t, ...args) => String.raw(t, ...args);

const pluginId = PL.id;
const root = ReactDOM.createRoot(document.getElementById("app")!);

// 当前上下文状态
let currentContext: ContextData | undefined;

// 已注册的快捷键命令ID
let registeredCommands: string[] = [];

/**
 * 动态注册快捷键
 */
async function registerKeybindings() {
  try {
    // 获取当前设置
    const settings = await getSettings();

    // 清除之前注册的命令
    for (const commandId of registeredCommands) {
      try {
        // Logseq目前没有提供取消注册命令的API，所以我们只能重新注册
        console.log(`尝试清除命令: ${commandId}`);
      } catch (error) {
        console.warn(`清除命令失败: ${commandId}`, error);
      }
    }
    registeredCommands = [];

    // 注册打开AI聊天快捷键
    const openChatCommandId = "open-ai-chat";
    logseq.App.registerCommandPalette({
      key: openChatCommandId,
      label: settings.keybindings.openChat.description,
      keybinding: {
        binding: settings.keybindings.openChat.binding,
        mac: settings.keybindings.openChat.mac
      }
    }, async () => {
      const context = await getContext();
      console.log("获取到的上下文:", context);

      // 设置当前上下文并打开UI
      currentContext = context;
      renderApp();
      logseq.showMainUI();
    });
    registeredCommands.push(openChatCommandId);

    // 注册快速AI回复快捷键
    const quickReplyCommandId = "quick-ai-reply";
    logseq.App.registerCommandPalette({
      key: quickReplyCommandId,
      label: settings.keybindings.quickReply.description,
      keybinding: {
        binding: settings.keybindings.quickReply.binding,
        mac: settings.keybindings.quickReply.mac
      }
    }, async () => {
      // TODO: 实现快速回复功能
      const context = await getContext();
      console.log("快速回复上下文:", context);

      // 暂时使用相同的逻辑，后续可以实现不同的行为
      currentContext = context;
      renderApp();
      logseq.showMainUI();
    });
    registeredCommands.push(quickReplyCommandId);

    console.log("快捷键注册完成:", settings.keybindings);
  } catch (error) {
    console.error("注册快捷键失败:", error);
  }
}

/**
 * 重新加载快捷键（当设置更新时调用）
 */
async function reloadKeybindings() {
  console.log("重新加载快捷键...");
  await registerKeybindings();
}

async function main() {
  console.info(`#${pluginId}: MAIN`);

  // 初始化插件设置
  await initializeSettings();

  // 初始化主题检测
  await initializeTheme();
  
  // 确保设置页面正确注册
  logseq.useSettingsSchema([
    {
      key: "apiUrl",
      type: "string",
      default: "https://api.openai.com/v1/chat/completions",
      title: "API URL",
      description: "AI服务的API地址"
    },
    {
      key: "apiKey",
      type: "string",
      default: "",
      title: "API Key",
      description: "访问AI服务所需的API密钥"
    },
    {
      key: "modelName",
      type: "string",
      default: "gpt-3.5-turbo",
      title: "模型名称",
      description: "使用的AI模型名称"
    },
    {
      key: "enableHistory",
      type: "boolean",
      default: false,
      title: "启用历史记录",
      description: "是否保存聊天历史记录"
    }
  ]);
  
  renderApp();

  function createModel() {
    return {
      show() {
        currentContext = undefined;
        renderApp();
        logseq.showMainUI();
      },
      // 处理AI聊天命令
      async openAIChat() {
        const context = await getContext();
        console.log("获取到的上下文:", context);
        
        // 设置当前上下文并打开UI
        currentContext = context;
        renderApp();
        logseq.showMainUI();
      },
    };
  }

  const model = createModel();
  logseq.provideModel(model);
  logseq.setMainUIInlineStyle({
    zIndex: 9,
  });
  
  // 注册斜杠命令
  logseq.Editor.registerSlashCommand("AI聊天", async () => {
    return model.openAIChat();
  });

  // 动态注册快捷键
  await registerKeybindings();

  // 将重新加载快捷键函数暴露给全局，供设置页面使用
  (window as any).reloadKeybindings = reloadKeybindings;

  const openIconName = "ai-chat-plugin-open";

  logseq.provideStyle(css`
    .${openIconName} {
      opacity: 0.8;
      font-size: 22px;
      margin-top: 4px;
      color: var(--ls-primary-text-color);
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
    }

    .${openIconName}:hover {
      opacity: 1;
      color: var(--ls-active-primary-color);
      transform: scale(1.1);
    }
  `);

  logseq.App.registerUIItem("toolbar", {
    key: openIconName,
    template: `
    <a data-on-click="show">
        <div class="${openIconName}">🤖</div>
    </a>    
`,
  });
}

// 渲染应用程序
function renderApp() {
  root.render(
    <React.StrictMode>
      <App chatContext={currentContext} />
    </React.StrictMode>
  );
}

// 主题初始化函数
async function initializeTheme() {
  try {
    // 初始化主题管理器
    const themeManager = getThemeManager();
    console.info(`#${pluginId}: 主题管理器已初始化`);
  } catch (error) {
    console.error("主题初始化失败:", error);
  }
}

logseq.ready(main).catch(console.error);
