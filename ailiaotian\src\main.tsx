import "@logseq/libs";

import React from "react";
import * as ReactDOM from "react-dom/client";
import App from "./App";
import "./index.css";
import { initializeSettings } from "./settings";
import { getContext, ContextData } from "./contextManager";
import { getThemeManager } from "./themeManager";

import { logseq as PL } from "../package.json";

// @ts-expect-error
const css = (t, ...args) => String.raw(t, ...args);

const pluginId = PL.id;
const root = ReactDOM.createRoot(document.getElementById("app")!);

// 当前上下文状态
let currentContext: ContextData | undefined;

async function main() {
  console.info(`#${pluginId}: MAIN`);

  // 初始化插件设置
  await initializeSettings();

  // 初始化主题检测
  await initializeTheme();
  
  // 确保设置页面正确注册
  logseq.useSettingsSchema([
    {
      key: "apiUrl",
      type: "string",
      default: "https://api.openai.com/v1/chat/completions",
      title: "API URL",
      description: "AI服务的API地址"
    },
    {
      key: "apiKey",
      type: "string",
      default: "",
      title: "API Key",
      description: "访问AI服务所需的API密钥"
    },
    {
      key: "modelName",
      type: "string",
      default: "gpt-3.5-turbo",
      title: "模型名称",
      description: "使用的AI模型名称"
    },
    {
      key: "enableHistory",
      type: "boolean",
      default: false,
      title: "启用历史记录",
      description: "是否保存聊天历史记录"
    }
  ]);
  
  renderApp();

  function createModel() {
    return {
      show() {
        currentContext = undefined;
        renderApp();
        logseq.showMainUI();
      },
      // 处理AI聊天命令
      async openAIChat() {
        const context = await getContext();
        console.log("获取到的上下文:", context);
        
        // 设置当前上下文并打开UI
        currentContext = context;
        renderApp();
        logseq.showMainUI();
      },
    };
  }

  const model = createModel();
  logseq.provideModel(model);
  logseq.setMainUIInlineStyle({
    zIndex: 9,
  });
  
  // 注册斜杠命令
  logseq.Editor.registerSlashCommand("AI聊天", async () => {
    return model.openAIChat();
  });
  
  // 注册快捷键
  logseq.App.registerCommandPalette({
    key: "open-ai-chat",
    label: "打开AI聊天",
    keybinding: {
      binding: "ctrl+g",
      mac: "cmd+g"
    }
  }, async () => {
    return model.openAIChat();
  });

  const openIconName = "ai-chat-plugin-open";

  logseq.provideStyle(css`
    .${openIconName} {
      opacity: 0.8;
      font-size: 22px;
      margin-top: 4px;
      color: var(--ls-primary-text-color);
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
    }

    .${openIconName}:hover {
      opacity: 1;
      color: var(--ls-active-primary-color);
      transform: scale(1.1);
    }
  `);

  logseq.App.registerUIItem("toolbar", {
    key: openIconName,
    template: `
    <a data-on-click="show">
        <div class="${openIconName}">🤖</div>
    </a>    
`,
  });
}

// 渲染应用程序
function renderApp() {
  root.render(
    <React.StrictMode>
      <App chatContext={currentContext} />
    </React.StrictMode>
  );
}

// 主题初始化函数
async function initializeTheme() {
  try {
    // 初始化主题管理器
    const themeManager = getThemeManager();
    console.info(`#${pluginId}: 主题管理器已初始化`);
  } catch (error) {
    console.error("主题初始化失败:", error);
  }
}

logseq.ready(main).catch(console.error);
