# AI聊天插件 - 快速开始指南

## 🚀 立即体验功能

### 1. 安装和启用

1. 确保插件已更新到最新版本
2. 重新加载Logseq或重启应用
3. 插件会自动迁移您的现有设置

### 2. 基础使用

#### 方式1：使用快捷键
- `Ctrl+G` (Windows/Linux) 或 `Cmd+G` (Mac) - 打开AI聊天
- `Ctrl+Shift+G` (Windows/Linux) 或 `Cmd+Shift+G` (Mac) - 快速AI回复

#### 方式2：使用斜杠命令
- 输入 `/AI聊天` - 打开聊天界面
- 输入 `/快速AI回复` - 直接插入AI回复

#### 方式3：使用工具栏
- 点击工具栏中的 🤖 图标

### 3. 上下文使用体验

#### 场景1：精确处理单个块
```
1. 将光标放在要处理的块中
2. 使用快捷键 Ctrl+G
3. 系统会检测当前块
4. AI只会看到当前块的内容
```

#### 场景2：获取前后文内容
```
1. 在设置中调整"上下文范围"
2. 设置为1-2获取前后文
3. 系统会获取相邻的块
4. AI能理解更多上下文信息
```

## ⚙️ 推荐设置

### 新手用户（简单易用）
```
📊 上下文范围: 0 (只获取当前块)
📄 包含页面标题: 是
🎨 主题检测: 是
```

### 进阶用户（平衡性能）
```
📊 上下文范围: 1 (获取前后各1个块)
📄 包含页面标题: 是
🎨 主题检测: 是
```

### 专业用户（最大功能）
```
📊 上下文范围: 2 (获取前后各2个块)
📄 包含页面标题: 是
🎨 主题检测: 是
```

## 🔧 设置说明

### 上下文范围设置

- **0**: 只获取当前块内容
  - 适合精确处理单个问题
  - 性能最佳
  - 避免无关信息干扰

- **1**: 获取当前块 + 前后各1个块
  - 适合需要少量上下文的场景
  - 平衡性能和信息量
  - 推荐日常使用

- **2**: 获取当前块 + 前后各2个块
  - 适合需要更多上下文的复杂分析
  - 提供更丰富的信息
  - 可能影响性能

### 其他重要设置

- **上下文范围**: 前后块数量（0=只当前块）
- **页面标题**: 在上下文中包含页面标题
- **主题检测**: 自动识别页面主题

## 🐛 故障排除

### 问题1：检测不到当前块
**解决方案：**
1. 确保光标在块内容中
2. 尝试点击块的文本区域
3. 检查浏览器控制台是否有错误

### 问题2：上下文不准确
**解决方案：**
1. 调整上下文范围设置
2. 检查是否选中了正确的块
3. 确认页面标题和主题检测设置

### 问题3：性能较慢
**解决方案：**
1. 设置上下文范围为0或1
2. 关闭主题检测功能
3. 减少上下文范围

### 问题4：AI回答不相关
**解决方案：**
1. 检查上下文内容是否正确
2. 调整上下文范围设置
3. 确认选中了正确的块

## 🧪 测试功能

### 查看调试信息
在控制台中查找以下日志：
```
块检测成功，UUID: xxx-xxx-xxx
上下文获取完成，返回 1 个块
```

## 📊 性能优化建议

### 日常使用
- 上下文范围: 0-1
- 包含页面标题: 是
- 主题检测: 是

### 复杂分析
- 上下文范围: 1-2
- 包含页面标题: 是
- 主题检测: 是

### 快速处理
- 上下文范围: 0
- 包含页面标题: 否
- 主题检测: 否

## 🆘 获取帮助

如果遇到问题：
1. 查看控制台错误信息
2. 尝试重置设置到默认值
3. 重新加载插件或重启Logseq
4. 查看详细的故障排除指南

## 🎉 享受AI聊天体验

简化的上下文系统让AI助手能够：
- 🎯 准确地理解您的内容
- 🧠 提供相关的回答
- ⚡ 快速响应您的需求
- 🔧 简单易用的配置

开始体验更简洁的AI助手吧！
