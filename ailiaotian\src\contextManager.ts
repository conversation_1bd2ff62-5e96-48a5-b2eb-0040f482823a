/**
 * 上下文源类型
 */
export enum ContextSourceType {
  SELECTION = 'selection',      // 选中文本
  CURRENT_BLOCK = 'block',      // 当前块
  SELECTED_BLOCKS = 'blocks',   // 选中的多个块
  NONE = 'none'                 // 无上下文
}

/**
 * 上下文数据接口
 */
export interface ContextData {
  type: ContextSourceType;      // 上下文类型
  content: string;              // 上下文内容文本
  blockUUIDs: string[];         // 相关的块UUID列表
}

/**
 * 编辑器光标位置接口
 */
interface CursorPosition {
  start: number;
  end: number;
}

/**
 * 获取当前编辑器中的选中文本
 * @returns 选中的文本，如果没有则返回空字符串
 */
async function getSelectedText(): Promise<string | null> {
  try {
    const position = await logseq.Editor.getEditingCursorPosition();
    if (position && position.pos) {
      const currentBlock = await logseq.Editor.getCurrentBlock();
      if (currentBlock) {
        // 提取选中的文本内容
        const content = currentBlock.content;
        // 处理位置对象
        const pos = position.pos as unknown as CursorPosition;
        // 如果有选中范围，返回选中的文本
        if (pos.start !== pos.end) {
          return content.substring(pos.start, pos.end);
        }
      }
    }
    return null;
  } catch (error) {
    console.error("获取选中文本失败:", error);
    return null;
  }
}

/**
 * 获取选中的多个块
 * @returns 选中的块数组，如果没有则返回空数组
 */
async function getSelectedBlocks() {
  try {
    const blocks = await logseq.Editor.getSelectedBlocks();
    return blocks || [];
  } catch (error) {
    console.error("获取选中块失败:", error);
    return [];
  }
}

/**
 * 获取当前块
 * @returns 当前块对象，如果没有则返回null
 */
async function getCurrentBlock() {
  try {
    return await logseq.Editor.getCurrentBlock();
  } catch (error) {
    console.error("获取当前块失败:", error);
    return null;
  }
}

/**
 * 获取当前上下文
 * @returns 上下文数据对象
 */
export async function getContext(): Promise<ContextData> {
  // 检查是否有选中的文本
  const selectedText = await getSelectedText();
  if (selectedText) {
    const currentBlock = await getCurrentBlock();
    return {
      type: ContextSourceType.SELECTION,
      content: selectedText,
      blockUUIDs: currentBlock ? [currentBlock.uuid] : []
    };
  }

  // 检查是否有选中的多个块
  const selectedBlocks = await getSelectedBlocks();
  if (selectedBlocks && selectedBlocks.length > 0) {
    // 组合选中块的内容
    const combinedContent = selectedBlocks.map(block => block.content).join("\n\n");
    return {
      type: ContextSourceType.SELECTED_BLOCKS,
      content: combinedContent,
      blockUUIDs: selectedBlocks.map(block => block.uuid)
    };
  }

  // 如果没有选中的内容，使用当前块
  const currentBlock = await getCurrentBlock();
  if (currentBlock) {
    return {
      type: ContextSourceType.CURRENT_BLOCK,
      content: currentBlock.content,
      blockUUIDs: [currentBlock.uuid]
    };
  }

  // 如果以上都没有，返回空上下文
  return {
    type: ContextSourceType.NONE,
    content: "",
    blockUUIDs: []
  };
} 