{"name": "logseq-plugin-ai-chat", "version": "0.1.0", "description": "在Logseq中使用自定义API进行AI聊天，支持智能上下文感知和完整暗色模式", "main": "dist/index.html", "scripts": {"dev": "vite", "build": "tsc && vite build", "preinstall": "npx only-allow pnpm"}, "license": "MIT", "dependencies": {"@logseq/libs": "^0.0.17", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^8.0.7", "eventsource-parser": "^1.0.0"}, "devDependencies": {"@semantic-release/changelog": "6.0.3", "@semantic-release/exec": "6.0.3", "@semantic-release/git": "10.0.1", "@semantic-release/npm": "10.0.6", "@types/node": "18.19.74", "@types/react": "18.3.18", "@types/react-dom": "18.3.5", "@typescript-eslint/eslint-plugin": "5.62.0", "@typescript-eslint/parser": "5.62.0", "@vitejs/plugin-react": "3.1.0", "autoprefixer": "10.4.20", "conventional-changelog-conventionalcommits": "5.0.0", "eslint": "8.57.1", "eslint-plugin-react": "7.37.4", "eslint-plugin-react-hooks": "4.6.2", "postcss": "8.5.1", "semantic-release": "21.1.2", "tailwindcss": "3.4.17", "typescript": "4.9.5", "vite": "4.5.9", "vite-plugin-logseq": "1.1.2"}, "logseq": {"id": "logseq-plugin-ai-chat", "icon": "./logo.svg", "title": "AI 聊天助手", "description": "在Logseq中使用自定义API进行AI聊天，支持智能上下文感知和完整暗色模式"}}