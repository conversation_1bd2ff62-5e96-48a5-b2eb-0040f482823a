/**
 * 插件设置接口定义
 */
export interface PluginSettings {
  apiUrl: string;
  apiKey: string;
  modelName: string;
  temperature: number;
  maxTokens: number;
  enableHistory: boolean;
  customPrompts: Array<{name: string, prompt: string}>;
}

/**
 * 默认设置值
 */
export const DEFAULT_SETTINGS: PluginSettings = {
  apiUrl: "https://api.openai.com/v1/chat/completions",
  apiKey: "",
  modelName: "gpt-3.5-turbo",
  temperature: 0.7,
  maxTokens: 2000,
  enableHistory: false,
  customPrompts: [
    {
      name: "总结",
      prompt: "请总结以下内容的要点："
    },
    {
      name: "扩展",
      prompt: "请基于以下内容进行扩展和补充："
    }
  ]
};

/**
 * 初始化插件设置
 * @returns 合并后的设置对象
 */
export async function initializeSettings(): Promise<PluginSettings> {
  // 获取当前设置，使用兼容的API
  const currentSettings = logseq.settings || {};
  const mergedSettings = Object.assign({}, DEFAULT_SETTINGS, currentSettings);
  
  // 确保customPrompts字段存在且为数组
  if (!Array.isArray(mergedSettings.customPrompts)) {
    mergedSettings.customPrompts = DEFAULT_SETTINGS.customPrompts;
  }
  
  // 确保新添加的字段有默认值
  if (mergedSettings.temperature === undefined) {
    mergedSettings.temperature = DEFAULT_SETTINGS.temperature;
  }
  
  if (mergedSettings.maxTokens === undefined) {
    mergedSettings.maxTokens = DEFAULT_SETTINGS.maxTokens;
  }
  
  // 保存合并后的设置
  await logseq.updateSettings(mergedSettings);
  
  return mergedSettings;
}

/**
 * 更新插件设置
 * @param settings 要更新的设置对象或部分设置
 */
export async function updateSettings(settings: Partial<PluginSettings>): Promise<void> {
  await logseq.updateSettings(settings);
}

/**
 * 获取当前插件设置
 * @returns 当前设置对象
 */
export async function getSettings(): Promise<PluginSettings> {
  // 获取当前设置，使用兼容的API
  const settings = logseq.settings || {};
  return Object.assign({}, DEFAULT_SETTINGS, settings) as PluginSettings;
} 