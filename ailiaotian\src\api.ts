/**
 * API 集成功能模块
 * 处理与 AI 服务的通信
 */
import { PluginSettings, getSettings } from './settings';
import { ContextData } from './contextManager';

/**
 * 聊天消息接口
 */
export interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

/**
 * API 请求配置接口
 */
interface ApiRequestConfig {
  messages: ChatMessage[];
  stream: boolean;
  model: string;
  temperature?: number;
  max_tokens?: number;
}

/**
 * API 响应处理接口
 */
export interface ApiResponseHandlers {
  onChunk: (chunk: string) => void;
  onComplete: (fullResponse: string) => void;
  onError: (error: string) => void;
}

/**
 * API 连接错误类
 */
class ApiConnectionError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'ApiConnectionError';
  }
}

/**
 * 创建聊天请求消息
 * @param userMessage 用户输入的消息
 * @param context 上下文数据
 * @param systemPrompt 系统提示（可选）
 * @returns 聊天消息数组
 */
function createChatMessages(
  userMessage: string,
  context: ContextData,
  systemPrompt?: string
): ChatMessage[] {
  const messages: ChatMessage[] = [];

  // 添加系统提示（如果有）
  if (systemPrompt) {
    messages.push({
      role: 'system',
      content: systemPrompt
    });
  } else {
    // 默认系统提示
    messages.push({
      role: 'system',
      content: '你是一个有用的助手，可以帮助用户处理和分析文本内容。提供简洁、准确的回答。'
    });
  }

  // 构建用户消息，包含上下文信息
  let userContent = userMessage;

  // 如果用户消息没有包含上下文内容，则添加上下文前缀
  if (context.content && !userMessage.includes(context.content)) {
    // 创建上下文前缀
    let contextPrefix = '';
    switch (context.type) {
      case 'selection':
        contextPrefix = '以下是选中的文本：\n\n';
        break;
      case 'block':
        contextPrefix = '以下是当前块的内容：\n\n';
        break;
      case 'blocks':
        contextPrefix = '以下是选中的多个块：\n\n';
        break;
      default:
        break;
    }

    userContent = `${contextPrefix}${context.content}\n\n${userMessage}`;
  }

  // 添加用户消息
  messages.push({
    role: 'user',
    content: userContent
  });

  return messages;
}

/**
 * 解析流式响应的数据块
 * @param chunk 响应数据块
 * @returns 提取的文本内容
 */
function parseStreamChunk(chunk: string): string {
  try {
    // 处理多行数据
    const lines = chunk
      .split('\n')
      .filter(line => line.trim() !== '' && line.trim() !== 'data: [DONE]');

    // 提取所有行中的内容
    let content = '';
    for (const line of lines) {
      // 如果以 data: 开头，则为 SSE 格式的数据
      if (line.startsWith('data: ')) {
        // 提取 data: 前缀之后的 JSON
        const jsonStr = line.replace(/^data: /, '');
        if (!jsonStr || jsonStr === '[DONE]') continue;

        try {
          const json = JSON.parse(jsonStr);
          
          // 支持不同 API 提供商的响应格式
          // 1. OpenAI 格式
          if (json.choices && json.choices[0]) {
            // OpenAI 流式格式
            if (json.choices[0].delta && json.choices[0].delta.content) {
              content += json.choices[0].delta.content;
            }
            // OpenAI 非流式格式
            else if (json.choices[0].message && json.choices[0].message.content) {
              content += json.choices[0].message.content;
            }
          }
          // 2. 通用格式：直接包含内容的字段
          else if (json.content) {
            content += json.content;
          }
          // 3. 其他自定义 API 格式
          else if (json.text) {
            content += json.text;
          }
        } catch (e) {
          // 解析 JSON 失败，但我们不将原始 JSON 字符串添加到内容中
          console.warn('解析 JSON 失败:', jsonStr);
        }
      } 
      // 不再处理非 SSE 格式的纯文本响应，以避免元数据混入
    }

    return content;
  } catch (error) {
    console.error('解析数据块失败:', error);
    return '';
  }
}

/**
 * 发送聊天请求到 API
 * @param userMessage 用户输入消息
 * @param context 上下文数据
 * @param handlers 响应处理程序
 * @param systemPrompt 系统提示（可选）
 */
export async function sendChatRequest(
  userMessage: string,
  context: ContextData,
  handlers: ApiResponseHandlers,
  systemPrompt?: string
): Promise<void> {
  try {
    // 获取设置
    const settings = await getSettings();
    
    // 验证设置
    if (!settings.apiUrl || !settings.apiKey) {
      throw new ApiConnectionError('API URL 或 API Key 未配置');
    }
    
    // 创建消息数组
    const messages = createChatMessages(userMessage, context, systemPrompt);
    
    // 准备请求配置
    const requestConfig: ApiRequestConfig = {
      messages,
      stream: true,
      model: settings.modelName
    };
    
    // 添加可选参数
    if (settings.temperature !== undefined) {
      requestConfig.temperature = settings.temperature;
    }
    
    if (settings.maxTokens !== undefined && settings.maxTokens > 0) {
      requestConfig.max_tokens = settings.maxTokens;
    }
    
    // 发送请求
    const response = await fetch(settings.apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${settings.apiKey}`
      },
      body: JSON.stringify(requestConfig)
    });
    
    // 检查响应状态
    if (!response.ok) {
      let errorMessage = `API 请求失败: ${response.status} ${response.statusText}`;
      try {
        const errorData = await response.json();
        errorMessage = `${errorMessage}. ${errorData.error?.message || JSON.stringify(errorData)}`;
      } catch (e) {
        // 如果解析 JSON 失败，使用默认错误消息
      }
      throw new ApiConnectionError(errorMessage);
    }
    
    // 处理流式响应
    if (response.body) {
      const reader = response.body.getReader();
      const decoder = new TextDecoder('utf-8');
      let completeResponse = '';
      let buffer = '';
      let lastFlushTime = Date.now();
      const FLUSH_INTERVAL = 50; // 每50毫秒更新一次UI
      
      // 读取流
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) {
          // 最后一次刷新
          if (buffer.length > 0) {
            const content = parseStreamChunk(buffer);
            if (content) {
              completeResponse += content;
              handlers.onChunk(content);
            }
          }
          handlers.onComplete(completeResponse);
          break;
        }
        
        // 将二进制数据解码为文本
        const chunk = decoder.decode(value, { stream: true });
        buffer += chunk;
        
        // 处理完整的 SSE 消息
        // 查找所有完整的 data: 行，确保我们只处理完整的消息
        const dataLines = buffer.split('\n\n');
        
        // 保留最后一个可能不完整的块
        const lastChunk = dataLines.pop() || '';
        
        // 处理所有完整的块
        if (dataLines.length > 0) {
          const content = parseStreamChunk(dataLines.join('\n\n'));
          if (content) {
            completeResponse += content;
            handlers.onChunk(content);
          }
        }
        
        // 更新缓冲区为最后一个可能不完整的块
        buffer = lastChunk;
        
        // 如果自上次刷新以来已经过去了足够的时间，也处理缓冲区
        const now = Date.now();
        if (now - lastFlushTime >= FLUSH_INTERVAL && buffer.includes('data: ')) {
          const content = parseStreamChunk(buffer);
          if (content) {
            completeResponse += content;
            handlers.onChunk(content);
            buffer = ''; // 清空已处理的缓冲区
          }
          lastFlushTime = now;
        }
      }
    } else {
      throw new ApiConnectionError('API 响应中没有正文');
    }
  } catch (error) {
    if (error instanceof ApiConnectionError) {
      handlers.onError(error.message);
    } else {
      handlers.onError(`请求失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
} 