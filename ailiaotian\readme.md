# AI聊天助手插件 🤖

一个功能强大的Logseq AI聊天插件，支持自定义API、智能上下文感知和完整的暗色模式。

## ✨ 主要功能

### 🤖 AI对话集成
- 在Logseq中直接与AI进行对话
- 支持自定义API（OpenAI兼容格式）
- 流式响应，实时显示AI回复
- 支持多种AI模型（GPT-3.5、GPT-4等）

### 🧠 智能上下文感知
- 自动检测当前编辑的块内容
- 支持选中文本或多个块作为上下文
- 可配置上下文范围（获取前后相邻块）
- 包含页面标题和主题信息

### 🌙 完整暗色模式支持
- **自动跟随Logseq主题**：无缝集成系统主题设置
- **完整UI适配**：所有组件都有对应的暗色样式
- **平滑过渡**：主题切换时的优雅动画效果
- **智能颜色系统**：优化的对比度确保最佳可读性

### 🎨 多种交互方式
- **快捷键**：`Ctrl+G` (Windows/Linux) 或 `Cmd+G` (Mac)
- **斜杠命令**：输入 `/AI聊天`
- **工具栏按钮**：点击🤖图标
- **命令面板**：通过命令面板调用

### ⚙️ 灵活的内容处理
- **替换原文**：用AI回复替换选中的内容
- **插入子块**：将AI回复作为子块插入到当前块下
- **格式化输出**：自动将AI回复格式化为Logseq块结构

### 🎯 自定义提示词系统
- 内置常用提示词（总结、扩展等）
- 支持自定义提示词管理
- 提示词自动补全和建议

### ⌨️ 自定义快捷键设置
- **可配置快捷键**：支持自定义所有快捷键组合
- **分平台设置**：Windows/Linux和Mac分别配置
- **智能验证**：实时验证格式和冲突检测
- **热更新**：设置后立即生效，无需重启

## 🚀 快速开始

### 安装插件
1. 下载最新版本的插件包
2. 在Logseq中启用开发者模式
3. 选择"加载未打包的插件"
4. 选择插件目录（包含package.json的目录）
5. 插件将自动加载并可用

### 基本配置
1. 点击工具栏中的🤖图标打开设置
2. 在"API配置"中填入您的API信息：
   - **API URL**：AI服务的API地址
   - **API Key**：访问AI服务所需的密钥
   - **模型名称**：使用的AI模型（如gpt-3.5-turbo）
3. 保存设置即可开始使用

### 主题设置
- 插件会自动跟随Logseq的主题设置
- 在设置页面的"界面设置"中可以查看当前主题状态
- 支持亮色模式、暗色模式和自动跟随系统

## 🎯 使用场景

### 内容处理
- 总结长文本内容
- 扩展和补充现有内容
- 翻译和改写文本
- 回答基于上下文的问题

### 写作辅助
- 基于现有内容生成新想法
- 优化文本表达
- 提供不同角度的观点
- 格式化和结构化内容

### 知识管理
- 分析和解释复杂概念
- 生成相关问题和思考点
- 创建内容大纲和结构
- 提取关键信息

## 🔧 技术特点

### 现代化架构
- 基于React + TypeScript开发
- 使用Vite构建工具
- TailwindCSS样式框架
- 完整的类型安全

### 暗色模式技术
- 智能主题检测和同步
- CSS自定义属性系统
- 平滑过渡动画
- 完整的组件适配

### 用户体验优化
- 流式响应显示
- 智能块检测
- 多重降级机制
- 详细的错误处理

## 📚 文档

- [快速开始指南](QUICK_START_GUIDE.md) - 详细的使用教程
- [快捷键设置指南](KEYBINDING_SETTINGS_GUIDE.md) - 自定义快捷键功能说明
- [暗色模式指南](DARK_MODE_GUIDE.md) - 暗色模式功能说明
- [上下文刷新功能](CONTEXT_REFRESH_FEATURE.md) - 上下文管理功能
- [重新设计总结](REDESIGN_SUMMARY.md) - 架构设计说明

## 🐛 故障排除

### 常见问题
1. **API连接失败**：检查API URL和密钥是否正确
2. **主题不同步**：重新加载插件或重启Logseq
3. **上下文检测失败**：确保光标在块内容中
4. **样式显示异常**：清除浏览器缓存

### 获取帮助
- 查看控制台错误信息
- 尝试重置设置到默认值
- 重新加载插件或重启Logseq
- 查看详细的故障排除指南

## 🎉 开始体验

现在就开始使用AI聊天助手，享受智能化的笔记体验吧！

- 🎯 准确理解您的内容上下文
- 🧠 提供相关和有用的回答
- ⚡ 快速响应您的需求
- 🌙 舒适的暗色模式体验
- 🔧 简单易用的配置界面
