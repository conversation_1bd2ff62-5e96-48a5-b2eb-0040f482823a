# 上下文自动刷新功能说明

## 功能概述

全新升级的上下文刷新功能现在支持自动检测和手动刷新两种模式。系统能够智能检测用户的块切换和内容变化，自动更新对话框中的上下文内容，提供更加流畅的用户体验。

## 功能特点

1. **🔄 自动刷新模式**: 实时检测块变化，自动更新上下文内容
2. **🎯 自动检测**: 检测块切换、内容变化、选择变化等多种情况
3. **⚡ 快速响应**: 1.5秒检测间隔，快速响应用户操作
4. **🔧 手动刷新**: 保留手动刷新功能，用户可随时主动更新
5. **💡 状态指示**: 可视化显示自动刷新状态和工作状态
6. **🎨 动画效果**: 优雅的动画效果提升用户体验
7. **🧠 上下文支持**: 完全兼容上下文模式
8. **🔇 静默模式**: 自动刷新时不显示提示消息，避免干扰
9. **💾 状态持久化**: 自动刷新设置会被保存，下次打开时自动恢复
10. **🎛️ 设置集成**: 可在设置页面中配置自动刷新功能

## 使用方法

### 自动刷新模式
1. 打开AI聊天对话框（使用快捷键 `Ctrl+G` 或斜杠命令 `/AI聊天`）
2. 点击上下文框右上角的自动刷新按钮（⚙️图标）启用自动刷新
3. 系统会显示"自动刷新"状态指示器，带有绿色脉冲动画
4. 在Logseq中切换到不同的块或修改块内容
5. 上下文框中的内容将自动更新，无需手动操作

### 手动刷新模式
1. 打开AI聊天对话框
2. 在Logseq中切换到不同的块或选择不同的内容
3. 点击上下文框右上角的手动刷新按钮（🔄图标）
4. 上下文框中的内容将立即更新为当前块的内容

### 状态指示
- **绿色脉冲点 + "自动刷新"**: 自动刷新模式已启用
- **蓝色高亮按钮**: 自动刷新按钮处于激活状态
- **旋转动画**: 正在执行刷新操作

### 设置管理
1. 打开插件设置页面
2. 展开"界面设置"部分
3. 在界面设置中可以配置：
   - **启用自动刷新上下文**：开启/关闭自动刷新功能
   - **上下文范围**：设置获取前后块的数量（0-10）
   - **包含页面标题**：是否在上下文中包含页面标题
   - **启用主题检测**：是否自动检测页面主题
4. 保存设置，所有配置会在所有对话框中生效

## 技术实现

### 主要修改文件

1. **ChatModal.tsx**: 
   - 添加了 `onRefreshContext` 属性
   - 新增 `handleRefreshContext` 函数
   - 在上下文显示区域添加刷新按钮

2. **App.tsx**:
   - 导入 `getContext` 函数
   - 新增 `handleRefreshContext` 回调函数
   - 将回调函数传递给 ChatModal 组件

3. **settings.ts**:
   - 在 `UISettings` 接口中添加 `autoRefreshContext` 字段
   - 在默认设置中添加自动刷新的默认值
   - 在初始化函数中添加兼容性检查

4. **SettingsUI.tsx**:
   - 添加UI设置折叠面板
   - 新增自动刷新上下文的配置选项
   - 添加相应的事件处理函数

### 核心逻辑

#### 自动检测机制
```typescript
const checkAndRefreshContext = async () => {
  try {
    const currentBlock = await logseq.Editor.getCurrentBlock();
    if (!currentBlock) return;

    const currentBlockUUID = currentBlock.uuid;
    const currentContent = currentBlock.content;
    const contextBlockUUIDs = context?.blockUUIDs || [];

    // 检查是否需要刷新：
    // 1. 当前块UUID发生变化
    // 2. 当前块不在上下文块列表中
    // 3. 当前块内容发生变化（针对同一个块）
    const blockChanged = lastCheckedBlockUUID !== currentBlockUUID;
    const blockNotInContext = !contextBlockUUIDs.includes(currentBlockUUID);
    const contentChanged = lastCheckedBlockUUID === currentBlockUUID &&
                          lastCheckedContent !== currentContent;

    if (blockChanged || blockNotInContext || contentChanged) {
      await handleRefreshContext(true); // 静默刷新
    }
  } catch (error) {
    console.error('自动刷新检查失败:', error);
  }
};
```

#### 刷新处理函数
```typescript
const handleRefreshContext = async (isAutoRefresh = false) => {
  try {
    setError('');
    if (isAutoRefresh) {
      setIsAutoRefreshing(true); // 显示旋转动画
    }
    await onRefreshContext();
    if (!isAutoRefresh) {
      logseq.UI.showMsg("上下文已刷新", "success"); // 只在手动刷新时显示提示
    }
  } catch (error) {
    console.error('刷新上下文失败:', error);
    if (!isAutoRefresh) {
      setError('刷新上下文失败，请重试');
      logseq.UI.showMsg("刷新上下文失败", "error");
    }
  } finally {
    if (isAutoRefresh) {
      setIsAutoRefreshing(false);
    }
  }
};
```

## 用户体验改进

1. **🎨 丰富的视觉反馈**:
   - 自动刷新按钮的蓝色高亮状态
   - 手动刷新按钮的旋转动画
   - 绿色脉冲状态指示器

2. **⚡ 智能状态管理**:
   - 在加载时禁用按钮，防止重复操作
   - 自动刷新时的静默模式，避免频繁提示
   - 手动操作时的明确反馈

3. **🛡️ 完善的错误处理**:
   - 自动刷新失败时静默处理，不干扰用户
   - 手动刷新失败时提供明确的错误信息
   - 网络异常时的优雅降级

4. **🔔 合理的消息提示**:
   - 启用/关闭自动刷新时的状态提示
   - 手动刷新成功的确认消息
   - 避免自动刷新时的重复提示

## 兼容性

- 与现有的上下文功能完全兼容
- 支持所有上下文类型（当前块、选中文本、选中块等）
- 保持原有的上下文设置（范围、主题检测等）

## 技术特性

### 检测机制
- **块切换检测**: 监控当前活动块的UUID变化
- **内容变化检测**: 监控同一块内容的修改
- **上下文匹配检测**: 检查当前块是否在上下文列表中
- **自动去重**: 避免不必要的重复刷新操作

### 性能优化
- **1.5秒检测间隔**: 平衡响应速度和性能消耗
- **状态缓存**: 缓存上次检测的块信息，减少重复计算
- **异步处理**: 所有检测和刷新操作都是异步的，不阻塞UI
- **错误隔离**: 单次检测失败不影响后续检测

### 兼容性保证
- **完全向后兼容**: 不影响现有的手动刷新功能
- **上下文支持**: 完全兼容上下文模式
- **设置保持**: 保持用户的所有上下文设置（范围、主题检测等）
- **多模式支持**: 支持所有上下文类型（当前块、选中文本、选中块等）

## 未来扩展可能性

1. **⚙️ 可配置检测间隔**: 允许用户自定义检测频率
2. **📊 刷新统计**: 显示自动刷新的统计信息
3. **🎯 选择性刷新**: 允许用户选择哪些类型的变化触发刷新
4. **🔄 刷新历史**: 记录和回溯刷新历史
5. **🚀 预测性刷新**: 基于用户行为模式预测刷新需求
