{"name": "logseq-plugin-ai-chat", "version": "0.24.0", "description": "在Logseq中使用自定义API进行AI聊天，支持上下文获取和可调整界面", "main": "dist/index.html", "scripts": {"dev": "vite", "build": "tsc && vite build", "preinstall": "npx only-allow pnpm"}, "license": "MIT", "dependencies": {"@babel/helper-string-parser": "^7.25.9", "@babel/helper-validator-identifier": "^7.25.9", "@babel/parser": "^7.27.0", "@logseq/libs": "^0.0.17", "debug": "^4.4.0", "eventsource-parser": "^1.0.0", "gensync": "1.0.0-beta.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^8.0.7"}, "devDependencies": {"@alloc/quick-lru": "^5.2.0", "@babel/core": "^7.26.10", "@babel/types": "^7.27.0", "@jridgewell/sourcemap-codec": "^1.5.0", "@semantic-release/changelog": "6.0.3", "@semantic-release/exec": "6.0.3", "@semantic-release/git": "10.0.1", "@semantic-release/npm": "10.0.6", "@types/hast": "^3.0.4", "@types/node": "18.19.74", "@types/react": "18.3.18", "@types/react-dom": "18.3.5", "@types/unist": "^3.0.3", "@typescript-eslint/eslint-plugin": "5.62.0", "@typescript-eslint/parser": "5.62.0", "@vitejs/plugin-react": "3.1.0", "autoprefixer": "10.4.20", "camelcase-css": "^2.0.1", "conventional-changelog-conventionalcommits": "5.0.0", "dlv": "^1.1.3", "eslint": "8.57.1", "eslint-plugin-react": "7.37.4", "eslint-plugin-react-hooks": "4.6.2", "is-extglob": "^2.1.1", "is-glob": "^4.0.3", "magic-string": "^0.30.17", "ms": "^2.1.3", "nanoid": "^5.1.5", "object-hash": "^3.0.0", "picocolors": "^1.1.1", "postcss": "8.5.1", "postcss-js": "^4.0.1", "postcss-nested": "^7.0.2", "postcss-selector-parser": "^7.1.0", "semantic-release": "21.1.2", "source-map-js": "^1.2.1", "tailwindcss": "3.4.17", "typescript": "4.9.5", "vite": "4.5.9", "vite-plugin-logseq": "1.1.2"}, "logseq": {"id": "logseq-plugin-ai-chat", "icon": "./logo.svg", "title": "AI 聊天助手", "description": "在Logseq中使用自定义API进行AI聊天，支持上下文获取、页面主题识别、可调整大小窗口和分离式界面"}}