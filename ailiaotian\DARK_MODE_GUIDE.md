# 暗色模式功能指南

## 🌙 新增功能概述

AI聊天插件现已全面支持暗色模式，提供更舒适的夜间使用体验。暗色模式会自动跟随Logseq的主题设置，并提供了丰富的自定义选项。

## ✨ 主要特性

### 1. 自动主题同步
- **智能跟随**：自动检测并跟随Logseq的主题设置
- **实时切换**：当Logseq切换主题时，插件界面会立即响应
- **系统集成**：支持跟随系统的暗色模式偏好

### 2. 完整的UI适配
- **模态框**：聊天窗口和设置页面完全适配暗色模式
- **按钮和输入框**：所有交互元素都有对应的暗色样式
- **文本和边框**：优化的对比度确保良好的可读性
- **滚动条**：自定义滚动条样式适配暗色主题

### 3. 主题管理系统
- **主题管理器**：统一管理主题状态和切换逻辑
- **CSS变量**：使用CSS自定义属性实现动态主题切换
- **平滑过渡**：主题切换时的平滑动画效果

## 🎨 视觉效果

### 亮色模式
- 背景色：纯白色和浅灰色
- 文字色：深灰色和黑色
- 强调色：蓝色系

### 暗色模式
- 背景色：深灰色和黑色
- 文字色：浅灰色和白色
- 强调色：亮蓝色系

## 🔧 技术实现

### 1. 主题检测
```typescript
// 自动检测Logseq主题
const currentTheme = await logseq.App.getStateFromStore("ui/theme");

// 监听主题变化
logseq.App.onThemeModeChanged(({ mode }) => {
  // 更新插件主题
});
```

### 2. CSS适配
```css
/* 亮色模式 */
.apple-modal {
  background-color: rgba(255, 255, 255, 0.95);
  color: #333333;
}

/* 暗色模式 */
.dark .apple-modal {
  background-color: rgba(26, 26, 26, 0.95);
  color: #e0e0e0;
}
```

### 3. 动态样式
- 使用CSS自定义属性实现动态颜色
- JavaScript动态设置主题相关的样式属性
- 组件级别的主题状态管理

## 🚀 使用方法

### 自动模式（推荐）
1. 在Logseq中切换到暗色主题
2. 插件会自动切换到暗色模式
3. 无需额外配置

### 设置页面
1. 打开插件设置页面
2. 在"界面设置"部分可以看到主题状态
3. 主题切换组件显示当前主题模式

## 🎯 支持的组件

### 完全适配的组件
- ✅ 聊天模态框
- ✅ 设置页面
- ✅ 输入框和按钮
- ✅ 下拉选择框
- ✅ 提示词建议
- ✅ 错误和状态消息
- ✅ 滚动条

### AI响应内容样式
- ✅ 代码块高亮
- ✅ 引用块样式
- ✅ 表格样式
- ✅ 链接颜色
- ✅ 列表样式
- ✅ 分隔线
- ✅ 强调文本

## 🔍 技术细节

### 主题管理器类
```typescript
export class ThemeManager {
  // 单例模式
  private static instance: ThemeManager;
  
  // 主题状态管理
  private currentTheme: ThemeState;
  
  // 监听器管理
  private listeners: Array<(theme: ThemeState) => void>;
}
```

### 主题状态接口
```typescript
export interface ThemeState {
  mode: 'light' | 'dark' | 'auto';
  isDark: boolean;
  systemPrefersDark: boolean;
}
```

### CSS变量系统
```css
:root {
  --theme-bg-primary: #ffffff;
  --theme-text-primary: #333333;
  --theme-accent: #007AFF;
}

.dark {
  --theme-bg-primary: #1a1a1a;
  --theme-text-primary: #e0e0e0;
  --theme-accent: #0A84FF;
}
```

## 🐛 故障排除

### 主题不同步
1. 检查Logseq版本是否支持主题API
2. 重新加载插件
3. 检查浏览器控制台是否有错误

### 样式显示异常
1. 清除浏览器缓存
2. 重启Logseq应用
3. 检查是否有其他插件冲突

### 性能问题
1. 主题切换使用了平滑过渡动画
2. 如果感觉卡顿，可以通过CSS禁用动画
3. 大量内容时可能需要稍等片刻

## 🔮 未来计划

### 即将推出
- 🎨 更多主题颜色选项
- 🌈 自定义颜色配置
- 📱 移动端适配优化
- ⚡ 性能进一步优化

### 长期规划
- 🎭 多套主题模板
- 🎪 主题导入导出
- 🎨 可视化主题编辑器
- 🌟 社区主题分享

## 📝 更新日志

### v0.1.0 (当前版本)
- ✅ 基础暗色模式支持
- ✅ 自动主题同步
- ✅ 完整UI组件适配
- ✅ 主题管理系统
- ✅ CSS变量系统
- ✅ 平滑过渡动画

---

享受更舒适的暗色模式体验！🌙✨
