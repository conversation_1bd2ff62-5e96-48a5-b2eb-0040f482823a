# 快捷键设置功能指南

## 🎯 功能概述

AI聊天插件现在支持自定义快捷键设置，用户可以根据个人习惯和系统环境自定义快捷键组合。

## ✨ 主要特性

### 🔧 可配置的快捷键
- **打开AI聊天**: 打开AI聊天对话框
- **快速AI回复**: 快速AI回复功能（预留）
- **分平台设置**: 支持Windows/Linux和Mac不同的快捷键配置

### 🛡️ 智能验证系统
- **格式验证**: 自动验证快捷键格式是否正确
- **冲突检测**: 检测与系统快捷键和Logseq快捷键的冲突
- **重复检测**: 防止内部快捷键重复配置
- **实时反馈**: 输入时即时显示验证结果

### 🎨 用户友好界面
- **直观预览**: 显示当前系统的快捷键格式
- **错误提示**: 清晰的错误信息和警告提示
- **平台适配**: 自动检测操作系统并显示对应格式

## 🚀 使用方法

### 1. 打开设置页面
- 点击工具栏中的🤖图标
- 或使用当前的快捷键打开AI聊天，然后点击设置

### 2. 配置快捷键
1. 在设置页面找到"快捷键设置"部分
2. 分别为Windows/Linux和Mac配置快捷键
3. 系统会实时验证输入的快捷键格式
4. 如有冲突或错误，会显示相应的提示信息

### 3. 保存设置
- 点击"保存设置"按钮
- 系统会自动重新注册快捷键
- 新的快捷键立即生效

## 📝 快捷键格式说明

### 支持的修饰键
- `ctrl` - Ctrl键 (Windows/Linux)
- `cmd` - Command键 (Mac)
- `alt` - Alt键
- `shift` - Shift键
- `meta` - Meta键

### 支持的按键
- **字母**: a-z
- **数字**: 0-9
- **功能键**: f1-f12
- **特殊键**: space, enter, tab, escape, backspace, delete, home, end, pageup, pagedown, insert, up, down, left, right

### 格式示例
```
ctrl+g          # Ctrl + G
cmd+shift+a     # Command + Shift + A
alt+f1          # Alt + F1
ctrl+space      # Ctrl + Space
```

## ⚠️ 注意事项

### 系统快捷键冲突
系统会检测并警告以下类型的冲突：
- **系统快捷键**: 如 Ctrl+C (复制), Ctrl+V (粘贴) 等
- **Logseq快捷键**: 如 Ctrl+K (搜索), Ctrl+/ (帮助) 等
- **内部冲突**: 插件内部不同功能使用相同快捷键

### 最佳实践
1. **避免常用系统快捷键**: 不要使用 Ctrl+C, Ctrl+V 等常用快捷键
2. **使用组合键**: 建议使用多个修饰键组合，如 Ctrl+Alt+G
3. **平台一致性**: 尽量保持Windows/Linux和Mac版本的逻辑一致性
4. **测试验证**: 设置后测试快捷键是否正常工作

## 🔧 技术实现

### 动态注册机制
- 快捷键在插件启动时动态注册
- 设置更新时自动重新注册
- 支持热更新，无需重启Logseq

### 验证算法
- 多层次验证：格式 → 冲突 → 重复
- 实时反馈机制
- 智能建议系统

## 🐛 故障排除

### 快捷键不生效
1. 检查快捷键格式是否正确
2. 确认没有冲突警告
3. 尝试重新保存设置
4. 重启Logseq插件

### 冲突警告
1. 查看具体的冲突信息
2. 选择不同的快捷键组合
3. 参考建议的快捷键格式

### 格式错误
1. 检查修饰键拼写是否正确
2. 确认按键名称是否支持
3. 验证是否有重复的修饰键

## 🎉 默认快捷键

| 功能 | Windows/Linux | Mac | 描述 |
|------|---------------|-----|------|
| 打开AI聊天 | `Ctrl+G` | `Cmd+G` | 打开AI聊天对话框 |
| 快速AI回复 | `Ctrl+Shift+G` | `Cmd+Shift+G` | 快速AI回复功能 |

## 🔮 未来扩展

- 支持更多快捷键功能
- 快捷键导入/导出
- 快捷键使用统计
- 智能快捷键推荐
