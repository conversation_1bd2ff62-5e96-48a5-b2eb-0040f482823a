/**
 * 主题管理器
 * 处理暗色模式和主题切换相关功能
 */

export type ThemeMode = 'light' | 'dark' | 'auto';

/**
 * 主题状态接口
 */
export interface ThemeState {
  mode: ThemeMode;
  isDark: boolean;
  systemPrefersDark: boolean;
}

/**
 * 主题管理器类
 */
export class ThemeManager {
  private static instance: ThemeManager;
  private currentTheme: ThemeState;
  private listeners: Array<(theme: ThemeState) => void> = [];
  private mediaQuery: MediaQueryList;

  private constructor() {
    // 初始化媒体查询
    this.mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    // 初始化主题状态
    this.currentTheme = {
      mode: 'auto',
      isDark: false,
      systemPrefersDark: this.mediaQuery.matches
    };

    // 监听系统主题变化
    this.mediaQuery.addEventListener('change', this.handleSystemThemeChange.bind(this));
    
    // 初始化主题
    this.initializeTheme();
  }

  /**
   * 获取主题管理器实例
   */
  public static getInstance(): ThemeManager {
    if (!ThemeManager.instance) {
      ThemeManager.instance = new ThemeManager();
    }
    return ThemeManager.instance;
  }

  /**
   * 初始化主题
   */
  private async initializeTheme(): Promise<void> {
    try {
      // 从Logseq获取当前主题
      const logseqTheme = await logseq.App.getStateFromStore("ui/theme");
      
      // 更新主题状态
      this.currentTheme.isDark = logseqTheme === "dark";
      this.currentTheme.mode = logseqTheme === "dark" ? 'dark' : 'light';
      
      // 应用主题
      this.applyTheme();
      
      // 监听Logseq主题变化
      logseq.App.onThemeModeChanged(({ mode }) => {
        this.currentTheme.isDark = mode === "dark";
        this.currentTheme.mode = mode === "dark" ? 'dark' : 'light';
        this.applyTheme();
        this.notifyListeners();
      });
      
    } catch (error) {
      console.error("主题初始化失败:", error);
      // 降级到系统主题
      this.currentTheme.isDark = this.currentTheme.systemPrefersDark;
      this.applyTheme();
    }
  }

  /**
   * 处理系统主题变化
   */
  private handleSystemThemeChange = (e: MediaQueryListEvent): void => {
    this.currentTheme.systemPrefersDark = e.matches;
    
    // 如果当前是自动模式，更新主题
    if (this.currentTheme.mode === 'auto') {
      this.currentTheme.isDark = e.matches;
      this.applyTheme();
      this.notifyListeners();
    }
  };

  /**
   * 应用主题到DOM
   */
  private applyTheme(): void {
    const htmlElement = document.documentElement;
    
    if (this.currentTheme.isDark) {
      htmlElement.classList.add('dark');
    } else {
      htmlElement.classList.remove('dark');
    }

    // 设置CSS自定义属性
    this.setCSSVariables();
  }

  /**
   * 设置CSS自定义属性
   */
  private setCSSVariables(): void {
    const root = document.documentElement;
    
    if (this.currentTheme.isDark) {
      // 暗色模式变量
      root.style.setProperty('--theme-bg-primary', '#1a1a1a');
      root.style.setProperty('--theme-bg-secondary', '#2a2a2a');
      root.style.setProperty('--theme-bg-tertiary', '#3a3a3a');
      root.style.setProperty('--theme-text-primary', '#e0e0e0');
      root.style.setProperty('--theme-text-secondary', '#a0a0a0');
      root.style.setProperty('--theme-text-muted', '#666666');
      root.style.setProperty('--theme-border-primary', '#404040');
      root.style.setProperty('--theme-border-secondary', '#505050');
      root.style.setProperty('--theme-accent', '#0A84FF');
      root.style.setProperty('--theme-accent-hover', '#0066CC');
    } else {
      // 亮色模式变量
      root.style.setProperty('--theme-bg-primary', '#ffffff');
      root.style.setProperty('--theme-bg-secondary', '#f8f9fa');
      root.style.setProperty('--theme-bg-tertiary', '#e9ecef');
      root.style.setProperty('--theme-text-primary', '#333333');
      root.style.setProperty('--theme-text-secondary', '#666666');
      root.style.setProperty('--theme-text-muted', '#999999');
      root.style.setProperty('--theme-border-primary', '#e0e0e0');
      root.style.setProperty('--theme-border-secondary', '#f0f0f0');
      root.style.setProperty('--theme-accent', '#007AFF');
      root.style.setProperty('--theme-accent-hover', '#0056CC');
    }
  }

  /**
   * 获取当前主题状态
   */
  public getTheme(): ThemeState {
    return { ...this.currentTheme };
  }

  /**
   * 设置主题模式
   */
  public setTheme(mode: ThemeMode): void {
    this.currentTheme.mode = mode;
    
    switch (mode) {
      case 'light':
        this.currentTheme.isDark = false;
        break;
      case 'dark':
        this.currentTheme.isDark = true;
        break;
      case 'auto':
        this.currentTheme.isDark = this.currentTheme.systemPrefersDark;
        break;
    }
    
    this.applyTheme();
    this.notifyListeners();
  }

  /**
   * 切换主题
   */
  public toggleTheme(): void {
    const newMode = this.currentTheme.isDark ? 'light' : 'dark';
    this.setTheme(newMode);
  }

  /**
   * 添加主题变化监听器
   */
  public addListener(listener: (theme: ThemeState) => void): void {
    this.listeners.push(listener);
  }

  /**
   * 移除主题变化监听器
   */
  public removeListener(listener: (theme: ThemeState) => void): void {
    const index = this.listeners.indexOf(listener);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  /**
   * 通知所有监听器
   */
  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener(this.getTheme());
      } catch (error) {
        console.error("主题监听器执行失败:", error);
      }
    });
  }

  /**
   * 获取主题相关的CSS类名
   */
  public getThemeClasses(): string {
    const classes = ['theme-transition'];
    
    if (this.currentTheme.isDark) {
      classes.push('dark');
    }
    
    return classes.join(' ');
  }

  /**
   * 检查是否为暗色模式
   */
  public isDarkMode(): boolean {
    return this.currentTheme.isDark;
  }

  /**
   * 获取主题适配的颜色
   */
  public getThemeColor(colorKey: string): string {
    const colors = this.currentTheme.isDark ? {
      primary: '#e0e0e0',
      secondary: '#a0a0a0',
      muted: '#666666',
      accent: '#0A84FF',
      background: '#1a1a1a',
      surface: '#2a2a2a',
      border: '#404040'
    } : {
      primary: '#333333',
      secondary: '#666666',
      muted: '#999999',
      accent: '#007AFF',
      background: '#ffffff',
      surface: '#f8f9fa',
      border: '#e0e0e0'
    };
    
    return colors[colorKey as keyof typeof colors] || colors.primary;
  }

  /**
   * 销毁主题管理器
   */
  public destroy(): void {
    this.mediaQuery.removeEventListener('change', this.handleSystemThemeChange);
    this.listeners = [];
  }
}

/**
 * 获取主题管理器实例的便捷函数
 */
export const getThemeManager = (): ThemeManager => {
  return ThemeManager.getInstance();
};

/**
 * React Hook for theme management
 */
export const useTheme = () => {
  const themeManager = getThemeManager();
  return {
    theme: themeManager.getTheme(),
    setTheme: themeManager.setTheme.bind(themeManager),
    toggleTheme: themeManager.toggleTheme.bind(themeManager),
    isDarkMode: themeManager.isDarkMode(),
    getThemeColor: themeManager.getThemeColor.bind(themeManager),
    getThemeClasses: themeManager.getThemeClasses.bind(themeManager)
  };
};
