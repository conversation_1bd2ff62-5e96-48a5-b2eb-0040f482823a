{"extends": ["eslint:recommended", "plugin:react/recommended", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended"], "plugins": ["@typescript-eslint", "react-hooks"], "parser": "@typescript-eslint/parser", "rules": {"react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "warn", "import/prefer-default-export": "off", "@typescript-eslint/ban-ts-comment": "off", "@typescript-eslint/no-non-null-assertion": "off", "@typescript-eslint/explicit-module-boundary-types": "off"}}