@tailwind base;
@tailwind components;
@tailwind utilities;

/* 苹果设计风格组件 */
.apple-modal {
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  transition: all 0.3s ease;
}

.dark .apple-modal {
  background-color: rgba(30, 30, 30, 0.7);
  border: 1px solid rgba(60, 60, 60, 0.2);
}

.apple-input {
  border-radius: 10px;
  transition: all 0.2s ease;
  background-color: rgba(240, 240, 240, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.dark .apple-input {
  background-color: rgba(50, 50, 50, 0.8);
  border: 1px solid rgba(80, 80, 80, 0.2);
  color: #e0e0e0;
}

.apple-input:focus {
  box-shadow: 0 0 0 4px rgba(0, 125, 250, 0.2);
  border-color: #0077ff;
}

.dark .apple-input:focus {
  box-shadow: 0 0 0 4px rgba(64, 156, 255, 0.2);
  border-color: #4096ff;
}

.apple-select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%23888' d='M4 8l4-4 1 1-5 5-2.5-2.5 1-1z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 10px center;
  padding-right: 30px;
  transition: all 0.2s ease;
}

.dark .apple-select {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%23aaa' d='M4 8l4-4 1 1-5 5-2.5-2.5 1-1z'/%3E%3C/svg%3E");
}

.apple-spinner {
  border-width: 2px;
  animation: spin 1s linear infinite;
}

/* 打字机效果与流式响应 */
.typewriter-text {
  word-break: break-word;
}

.blinking-cursor {
  display: inline-block;
  width: 2px;
  height: 18px;
  background-color: currentColor;
  margin-left: 1px;
  animation: blink 1s step-end infinite;
  vertical-align: text-bottom;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

.ai-response {
  line-height: 1.6;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

/* 苹果风格按钮 */
.apple-button {
  border-radius: 10px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.apple-button:hover {
  transform: translateY(-1px);
}

.apple-button:active {
  transform: translateY(1px);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 20px;
}

.dark ::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
}

/* 主题适配的基础样式 */
.theme-bg-primary {
  background-color: var(--ls-primary-background-color, #ffffff);
}

.dark .theme-bg-primary {
  background-color: var(--ls-primary-background-color, #1a1a1a);
}

.theme-bg-secondary {
  background-color: var(--ls-secondary-background-color, #f8f9fa);
}

.dark .theme-bg-secondary {
  background-color: var(--ls-secondary-background-color, #2a2a2a);
}

.theme-text-primary {
  color: var(--ls-primary-text-color, #333333);
}

.dark .theme-text-primary {
  color: var(--ls-primary-text-color, #e0e0e0);
}

.theme-text-secondary {
  color: var(--ls-secondary-text-color, #666666);
}

.dark .theme-text-secondary {
  color: var(--ls-secondary-text-color, #a0a0a0);
}

.theme-border {
  border-color: var(--ls-border-color, #e0e0e0);
}

.dark .theme-border {
  border-color: var(--ls-border-color, #404040);
}

/* 增强的苹果风格组件暗色模式 */
.apple-modal {
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  transition: all 0.3s ease;
  color: var(--ls-primary-text-color, #333333);
}

.dark .apple-modal {
  background-color: rgba(26, 26, 26, 0.95);
  border: 1px solid rgba(60, 60, 60, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  color: var(--ls-primary-text-color, #e0e0e0);
}

/* 增强的输入框样式 */
.apple-input {
  border-radius: 10px;
  transition: all 0.2s ease;
  background-color: rgba(248, 249, 250, 0.9);
  border: 1px solid rgba(0, 0, 0, 0.1);
  color: var(--ls-primary-text-color, #333333);
}

.dark .apple-input {
  background-color: rgba(42, 42, 42, 0.9);
  border: 1px solid rgba(80, 80, 80, 0.3);
  color: var(--ls-primary-text-color, #e0e0e0);
}

.apple-input::placeholder {
  color: var(--ls-secondary-text-color, #999999);
}

.dark .apple-input::placeholder {
  color: var(--ls-secondary-text-color, #666666);
}

/* 增强的按钮样式 */
.apple-button {
  border-radius: 10px;
  font-weight: 500;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.apple-button:hover {
  transform: translateY(-1px);
}

.apple-button:active {
  transform: translateY(1px);
}

.apple-button-primary {
  background-color: #007AFF;
  color: white;
}

.apple-button-primary:hover {
  background-color: #0056CC;
}

.dark .apple-button-primary {
  background-color: #0A84FF;
}

.dark .apple-button-primary:hover {
  background-color: #0066CC;
}

.apple-button-secondary {
  background-color: rgba(120, 120, 128, 0.16);
  color: var(--ls-primary-text-color, #333333);
}

.apple-button-secondary:hover {
  background-color: rgba(120, 120, 128, 0.24);
}

.dark .apple-button-secondary {
  background-color: rgba(142, 142, 147, 0.16);
  color: var(--ls-primary-text-color, #e0e0e0);
}

.dark .apple-button-secondary:hover {
  background-color: rgba(142, 142, 147, 0.24);
}

/* 增强的选择框样式 */
.apple-select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%23666' d='M6 8L2 4h8z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 10px center;
  padding-right: 30px;
  transition: all 0.2s ease;
  background-color: rgba(248, 249, 250, 0.9);
  border: 1px solid rgba(0, 0, 0, 0.1);
  color: var(--ls-primary-text-color, #333333);
}

.dark .apple-select {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%23aaa' d='M6 8L2 4h8z'/%3E%3C/svg%3E");
  background-color: rgba(42, 42, 42, 0.9);
  border: 1px solid rgba(80, 80, 80, 0.3);
  color: var(--ls-primary-text-color, #e0e0e0);
}

/* 主题过渡动画 */
.theme-transition {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* 代码块样式 */
.ai-response pre {
  background-color: var(--theme-bg-secondary, #f8f9fa);
  border: 1px solid var(--theme-border-primary, #e0e0e0);
  border-radius: 8px;
  padding: 12px;
  overflow-x: auto;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.4;
}

.dark .ai-response pre {
  background-color: var(--theme-bg-secondary, #2a2a2a);
  border-color: var(--theme-border-primary, #404040);
  color: var(--theme-text-primary, #e0e0e0);
}

.ai-response code {
  background-color: var(--theme-bg-secondary, #f8f9fa);
  border: 1px solid var(--theme-border-primary, #e0e0e0);
  border-radius: 4px;
  padding: 2px 6px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
}

.dark .ai-response code {
  background-color: var(--theme-bg-secondary, #2a2a2a);
  border-color: var(--theme-border-primary, #404040);
  color: var(--theme-text-primary, #e0e0e0);
}

/* 引用块样式 */
.ai-response blockquote {
  border-left: 4px solid var(--theme-accent, #007AFF);
  margin: 16px 0;
  padding: 8px 16px;
  background-color: var(--theme-bg-secondary, #f8f9fa);
  border-radius: 0 8px 8px 0;
}

.dark .ai-response blockquote {
  border-left-color: var(--theme-accent, #0A84FF);
  background-color: var(--theme-bg-secondary, #2a2a2a);
}

/* 表格样式 */
.ai-response table {
  border-collapse: collapse;
  width: 100%;
  margin: 16px 0;
}

.ai-response th,
.ai-response td {
  border: 1px solid var(--theme-border-primary, #e0e0e0);
  padding: 8px 12px;
  text-align: left;
}

.ai-response th {
  background-color: var(--theme-bg-secondary, #f8f9fa);
  font-weight: 600;
}

.dark .ai-response th,
.dark .ai-response td {
  border-color: var(--theme-border-primary, #404040);
}

.dark .ai-response th {
  background-color: var(--theme-bg-secondary, #2a2a2a);
}

/* 链接样式 */
.ai-response a {
  color: var(--theme-accent, #007AFF);
  text-decoration: none;
}

.ai-response a:hover {
  text-decoration: underline;
}

.dark .ai-response a {
  color: var(--theme-accent, #0A84FF);
}

/* 列表样式 */
.ai-response ul,
.ai-response ol {
  margin: 12px 0;
  padding-left: 24px;
}

.ai-response li {
  margin: 4px 0;
}

/* 分隔线样式 */
.ai-response hr {
  border: none;
  height: 1px;
  background-color: var(--theme-border-primary, #e0e0e0);
  margin: 24px 0;
}

.dark .ai-response hr {
  background-color: var(--theme-border-primary, #404040);
}

/* 强调文本样式 */
.ai-response strong {
  font-weight: 600;
  color: var(--theme-text-primary, #333333);
}

.dark .ai-response strong {
  color: var(--theme-text-primary, #e0e0e0);
}

.ai-response em {
  font-style: italic;
  color: var(--theme-text-secondary, #666666);
}

.dark .ai-response em {
  color: var(--theme-text-secondary, #a0a0a0);
}

/* 输入框和表单元素增强 */
input, textarea, select {
  transition: all 0.2s ease;
}

input:focus, textarea:focus, select:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--theme-accent, #007AFF);
}

.dark input:focus, .dark textarea:focus, .dark select:focus {
  box-shadow: 0 0 0 2px var(--theme-accent, #0A84FF);
}

/* 复选框样式 */
input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: var(--theme-accent, #007AFF);
}

.dark input[type="checkbox"] {
  accent-color: var(--theme-accent, #0A84FF);
}

/* 占位符文本 */
::placeholder {
  color: var(--theme-text-secondary, #666666);
  opacity: 0.7;
}

.dark ::placeholder {
  color: var(--theme-text-secondary, #a0a0a0);
  opacity: 0.7;
}

/* 选择文本样式 */
::selection {
  background-color: var(--theme-accent, #007AFF);
  color: white;
}

.dark ::selection {
  background-color: var(--theme-accent, #0A84FF);
  color: white;
}

/* 禁用状态样式 */
button:disabled, input:disabled, textarea:disabled, select:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 错误状态样式 */
.error-message {
  background-color: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #DC2626;
  border-radius: 8px;
  padding: 12px;
}

.dark .error-message {
  background-color: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
  color: #FCA5A5;
}

/* 成功状态样式 */
.success-message {
  background-color: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.3);
  color: #059669;
  border-radius: 8px;
  padding: 12px;
}

.dark .success-message {
  background-color: rgba(34, 197, 94, 0.1);
  border-color: rgba(34, 197, 94, 0.3);
  color: #6EE7B7;
}

/* 警告状态样式 */
.warning-message {
  background-color: rgba(245, 158, 11, 0.1);
  border: 1px solid rgba(245, 158, 11, 0.3);
  color: #D97706;
  border-radius: 8px;
  padding: 12px;
}

.dark .warning-message {
  background-color: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.3);
  color: #FCD34D;
}

/* 提示词建议下拉框样式 */
.prompt-suggestions {
  background-color: var(--theme-bg-primary, #ffffff);
  border: 1px solid var(--theme-border-primary, #e0e0e0);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  max-height: 200px;
  overflow-y: auto;
}

.dark .prompt-suggestions {
  background-color: var(--theme-bg-primary, #1a1a1a);
  border-color: var(--theme-border-primary, #404040);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.prompt-suggestion-item {
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  color: var(--theme-text-primary, #333333);
}

.prompt-suggestion-item:hover,
.prompt-suggestion-item.selected {
  background-color: var(--theme-bg-secondary, #f8f9fa);
}

.dark .prompt-suggestion-item {
  color: var(--theme-text-primary, #e0e0e0);
}

.dark .prompt-suggestion-item:hover,
.dark .prompt-suggestion-item.selected {
  background-color: var(--theme-bg-secondary, #2a2a2a);
}

