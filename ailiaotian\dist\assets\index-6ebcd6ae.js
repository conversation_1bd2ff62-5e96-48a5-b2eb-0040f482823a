(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const i of o)if(i.type==="childList")for(const a of i.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&r(a)}).observe(document,{childList:!0,subtree:!0});function n(o){const i={};return o.integrity&&(i.integrity=o.integrity),o.referrerPolicy&&(i.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?i.credentials="include":o.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(o){if(o.ep)return;o.ep=!0;const i=n(o);fetch(o.href,i)}})();var Ri=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function zh(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Pf={exports:{}},Bl={},Of={exports:{}},de={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ci=Symbol.for("react.element"),Dh=Symbol.for("react.portal"),Fh=Symbol.for("react.fragment"),Rh=Symbol.for("react.strict_mode"),Uh=Symbol.for("react.profiler"),$h=Symbol.for("react.provider"),Bh=Symbol.for("react.context"),Hh=Symbol.for("react.forward_ref"),Wh=Symbol.for("react.suspense"),Vh=Symbol.for("react.memo"),Qh=Symbol.for("react.lazy"),yc=Symbol.iterator;function qh(e){return e===null||typeof e!="object"?null:(e=yc&&e[yc]||e["@@iterator"],typeof e=="function"?e:null)}var If={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Lf=Object.assign,bf={};function ao(e,t,n){this.props=e,this.context=t,this.refs=bf,this.updater=n||If}ao.prototype.isReactComponent={};ao.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};ao.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function jf(){}jf.prototype=ao.prototype;function Ya(e,t,n){this.props=e,this.context=t,this.refs=bf,this.updater=n||If}var Xa=Ya.prototype=new jf;Xa.constructor=Ya;Lf(Xa,ao.prototype);Xa.isPureReactComponent=!0;var vc=Array.isArray,Mf=Object.prototype.hasOwnProperty,Za={current:null},Af={key:!0,ref:!0,__self:!0,__source:!0};function zf(e,t,n){var r,o={},i=null,a=null;if(t!=null)for(r in t.ref!==void 0&&(a=t.ref),t.key!==void 0&&(i=""+t.key),t)Mf.call(t,r)&&!Af.hasOwnProperty(r)&&(o[r]=t[r]);var c=arguments.length-2;if(c===1)o.children=n;else if(1<c){for(var f=Array(c),p=0;p<c;p++)f[p]=arguments[p+2];o.children=f}if(e&&e.defaultProps)for(r in c=e.defaultProps,c)o[r]===void 0&&(o[r]=c[r]);return{$$typeof:ci,type:e,key:i,ref:a,props:o,_owner:Za.current}}function Kh(e,t){return{$$typeof:ci,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Ja(e){return typeof e=="object"&&e!==null&&e.$$typeof===ci}function Gh(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var wc=/\/+/g;function Os(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Gh(""+e.key):t.toString(36)}function ol(e,t,n,r,o){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var a=!1;if(e===null)a=!0;else switch(i){case"string":case"number":a=!0;break;case"object":switch(e.$$typeof){case ci:case Dh:a=!0}}if(a)return a=e,o=o(a),e=r===""?"."+Os(a,0):r,vc(o)?(n="",e!=null&&(n=e.replace(wc,"$&/")+"/"),ol(o,t,n,"",function(p){return p})):o!=null&&(Ja(o)&&(o=Kh(o,n+(!o.key||a&&a.key===o.key?"":(""+o.key).replace(wc,"$&/")+"/")+e)),t.push(o)),1;if(a=0,r=r===""?".":r+":",vc(e))for(var c=0;c<e.length;c++){i=e[c];var f=r+Os(i,c);a+=ol(i,t,n,f,o)}else if(f=qh(e),typeof f=="function")for(e=f.call(e),c=0;!(i=e.next()).done;)i=i.value,f=r+Os(i,c++),a+=ol(i,t,n,f,o);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return a}function Ui(e,t,n){if(e==null)return e;var r=[],o=0;return ol(e,r,"","",function(i){return t.call(n,i,o++)}),r}function Yh(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var ut={current:null},il={transition:null},Xh={ReactCurrentDispatcher:ut,ReactCurrentBatchConfig:il,ReactCurrentOwner:Za};function Df(){throw Error("act(...) is not supported in production builds of React.")}de.Children={map:Ui,forEach:function(e,t,n){Ui(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Ui(e,function(){t++}),t},toArray:function(e){return Ui(e,function(t){return t})||[]},only:function(e){if(!Ja(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};de.Component=ao;de.Fragment=Fh;de.Profiler=Uh;de.PureComponent=Ya;de.StrictMode=Rh;de.Suspense=Wh;de.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Xh;de.act=Df;de.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Lf({},e.props),o=e.key,i=e.ref,a=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,a=Za.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var c=e.type.defaultProps;for(f in t)Mf.call(t,f)&&!Af.hasOwnProperty(f)&&(r[f]=t[f]===void 0&&c!==void 0?c[f]:t[f])}var f=arguments.length-2;if(f===1)r.children=n;else if(1<f){c=Array(f);for(var p=0;p<f;p++)c[p]=arguments[p+2];r.children=c}return{$$typeof:ci,type:e.type,key:o,ref:i,props:r,_owner:a}};de.createContext=function(e){return e={$$typeof:Bh,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:$h,_context:e},e.Consumer=e};de.createElement=zf;de.createFactory=function(e){var t=zf.bind(null,e);return t.type=e,t};de.createRef=function(){return{current:null}};de.forwardRef=function(e){return{$$typeof:Hh,render:e}};de.isValidElement=Ja;de.lazy=function(e){return{$$typeof:Qh,_payload:{_status:-1,_result:e},_init:Yh}};de.memo=function(e,t){return{$$typeof:Vh,type:e,compare:t===void 0?null:t}};de.startTransition=function(e){var t=il.transition;il.transition={};try{e()}finally{il.transition=t}};de.unstable_act=Df;de.useCallback=function(e,t){return ut.current.useCallback(e,t)};de.useContext=function(e){return ut.current.useContext(e)};de.useDebugValue=function(){};de.useDeferredValue=function(e){return ut.current.useDeferredValue(e)};de.useEffect=function(e,t){return ut.current.useEffect(e,t)};de.useId=function(){return ut.current.useId()};de.useImperativeHandle=function(e,t,n){return ut.current.useImperativeHandle(e,t,n)};de.useInsertionEffect=function(e,t){return ut.current.useInsertionEffect(e,t)};de.useLayoutEffect=function(e,t){return ut.current.useLayoutEffect(e,t)};de.useMemo=function(e,t){return ut.current.useMemo(e,t)};de.useReducer=function(e,t,n){return ut.current.useReducer(e,t,n)};de.useRef=function(e){return ut.current.useRef(e)};de.useState=function(e){return ut.current.useState(e)};de.useSyncExternalStore=function(e,t,n){return ut.current.useSyncExternalStore(e,t,n)};de.useTransition=function(){return ut.current.useTransition()};de.version="18.3.1";Of.exports=de;var ae=Of.exports;const Ff=zh(ae);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Zh=ae,Jh=Symbol.for("react.element"),em=Symbol.for("react.fragment"),tm=Object.prototype.hasOwnProperty,nm=Zh.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,rm={key:!0,ref:!0,__self:!0,__source:!0};function Rf(e,t,n){var r,o={},i=null,a=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(a=t.ref);for(r in t)tm.call(t,r)&&!rm.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:Jh,type:e,key:i,ref:a,props:o,_owner:nm.current}}Bl.Fragment=em;Bl.jsx=Rf;Bl.jsxs=Rf;Pf.exports=Bl;var eu=Pf.exports;const om=eu.Fragment,W=eu.jsx,he=eu.jsxs;var gl={exports:{}};/*! For license information please see lsplugin.user.js.LICENSE.txt */gl.exports;(function(e,t){(function(n,r){e.exports=r()})(self,()=>(()=>{var n={227:(a,c,f)=>{var p=f(155);c.formatArgs=function(_){if(_[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+_[0]+(this.useColors?"%c ":" ")+"+"+a.exports.humanize(this.diff),!this.useColors)return;const v="color: "+this.color;_.splice(1,0,v,"color: inherit");let E=0,k=0;_[0].replace(/%[a-zA-Z%]/g,N=>{N!=="%%"&&(E++,N==="%c"&&(k=E))}),_.splice(k,0,v)},c.save=function(_){try{_?c.storage.setItem("debug",_):c.storage.removeItem("debug")}catch{}},c.load=function(){let _;try{_=c.storage.getItem("debug")}catch{}return!_&&p!==void 0&&"env"in p&&(_=p.env.DEBUG),_},c.useColors=function(){return typeof window<"u"&&window.process&&(window.process.type==="renderer"||window.process.__nwjs)?!0:typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/)?!1:typeof document<"u"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window<"u"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},c.storage=function(){try{return localStorage}catch{}}(),c.destroy=(()=>{let _=!1;return()=>{_||(_=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),c.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],c.log=console.debug||console.log||(()=>{}),a.exports=f(447)(c);const{formatters:w}=a.exports;w.j=function(_){try{return JSON.stringify(_)}catch(v){return"[UnexpectedJSONParseError]: "+v.message}}},447:(a,c,f)=>{a.exports=function(p){function w(E){let k,N,L,h=null;function d(...g){if(!d.enabled)return;const C=d,I=Number(new Date),j=I-(k||I);C.diff=j,C.prev=k,C.curr=I,k=I,g[0]=w.coerce(g[0]),typeof g[0]!="string"&&g.unshift("%O");let y=0;g[0]=g[0].replace(/%([a-zA-Z%])/g,(x,M)=>{if(x==="%%")return"%";y++;const T=w.formatters[M];if(typeof T=="function"){const R=g[y];x=T.call(C,R),g.splice(y,1),y--}return x}),w.formatArgs.call(C,g),(C.log||w.log).apply(C,g)}return d.namespace=E,d.useColors=w.useColors(),d.color=w.selectColor(E),d.extend=_,d.destroy=w.destroy,Object.defineProperty(d,"enabled",{enumerable:!0,configurable:!1,get:()=>h!==null?h:(N!==w.namespaces&&(N=w.namespaces,L=w.enabled(E)),L),set:g=>{h=g}}),typeof w.init=="function"&&w.init(d),d}function _(E,k){const N=w(this.namespace+(k===void 0?":":k)+E);return N.log=this.log,N}function v(E){return E.toString().substring(2,E.toString().length-2).replace(/\.\*\?$/,"*")}return w.debug=w,w.default=w,w.coerce=function(E){return E instanceof Error?E.stack||E.message:E},w.disable=function(){const E=[...w.names.map(v),...w.skips.map(v).map(k=>"-"+k)].join(",");return w.enable(""),E},w.enable=function(E){let k;w.save(E),w.namespaces=E,w.names=[],w.skips=[];const N=(typeof E=="string"?E:"").split(/[\s,]+/),L=N.length;for(k=0;k<L;k++)N[k]&&((E=N[k].replace(/\*/g,".*?"))[0]==="-"?w.skips.push(new RegExp("^"+E.slice(1)+"$")):w.names.push(new RegExp("^"+E+"$")))},w.enabled=function(E){if(E[E.length-1]==="*")return!0;let k,N;for(k=0,N=w.skips.length;k<N;k++)if(w.skips[k].test(E))return!1;for(k=0,N=w.names.length;k<N;k++)if(w.names[k].test(E))return!0;return!1},w.humanize=f(824),w.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(p).forEach(E=>{w[E]=p[E]}),w.names=[],w.skips=[],w.formatters={},w.selectColor=function(E){let k=0;for(let N=0;N<E.length;N++)k=(k<<5)-k+E.charCodeAt(N),k|=0;return w.colors[Math.abs(k)%w.colors.length]},w.enable(w.load()),w}},996:a=>{var c=function(L){return function(h){return!!h&&typeof h=="object"}(L)&&!function(h){var d=Object.prototype.toString.call(h);return d==="[object RegExp]"||d==="[object Date]"||function(g){return g.$$typeof===f}(h)}(L)},f=typeof Symbol=="function"&&Symbol.for?Symbol.for("react.element"):60103;function p(L,h){return h.clone!==!1&&h.isMergeableObject(L)?k((d=L,Array.isArray(d)?[]:{}),L,h):L;var d}function w(L,h,d){return L.concat(h).map(function(g){return p(g,d)})}function _(L){return Object.keys(L).concat(function(h){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(h).filter(function(d){return Object.propertyIsEnumerable.call(h,d)}):[]}(L))}function v(L,h){try{return h in L}catch{return!1}}function E(L,h,d){var g={};return d.isMergeableObject(L)&&_(L).forEach(function(C){g[C]=p(L[C],d)}),_(h).forEach(function(C){(function(I,j){return v(I,j)&&!(Object.hasOwnProperty.call(I,j)&&Object.propertyIsEnumerable.call(I,j))})(L,C)||(v(L,C)&&d.isMergeableObject(h[C])?g[C]=function(I,j){if(!j.customMerge)return k;var y=j.customMerge(I);return typeof y=="function"?y:k}(C,d)(L[C],h[C],d):g[C]=p(h[C],d))}),g}function k(L,h,d){(d=d||{}).arrayMerge=d.arrayMerge||w,d.isMergeableObject=d.isMergeableObject||c,d.cloneUnlessOtherwiseSpecified=p;var g=Array.isArray(h);return g===Array.isArray(L)?g?d.arrayMerge(L,h,d):E(L,h,d):p(h,d)}k.all=function(L,h){if(!Array.isArray(L))throw new Error("first argument should be an array");return L.reduce(function(d,g){return k(d,g,h)},{})};var N=k;a.exports=N},856:function(a){a.exports=function(){function c($){return c=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(K){return typeof K}:function(K){return K&&typeof Symbol=="function"&&K.constructor===Symbol&&K!==Symbol.prototype?"symbol":typeof K},c($)}function f($,K){return f=Object.setPrototypeOf||function(re,fe){return re.__proto__=fe,re},f($,K)}function p(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function w($,K,re){return w=p()?Reflect.construct:function(fe,De,At){var Yt=[null];Yt.push.apply(Yt,De);var Sn=new(Function.bind.apply(fe,Yt));return At&&f(Sn,At.prototype),Sn},w.apply(null,arguments)}function _($){return v($)||E($)||k($)||L()}function v($){if(Array.isArray($))return N($)}function E($){if(typeof Symbol<"u"&&$[Symbol.iterator]!=null||$["@@iterator"]!=null)return Array.from($)}function k($,K){if($){if(typeof $=="string")return N($,K);var re=Object.prototype.toString.call($).slice(8,-1);return re==="Object"&&$.constructor&&(re=$.constructor.name),re==="Map"||re==="Set"?Array.from($):re==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(re)?N($,K):void 0}}function N($,K){(K==null||K>$.length)&&(K=$.length);for(var re=0,fe=new Array(K);re<K;re++)fe[re]=$[re];return fe}function L(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var h=Object.hasOwnProperty,d=Object.setPrototypeOf,g=Object.isFrozen,C=Object.getPrototypeOf,I=Object.getOwnPropertyDescriptor,j=Object.freeze,y=Object.seal,x=Object.create,M=typeof Reflect<"u"&&Reflect,T=M.apply,R=M.construct;T||(T=function($,K,re){return $.apply(K,re)}),j||(j=function($){return $}),y||(y=function($){return $}),R||(R=function($,K){return w($,_(K))});var ee=ne(Array.prototype.forEach),le=ne(Array.prototype.pop),J=ne(Array.prototype.push),_e=ne(String.prototype.toLowerCase),Ae=ne(String.prototype.match),Pe=ne(String.prototype.replace),q=ne(String.prototype.indexOf),O=ne(String.prototype.trim),z=ne(RegExp.prototype.test),B=Ce(TypeError);function ne($){return function(K){for(var re=arguments.length,fe=new Array(re>1?re-1:0),De=1;De<re;De++)fe[De-1]=arguments[De];return T($,K,fe)}}function Ce($){return function(){for(var K=arguments.length,re=new Array(K),fe=0;fe<K;fe++)re[fe]=arguments[fe];return R($,re)}}function V($,K){d&&d($,null);for(var re=K.length;re--;){var fe=K[re];if(typeof fe=="string"){var De=_e(fe);De!==fe&&(g(K)||(K[re]=De),fe=De)}$[fe]=!0}return $}function se($){var K,re=x(null);for(K in $)T(h,$,[K])&&(re[K]=$[K]);return re}function ce($,K){for(;$!==null;){var re=I($,K);if(re){if(re.get)return ne(re.get);if(typeof re.value=="function")return ne(re.value)}$=C($)}function fe(De){return console.warn("fallback value for",De),null}return fe}var ze=j(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),Qe=j(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),G=j(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),oe=j(["animate","color-profile","cursor","discard","fedropshadow","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),ye=j(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover"]),pe=j(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),Ue=j(["#text"]),Ge=j(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),jt=j(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),ln=j(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),vt=j(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),ft=y(/\{\{[\w\W]*|[\w\W]*\}\}/gm),Zn=y(/<%[\w\W]*|[\w\W]*%>/gm),os=y(/^data-[\-\w.\u00B7-\uFFFF]/),Mt=y(/^aria-[\-\w]+$/),Gt=y(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),mi=y(/^(?:\w+script|data):/i),is=y(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),ls=y(/^html$/i),Jn=function(){return typeof window>"u"?null:window},ss=function($,K){if(c($)!=="object"||typeof $.createPolicy!="function")return null;var re=null,fe="data-tt-policy-suffix";K.currentScript&&K.currentScript.hasAttribute(fe)&&(re=K.currentScript.getAttribute(fe));var De="dompurify"+(re?"#"+re:"");try{return $.createPolicy(De,{createHTML:function(At){return At}})}catch{return console.warn("TrustedTypes policy "+De+" could not be created."),null}};function gi(){var $=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Jn(),K=function(S){return gi(S)};if(K.version="2.3.8",K.removed=[],!$||!$.document||$.document.nodeType!==9)return K.isSupported=!1,K;var re=$.document,fe=$.document,De=$.DocumentFragment,At=$.HTMLTemplateElement,Yt=$.Node,Sn=$.Element,po=$.NodeFilter,yi=$.NamedNodeMap,sn=yi===void 0?$.NamedNodeMap||$.MozNamedAttrMap:yi,as=$.HTMLFormElement,us=$.DOMParser,cs=$.trustedTypes,xr=Sn.prototype,fs=ce(xr,"cloneNode"),ds=ce(xr,"nextSibling"),ps=ce(xr,"childNodes"),ho=ce(xr,"parentNode");if(typeof At=="function"){var zt=fe.createElement("template");zt.content&&zt.content.ownerDocument&&(fe=zt.content.ownerDocument)}var Dt=ss(cs,re),vi=Dt?Dt.createHTML(""):"",Er=fe,mo=Er.implementation,_n=Er.createNodeIterator,wi=Er.createDocumentFragment,ki=Er.getElementsByTagName,hs=re.importNode,Si={};try{Si=se(fe).documentMode?fe.documentMode:{}}catch{}var wt={};K.isSupported=typeof ho=="function"&&mo&&mo.createHTMLDocument!==void 0&&Si!==9;var er,Xt,Tr=ft,Nr=Zn,go=os,ms=Mt,_i=mi,Pr=is,ve=Gt,$e=null,Ci=V({},[].concat(_(ze),_(Qe),_(G),_(ye),_(Ue))),Be=null,Cn=V({},[].concat(_(Ge),_(jt),_(ln),_(vt))),Oe=Object.seal(Object.create(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),xn=null,Or=null,yo=!0,vo=!0,xi=!1,En=!1,an=!1,wo=!1,ko=!1,Tn=!1,Ir=!1,Nn=!1,Ei=!0,So=!0,Pn=!1,Ft={},On=null,Ti=V({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]),Ni=null,Pi=V({},["audio","video","img","source","image","track"]),_o=null,un=V({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),In="http://www.w3.org/1998/Math/MathML",Co="http://www.w3.org/2000/svg",Zt="http://www.w3.org/1999/xhtml",Lr=Zt,Oi=!1,tr=["application/xhtml+xml","text/html"],nr="text/html",Ln=null,gs=fe.createElement("form"),Ii=function(S){return S instanceof RegExp||S instanceof Function},xo=function(S){Ln&&Ln===S||(S&&c(S)==="object"||(S={}),S=se(S),$e="ALLOWED_TAGS"in S?V({},S.ALLOWED_TAGS):Ci,Be="ALLOWED_ATTR"in S?V({},S.ALLOWED_ATTR):Cn,_o="ADD_URI_SAFE_ATTR"in S?V(se(un),S.ADD_URI_SAFE_ATTR):un,Ni="ADD_DATA_URI_TAGS"in S?V(se(Pi),S.ADD_DATA_URI_TAGS):Pi,On="FORBID_CONTENTS"in S?V({},S.FORBID_CONTENTS):Ti,xn="FORBID_TAGS"in S?V({},S.FORBID_TAGS):{},Or="FORBID_ATTR"in S?V({},S.FORBID_ATTR):{},Ft="USE_PROFILES"in S&&S.USE_PROFILES,yo=S.ALLOW_ARIA_ATTR!==!1,vo=S.ALLOW_DATA_ATTR!==!1,xi=S.ALLOW_UNKNOWN_PROTOCOLS||!1,En=S.SAFE_FOR_TEMPLATES||!1,an=S.WHOLE_DOCUMENT||!1,Tn=S.RETURN_DOM||!1,Ir=S.RETURN_DOM_FRAGMENT||!1,Nn=S.RETURN_TRUSTED_TYPE||!1,ko=S.FORCE_BODY||!1,Ei=S.SANITIZE_DOM!==!1,So=S.KEEP_CONTENT!==!1,Pn=S.IN_PLACE||!1,ve=S.ALLOWED_URI_REGEXP||ve,Lr=S.NAMESPACE||Zt,S.CUSTOM_ELEMENT_HANDLING&&Ii(S.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(Oe.tagNameCheck=S.CUSTOM_ELEMENT_HANDLING.tagNameCheck),S.CUSTOM_ELEMENT_HANDLING&&Ii(S.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(Oe.attributeNameCheck=S.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),S.CUSTOM_ELEMENT_HANDLING&&typeof S.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements=="boolean"&&(Oe.allowCustomizedBuiltInElements=S.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),er=er=tr.indexOf(S.PARSER_MEDIA_TYPE)===-1?nr:S.PARSER_MEDIA_TYPE,Xt=er==="application/xhtml+xml"?function(Q){return Q}:_e,En&&(vo=!1),Ir&&(Tn=!0),Ft&&($e=V({},_(Ue)),Be=[],Ft.html===!0&&(V($e,ze),V(Be,Ge)),Ft.svg===!0&&(V($e,Qe),V(Be,jt),V(Be,vt)),Ft.svgFilters===!0&&(V($e,G),V(Be,jt),V(Be,vt)),Ft.mathMl===!0&&(V($e,ye),V(Be,ln),V(Be,vt))),S.ADD_TAGS&&($e===Ci&&($e=se($e)),V($e,S.ADD_TAGS)),S.ADD_ATTR&&(Be===Cn&&(Be=se(Be)),V(Be,S.ADD_ATTR)),S.ADD_URI_SAFE_ATTR&&V(_o,S.ADD_URI_SAFE_ATTR),S.FORBID_CONTENTS&&(On===Ti&&(On=se(On)),V(On,S.FORBID_CONTENTS)),So&&($e["#text"]=!0),an&&V($e,["html","head","body"]),$e.table&&(V($e,["tbody"]),delete xn.tbody),j&&j(S),Ln=S)},Li=V({},["mi","mo","mn","ms","mtext"]),bi=V({},["foreignobject","desc","title","annotation-xml"]),ys=V({},["title","style","font","a","script"]),Rt=V({},Qe);V(Rt,G),V(Rt,oe);var br=V({},ye);V(br,pe);var vs=function(S){var Q=ho(S);Q&&Q.tagName||(Q={namespaceURI:Zt,tagName:"template"});var U=_e(S.tagName),we=_e(Q.tagName);return S.namespaceURI===Co?Q.namespaceURI===Zt?U==="svg":Q.namespaceURI===In?U==="svg"&&(we==="annotation-xml"||Li[we]):!!Rt[U]:S.namespaceURI===In?Q.namespaceURI===Zt?U==="math":Q.namespaceURI===Co?U==="math"&&bi[we]:!!br[U]:S.namespaceURI===Zt&&!(Q.namespaceURI===Co&&!bi[we])&&!(Q.namespaceURI===In&&!Li[we])&&!br[U]&&(ys[U]||!Rt[U])},Ut=function(S){J(K.removed,{element:S});try{S.parentNode.removeChild(S)}catch{try{S.outerHTML=vi}catch{S.remove()}}},rr=function(S,Q){try{J(K.removed,{attribute:Q.getAttributeNode(S),from:Q})}catch{J(K.removed,{attribute:null,from:Q})}if(Q.removeAttribute(S),S==="is"&&!Be[S])if(Tn||Ir)try{Ut(Q)}catch{}else try{Q.setAttribute(S,"")}catch{}},ji=function(S){var Q,U;if(ko)S="<remove></remove>"+S;else{var we=Ae(S,/^[\r\n\t ]+/);U=we&&we[0]}er==="application/xhtml+xml"&&(S='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+S+"</body></html>");var Le=Dt?Dt.createHTML(S):S;if(Lr===Zt)try{Q=new us().parseFromString(Le,er)}catch{}if(!Q||!Q.documentElement){Q=mo.createDocument(Lr,"template",null);try{Q.documentElement.innerHTML=Oi?"":Le}catch{}}var Ye=Q.body||Q.documentElement;return S&&U&&Ye.insertBefore(fe.createTextNode(U),Ye.childNodes[0]||null),Lr===Zt?ki.call(Q,an?"html":"body")[0]:an?Q.documentElement:Ye},Mi=function(S){return _n.call(S.ownerDocument||S,S,po.SHOW_ELEMENT|po.SHOW_COMMENT|po.SHOW_TEXT,null,!1)},ws=function(S){return S instanceof as&&(typeof S.nodeName!="string"||typeof S.textContent!="string"||typeof S.removeChild!="function"||!(S.attributes instanceof sn)||typeof S.removeAttribute!="function"||typeof S.setAttribute!="function"||typeof S.namespaceURI!="string"||typeof S.insertBefore!="function")},or=function(S){return c(Yt)==="object"?S instanceof Yt:S&&c(S)==="object"&&typeof S.nodeType=="number"&&typeof S.nodeName=="string"},nt=function(S,Q,U){wt[S]&&ee(wt[S],function(we){we.call(K,Q,U,Ln)})},ir=function(S){var Q;if(nt("beforeSanitizeElements",S,null),ws(S)||z(/[\u0080-\uFFFF]/,S.nodeName))return Ut(S),!0;var U=Xt(S.nodeName);if(nt("uponSanitizeElement",S,{tagName:U,allowedTags:$e}),S.hasChildNodes()&&!or(S.firstElementChild)&&(!or(S.content)||!or(S.content.firstElementChild))&&z(/<[/\w]/g,S.innerHTML)&&z(/<[/\w]/g,S.textContent)||U==="select"&&z(/<template/i,S.innerHTML))return Ut(S),!0;if(!$e[U]||xn[U]){if(!xn[U]&&cn(U)&&(Oe.tagNameCheck instanceof RegExp&&z(Oe.tagNameCheck,U)||Oe.tagNameCheck instanceof Function&&Oe.tagNameCheck(U)))return!1;if(So&&!On[U]){var we=ho(S)||S.parentNode,Le=ps(S)||S.childNodes;if(Le&&we)for(var Ye=Le.length-1;Ye>=0;--Ye)we.insertBefore(fs(Le[Ye],!0),ds(S))}return Ut(S),!0}return S instanceof Sn&&!vs(S)?(Ut(S),!0):U!=="noscript"&&U!=="noembed"||!z(/<\/no(script|embed)/i,S.innerHTML)?(En&&S.nodeType===3&&(Q=S.textContent,Q=Pe(Q,Tr," "),Q=Pe(Q,Nr," "),S.textContent!==Q&&(J(K.removed,{element:S.cloneNode()}),S.textContent=Q)),nt("afterSanitizeElements",S,null),!1):(Ut(S),!0)},Ai=function(S,Q,U){if(Ei&&(Q==="id"||Q==="name")&&(U in fe||U in gs))return!1;if(!(vo&&!Or[Q]&&z(go,Q))){if(!(yo&&z(ms,Q))){if(!Be[Q]||Or[Q]){if(!(cn(S)&&(Oe.tagNameCheck instanceof RegExp&&z(Oe.tagNameCheck,S)||Oe.tagNameCheck instanceof Function&&Oe.tagNameCheck(S))&&(Oe.attributeNameCheck instanceof RegExp&&z(Oe.attributeNameCheck,Q)||Oe.attributeNameCheck instanceof Function&&Oe.attributeNameCheck(Q))||Q==="is"&&Oe.allowCustomizedBuiltInElements&&(Oe.tagNameCheck instanceof RegExp&&z(Oe.tagNameCheck,U)||Oe.tagNameCheck instanceof Function&&Oe.tagNameCheck(U))))return!1}else if(!_o[Q]){if(!z(ve,Pe(U,Pr,""))){if((Q!=="src"&&Q!=="xlink:href"&&Q!=="href"||S==="script"||q(U,"data:")!==0||!Ni[S])&&!(xi&&!z(_i,Pe(U,Pr,"")))){if(U)return!1}}}}}return!0},cn=function(S){return S.indexOf("-")>0},lr=function(S){var Q,U,we,Le;nt("beforeSanitizeAttributes",S,null);var Ye=S.attributes;if(Ye){var He={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:Be};for(Le=Ye.length;Le--;){var sr=Q=Ye[Le],Jt=sr.name,dt=sr.namespaceURI;if(U=Jt==="value"?Q.value:O(Q.value),we=Xt(Jt),He.attrName=we,He.attrValue=U,He.keepAttr=!0,He.forceKeepAttr=void 0,nt("uponSanitizeAttribute",S,He),U=He.attrValue,!He.forceKeepAttr&&(rr(Jt,S),He.keepAttr))if(z(/\/>/i,U))rr(Jt,S);else{En&&(U=Pe(U,Tr," "),U=Pe(U,Nr," "));var Eo=Xt(S.nodeName);if(Ai(Eo,we,U))try{dt?S.setAttributeNS(dt,Jt,U):S.setAttribute(Jt,U),le(K.removed)}catch{}}}nt("afterSanitizeAttributes",S,null)}},ks=function S(Q){var U,we=Mi(Q);for(nt("beforeSanitizeShadowDOM",Q,null);U=we.nextNode();)nt("uponSanitizeShadowNode",U,null),ir(U)||(U.content instanceof De&&S(U.content),lr(U));nt("afterSanitizeShadowDOM",Q,null)};return K.sanitize=function(S,Q){var U,we,Le,Ye,He;if((Oi=!S)&&(S="<!-->"),typeof S!="string"&&!or(S)){if(typeof S.toString!="function")throw B("toString is not a function");if(typeof(S=S.toString())!="string")throw B("dirty is not a string, aborting")}if(!K.isSupported){if(c($.toStaticHTML)==="object"||typeof $.toStaticHTML=="function"){if(typeof S=="string")return $.toStaticHTML(S);if(or(S))return $.toStaticHTML(S.outerHTML)}return S}if(wo||xo(Q),K.removed=[],typeof S=="string"&&(Pn=!1),Pn){if(S.nodeName){var sr=Xt(S.nodeName);if(!$e[sr]||xn[sr])throw B("root node is forbidden and cannot be sanitized in-place")}}else if(S instanceof Yt)(we=(U=ji("<!---->")).ownerDocument.importNode(S,!0)).nodeType===1&&we.nodeName==="BODY"||we.nodeName==="HTML"?U=we:U.appendChild(we);else{if(!Tn&&!En&&!an&&S.indexOf("<")===-1)return Dt&&Nn?Dt.createHTML(S):S;if(!(U=ji(S)))return Tn?null:Nn?vi:""}U&&ko&&Ut(U.firstChild);for(var Jt=Mi(Pn?S:U);Le=Jt.nextNode();)Le.nodeType===3&&Le===Ye||ir(Le)||(Le.content instanceof De&&ks(Le.content),lr(Le),Ye=Le);if(Ye=null,Pn)return S;if(Tn){if(Ir)for(He=wi.call(U.ownerDocument);U.firstChild;)He.appendChild(U.firstChild);else He=U;return Be.shadowroot&&(He=hs.call(re,He,!0)),He}var dt=an?U.outerHTML:U.innerHTML;return an&&$e["!doctype"]&&U.ownerDocument&&U.ownerDocument.doctype&&U.ownerDocument.doctype.name&&z(ls,U.ownerDocument.doctype.name)&&(dt="<!DOCTYPE "+U.ownerDocument.doctype.name+`>
`+dt),En&&(dt=Pe(dt,Tr," "),dt=Pe(dt,Nr," ")),Dt&&Nn?Dt.createHTML(dt):dt},K.setConfig=function(S){xo(S),wo=!0},K.clearConfig=function(){Ln=null,wo=!1},K.isValidAttribute=function(S,Q,U){Ln||xo({});var we=Xt(S),Le=Xt(Q);return Ai(we,Le,U)},K.addHook=function(S,Q){typeof Q=="function"&&(wt[S]=wt[S]||[],J(wt[S],Q))},K.removeHook=function(S){if(wt[S])return le(wt[S])},K.removeHooks=function(S){wt[S]&&(wt[S]=[])},K.removeAllHooks=function(){wt={}},K}return gi()}()},729:a=>{var c=Object.prototype.hasOwnProperty,f="~";function p(){}function w(k,N,L){this.fn=k,this.context=N,this.once=L||!1}function _(k,N,L,h,d){if(typeof L!="function")throw new TypeError("The listener must be a function");var g=new w(L,h||k,d),C=f?f+N:N;return k._events[C]?k._events[C].fn?k._events[C]=[k._events[C],g]:k._events[C].push(g):(k._events[C]=g,k._eventsCount++),k}function v(k,N){--k._eventsCount==0?k._events=new p:delete k._events[N]}function E(){this._events=new p,this._eventsCount=0}Object.create&&(p.prototype=Object.create(null),new p().__proto__||(f=!1)),E.prototype.eventNames=function(){var k,N,L=[];if(this._eventsCount===0)return L;for(N in k=this._events)c.call(k,N)&&L.push(f?N.slice(1):N);return Object.getOwnPropertySymbols?L.concat(Object.getOwnPropertySymbols(k)):L},E.prototype.listeners=function(k){var N=f?f+k:k,L=this._events[N];if(!L)return[];if(L.fn)return[L.fn];for(var h=0,d=L.length,g=new Array(d);h<d;h++)g[h]=L[h].fn;return g},E.prototype.listenerCount=function(k){var N=f?f+k:k,L=this._events[N];return L?L.fn?1:L.length:0},E.prototype.emit=function(k,N,L,h,d,g){var C=f?f+k:k;if(!this._events[C])return!1;var I,j,y=this._events[C],x=arguments.length;if(y.fn){switch(y.once&&this.removeListener(k,y.fn,void 0,!0),x){case 1:return y.fn.call(y.context),!0;case 2:return y.fn.call(y.context,N),!0;case 3:return y.fn.call(y.context,N,L),!0;case 4:return y.fn.call(y.context,N,L,h),!0;case 5:return y.fn.call(y.context,N,L,h,d),!0;case 6:return y.fn.call(y.context,N,L,h,d,g),!0}for(j=1,I=new Array(x-1);j<x;j++)I[j-1]=arguments[j];y.fn.apply(y.context,I)}else{var M,T=y.length;for(j=0;j<T;j++)switch(y[j].once&&this.removeListener(k,y[j].fn,void 0,!0),x){case 1:y[j].fn.call(y[j].context);break;case 2:y[j].fn.call(y[j].context,N);break;case 3:y[j].fn.call(y[j].context,N,L);break;case 4:y[j].fn.call(y[j].context,N,L,h);break;default:if(!I)for(M=1,I=new Array(x-1);M<x;M++)I[M-1]=arguments[M];y[j].fn.apply(y[j].context,I)}}return!0},E.prototype.on=function(k,N,L){return _(this,k,N,L,!1)},E.prototype.once=function(k,N,L){return _(this,k,N,L,!0)},E.prototype.removeListener=function(k,N,L,h){var d=f?f+k:k;if(!this._events[d])return this;if(!N)return v(this,d),this;var g=this._events[d];if(g.fn)g.fn!==N||h&&!g.once||L&&g.context!==L||v(this,d);else{for(var C=0,I=[],j=g.length;C<j;C++)(g[C].fn!==N||h&&!g[C].once||L&&g[C].context!==L)&&I.push(g[C]);I.length?this._events[d]=I.length===1?I[0]:I:v(this,d)}return this},E.prototype.removeAllListeners=function(k){var N;return k?(N=f?f+k:k,this._events[N]&&v(this,N)):(this._events=new p,this._eventsCount=0),this},E.prototype.off=E.prototype.removeListener,E.prototype.addListener=E.prototype.on,E.prefixed=f,E.EventEmitter=E,a.exports=E},717:a=>{typeof Object.create=="function"?a.exports=function(c,f){c.super_=f,c.prototype=Object.create(f.prototype,{constructor:{value:c,enumerable:!1,writable:!0,configurable:!0}})}:a.exports=function(c,f){c.super_=f;var p=function(){};p.prototype=f.prototype,c.prototype=new p,c.prototype.constructor=c}},824:a=>{var c=1e3,f=60*c,p=60*f,w=24*p,_=7*w,v=365.25*w;function E(k,N,L,h){var d=N>=1.5*L;return Math.round(k/L)+" "+h+(d?"s":"")}a.exports=function(k,N){N=N||{};var L=typeof k;if(L==="string"&&k.length>0)return function(h){if(!((h=String(h)).length>100)){var d=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(h);if(d){var g=parseFloat(d[1]);switch((d[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return g*v;case"weeks":case"week":case"w":return g*_;case"days":case"day":case"d":return g*w;case"hours":case"hour":case"hrs":case"hr":case"h":return g*p;case"minutes":case"minute":case"mins":case"min":case"m":return g*f;case"seconds":case"second":case"secs":case"sec":case"s":return g*c;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return g;default:return}}}}(k);if(L==="number"&&isFinite(k))return N.long?function(h){var d=Math.abs(h);return d>=w?E(h,d,w,"day"):d>=p?E(h,d,p,"hour"):d>=f?E(h,d,f,"minute"):d>=c?E(h,d,c,"second"):h+" ms"}(k):function(h){var d=Math.abs(h);return d>=w?Math.round(h/w)+"d":d>=p?Math.round(h/p)+"h":d>=f?Math.round(h/f)+"m":d>=c?Math.round(h/c)+"s":h+"ms"}(k);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(k))}},520:(a,c,f)=>{var p=f(155),w=p.platform==="win32",_=f(539);function v(y,x){for(var M=[],T=0;T<y.length;T++){var R=y[T];R&&R!=="."&&(R===".."?M.length&&M[M.length-1]!==".."?M.pop():x&&M.push(".."):M.push(R))}return M}function E(y){for(var x=y.length-1,M=0;M<=x&&!y[M];M++);for(var T=x;T>=0&&!y[T];T--);return M===0&&T===x?y:M>T?[]:y.slice(M,T+1)}var k=/^([a-zA-Z]:|[\\\/]{2}[^\\\/]+[\\\/]+[^\\\/]+)?([\\\/])?([\s\S]*?)$/,N=/^([\s\S]*?)((?:\.{1,2}|[^\\\/]+?|)(\.[^.\/\\]*|))(?:[\\\/]*)$/,L={};function h(y){var x=k.exec(y),M=(x[1]||"")+(x[2]||""),T=x[3]||"",R=N.exec(T);return[M,R[1],R[2],R[3]]}function d(y){var x=k.exec(y),M=x[1]||"",T=!!M&&M[1]!==":";return{device:M,isUnc:T,isAbsolute:T||!!x[2],tail:x[3]}}function g(y){return"\\\\"+y.replace(/^[\\\/]+/,"").replace(/[\\\/]+/g,"\\")}L.resolve=function(){for(var y="",x="",M=!1,T=arguments.length-1;T>=-1;T--){var R;if(T>=0?R=arguments[T]:y?(R=p.env["="+y])&&R.substr(0,3).toLowerCase()===y.toLowerCase()+"\\"||(R=y+"\\"):R=p.cwd(),!_.isString(R))throw new TypeError("Arguments to path.resolve must be strings");if(R){var ee=d(R),le=ee.device,J=ee.isUnc,_e=ee.isAbsolute,Ae=ee.tail;if((!le||!y||le.toLowerCase()===y.toLowerCase())&&(y||(y=le),M||(x=Ae+"\\"+x,M=_e),y&&M))break}}return J&&(y=g(y)),y+(M?"\\":"")+(x=v(x.split(/[\\\/]+/),!M).join("\\"))||"."},L.normalize=function(y){var x=d(y),M=x.device,T=x.isUnc,R=x.isAbsolute,ee=x.tail,le=/[\\\/]$/.test(ee);return(ee=v(ee.split(/[\\\/]+/),!R).join("\\"))||R||(ee="."),ee&&le&&(ee+="\\"),T&&(M=g(M)),M+(R?"\\":"")+ee},L.isAbsolute=function(y){return d(y).isAbsolute},L.join=function(){for(var y=[],x=0;x<arguments.length;x++){var M=arguments[x];if(!_.isString(M))throw new TypeError("Arguments to path.join must be strings");M&&y.push(M)}var T=y.join("\\");return/^[\\\/]{2}[^\\\/]/.test(y[0])||(T=T.replace(/^[\\\/]{2,}/,"\\")),L.normalize(T)},L.relative=function(y,x){y=L.resolve(y),x=L.resolve(x);for(var M=y.toLowerCase(),T=x.toLowerCase(),R=E(x.split("\\")),ee=E(M.split("\\")),le=E(T.split("\\")),J=Math.min(ee.length,le.length),_e=J,Ae=0;Ae<J;Ae++)if(ee[Ae]!==le[Ae]){_e=Ae;break}if(_e==0)return x;var Pe=[];for(Ae=_e;Ae<ee.length;Ae++)Pe.push("..");return(Pe=Pe.concat(R.slice(_e))).join("\\")},L._makeLong=function(y){if(!_.isString(y))return y;if(!y)return"";var x=L.resolve(y);return/^[a-zA-Z]\:\\/.test(x)?"\\\\?\\"+x:/^\\\\[^?.]/.test(x)?"\\\\?\\UNC\\"+x.substring(2):y},L.dirname=function(y){var x=h(y),M=x[0],T=x[1];return M||T?(T&&(T=T.substr(0,T.length-1)),M+T):"."},L.basename=function(y,x){var M=h(y)[2];return x&&M.substr(-1*x.length)===x&&(M=M.substr(0,M.length-x.length)),M},L.extname=function(y){return h(y)[3]},L.format=function(y){if(!_.isObject(y))throw new TypeError("Parameter 'pathObject' must be an object, not "+typeof y);var x=y.root||"";if(!_.isString(x))throw new TypeError("'pathObject.root' must be a string or undefined, not "+typeof y.root);var M=y.dir,T=y.base||"";return M?M[M.length-1]===L.sep?M+T:M+L.sep+T:T},L.parse=function(y){if(!_.isString(y))throw new TypeError("Parameter 'pathString' must be a string, not "+typeof y);var x=h(y);if(!x||x.length!==4)throw new TypeError("Invalid path '"+y+"'");return{root:x[0],dir:x[0]+x[1].slice(0,-1),base:x[2],ext:x[3],name:x[2].slice(0,x[2].length-x[3].length)}},L.sep="\\",L.delimiter=";";var C=/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/,I={};function j(y){return C.exec(y).slice(1)}I.resolve=function(){for(var y="",x=!1,M=arguments.length-1;M>=-1&&!x;M--){var T=M>=0?arguments[M]:p.cwd();if(!_.isString(T))throw new TypeError("Arguments to path.resolve must be strings");T&&(y=T+"/"+y,x=T[0]==="/")}return(x?"/":"")+(y=v(y.split("/"),!x).join("/"))||"."},I.normalize=function(y){var x=I.isAbsolute(y),M=y&&y[y.length-1]==="/";return(y=v(y.split("/"),!x).join("/"))||x||(y="."),y&&M&&(y+="/"),(x?"/":"")+y},I.isAbsolute=function(y){return y.charAt(0)==="/"},I.join=function(){for(var y="",x=0;x<arguments.length;x++){var M=arguments[x];if(!_.isString(M))throw new TypeError("Arguments to path.join must be strings");M&&(y+=y?"/"+M:M)}return I.normalize(y)},I.relative=function(y,x){y=I.resolve(y).substr(1),x=I.resolve(x).substr(1);for(var M=E(y.split("/")),T=E(x.split("/")),R=Math.min(M.length,T.length),ee=R,le=0;le<R;le++)if(M[le]!==T[le]){ee=le;break}var J=[];for(le=ee;le<M.length;le++)J.push("..");return(J=J.concat(T.slice(ee))).join("/")},I._makeLong=function(y){return y},I.dirname=function(y){var x=j(y),M=x[0],T=x[1];return M||T?(T&&(T=T.substr(0,T.length-1)),M+T):"."},I.basename=function(y,x){var M=j(y)[2];return x&&M.substr(-1*x.length)===x&&(M=M.substr(0,M.length-x.length)),M},I.extname=function(y){return j(y)[3]},I.format=function(y){if(!_.isObject(y))throw new TypeError("Parameter 'pathObject' must be an object, not "+typeof y);var x=y.root||"";if(!_.isString(x))throw new TypeError("'pathObject.root' must be a string or undefined, not "+typeof y.root);return(y.dir?y.dir+I.sep:"")+(y.base||"")},I.parse=function(y){if(!_.isString(y))throw new TypeError("Parameter 'pathString' must be a string, not "+typeof y);var x=j(y);if(!x||x.length!==4)throw new TypeError("Invalid path '"+y+"'");return x[1]=x[1]||"",x[2]=x[2]||"",x[3]=x[3]||"",{root:x[0],dir:x[0]+x[1].slice(0,-1),base:x[2],ext:x[3],name:x[2].slice(0,x[2].length-x[3].length)}},I.sep="/",I.delimiter=":",a.exports=w?L:I,a.exports.posix=I,a.exports.win32=L},155:a=>{var c,f,p=a.exports={};function w(){throw new Error("setTimeout has not been defined")}function _(){throw new Error("clearTimeout has not been defined")}function v(I){if(c===setTimeout)return setTimeout(I,0);if((c===w||!c)&&setTimeout)return c=setTimeout,setTimeout(I,0);try{return c(I,0)}catch{try{return c.call(null,I,0)}catch{return c.call(this,I,0)}}}(function(){try{c=typeof setTimeout=="function"?setTimeout:w}catch{c=w}try{f=typeof clearTimeout=="function"?clearTimeout:_}catch{f=_}})();var E,k=[],N=!1,L=-1;function h(){N&&E&&(N=!1,E.length?k=E.concat(k):L=-1,k.length&&d())}function d(){if(!N){var I=v(h);N=!0;for(var j=k.length;j;){for(E=k,k=[];++L<j;)E&&E[L].run();L=-1,j=k.length}E=null,N=!1,function(y){if(f===clearTimeout)return clearTimeout(y);if((f===_||!f)&&clearTimeout)return f=clearTimeout,clearTimeout(y);try{f(y)}catch{try{return f.call(null,y)}catch{return f.call(this,y)}}}(I)}}function g(I,j){this.fun=I,this.array=j}function C(){}p.nextTick=function(I){var j=new Array(arguments.length-1);if(arguments.length>1)for(var y=1;y<arguments.length;y++)j[y-1]=arguments[y];k.push(new g(I,j)),k.length!==1||N||v(d)},g.prototype.run=function(){this.fun.apply(null,this.array)},p.title="browser",p.browser=!0,p.env={},p.argv=[],p.version="",p.versions={},p.on=C,p.addListener=C,p.once=C,p.off=C,p.removeListener=C,p.removeAllListeners=C,p.emit=C,p.prependListener=C,p.prependOnceListener=C,p.listeners=function(I){return[]},p.binding=function(I){throw new Error("process.binding is not supported")},p.cwd=function(){return"/"},p.chdir=function(I){throw new Error("process.chdir is not supported")},p.umask=function(){return 0}},384:a=>{a.exports=function(c){return c&&typeof c=="object"&&typeof c.copy=="function"&&typeof c.fill=="function"&&typeof c.readUInt8=="function"}},539:(a,c,f)=>{var p=f(155),w=/%[sdj%]/g;c.format=function(O){if(!y(O)){for(var z=[],B=0;B<arguments.length;B++)z.push(E(arguments[B]));return z.join(" ")}B=1;for(var ne=arguments,Ce=ne.length,V=String(O).replace(w,function(ce){if(ce==="%%")return"%";if(B>=Ce)return ce;switch(ce){case"%s":return String(ne[B++]);case"%d":return Number(ne[B++]);case"%j":try{return JSON.stringify(ne[B++])}catch{return"[Circular]"}default:return ce}}),se=ne[B];B<Ce;se=ne[++B])I(se)||!T(se)?V+=" "+se:V+=" "+E(se);return V},c.deprecate=function(O,z){if(x(f.g.process))return function(){return c.deprecate(O,z).apply(this,arguments)};if(p.noDeprecation===!0)return O;var B=!1;return function(){if(!B){if(p.throwDeprecation)throw new Error(z);p.traceDeprecation?console.trace(z):console.error(z),B=!0}return O.apply(this,arguments)}};var _,v={};function E(O,z){var B={seen:[],stylize:N};return arguments.length>=3&&(B.depth=arguments[2]),arguments.length>=4&&(B.colors=arguments[3]),C(z)?B.showHidden=z:z&&c._extend(B,z),x(B.showHidden)&&(B.showHidden=!1),x(B.depth)&&(B.depth=2),x(B.colors)&&(B.colors=!1),x(B.customInspect)&&(B.customInspect=!0),B.colors&&(B.stylize=k),L(B,O,B.depth)}function k(O,z){var B=E.styles[z];return B?"\x1B["+E.colors[B][0]+"m"+O+"\x1B["+E.colors[B][1]+"m":O}function N(O,z){return O}function L(O,z,B){if(O.customInspect&&z&&le(z.inspect)&&z.inspect!==c.inspect&&(!z.constructor||z.constructor.prototype!==z)){var ne=z.inspect(B,O);return y(ne)||(ne=L(O,ne,B)),ne}var Ce=function(ye,pe){if(x(pe))return ye.stylize("undefined","undefined");if(y(pe)){var Ue="'"+JSON.stringify(pe).replace(/^"|"$/g,"").replace(/'/g,"\\'").replace(/\\"/g,'"')+"'";return ye.stylize(Ue,"string")}if(j(pe))return ye.stylize(""+pe,"number");if(C(pe))return ye.stylize(""+pe,"boolean");if(I(pe))return ye.stylize("null","null")}(O,z);if(Ce)return Ce;var V=Object.keys(z),se=function(ye){var pe={};return ye.forEach(function(Ue,Ge){pe[Ue]=!0}),pe}(V);if(O.showHidden&&(V=Object.getOwnPropertyNames(z)),ee(z)&&(V.indexOf("message")>=0||V.indexOf("description")>=0))return h(z);if(V.length===0){if(le(z)){var ce=z.name?": "+z.name:"";return O.stylize("[Function"+ce+"]","special")}if(M(z))return O.stylize(RegExp.prototype.toString.call(z),"regexp");if(R(z))return O.stylize(Date.prototype.toString.call(z),"date");if(ee(z))return h(z)}var ze,Qe="",G=!1,oe=["{","}"];return g(z)&&(G=!0,oe=["[","]"]),le(z)&&(Qe=" [Function"+(z.name?": "+z.name:"")+"]"),M(z)&&(Qe=" "+RegExp.prototype.toString.call(z)),R(z)&&(Qe=" "+Date.prototype.toUTCString.call(z)),ee(z)&&(Qe=" "+h(z)),V.length!==0||G&&z.length!=0?B<0?M(z)?O.stylize(RegExp.prototype.toString.call(z),"regexp"):O.stylize("[Object]","special"):(O.seen.push(z),ze=G?function(ye,pe,Ue,Ge,jt){for(var ln=[],vt=0,ft=pe.length;vt<ft;++vt)q(pe,String(vt))?ln.push(d(ye,pe,Ue,Ge,String(vt),!0)):ln.push("");return jt.forEach(function(Zn){Zn.match(/^\d+$/)||ln.push(d(ye,pe,Ue,Ge,Zn,!0))}),ln}(O,z,B,se,V):V.map(function(ye){return d(O,z,B,se,ye,G)}),O.seen.pop(),function(ye,pe,Ue){return ye.reduce(function(Ge,jt){return jt.indexOf(`
`)>=0,Ge+jt.replace(/\u001b\[\d\d?m/g,"").length+1},0)>60?Ue[0]+(pe===""?"":pe+`
 `)+" "+ye.join(`,
  `)+" "+Ue[1]:Ue[0]+pe+" "+ye.join(", ")+" "+Ue[1]}(ze,Qe,oe)):oe[0]+Qe+oe[1]}function h(O){return"["+Error.prototype.toString.call(O)+"]"}function d(O,z,B,ne,Ce,V){var se,ce,ze;if((ze=Object.getOwnPropertyDescriptor(z,Ce)||{value:z[Ce]}).get?ce=ze.set?O.stylize("[Getter/Setter]","special"):O.stylize("[Getter]","special"):ze.set&&(ce=O.stylize("[Setter]","special")),q(ne,Ce)||(se="["+Ce+"]"),ce||(O.seen.indexOf(ze.value)<0?(ce=I(B)?L(O,ze.value,null):L(O,ze.value,B-1)).indexOf(`
`)>-1&&(ce=V?ce.split(`
`).map(function(Qe){return"  "+Qe}).join(`
`).substr(2):`
`+ce.split(`
`).map(function(Qe){return"   "+Qe}).join(`
`)):ce=O.stylize("[Circular]","special")),x(se)){if(V&&Ce.match(/^\d+$/))return ce;(se=JSON.stringify(""+Ce)).match(/^"([a-zA-Z_][a-zA-Z_0-9]*)"$/)?(se=se.substr(1,se.length-2),se=O.stylize(se,"name")):(se=se.replace(/'/g,"\\'").replace(/\\"/g,'"').replace(/(^"|"$)/g,"'"),se=O.stylize(se,"string"))}return se+": "+ce}function g(O){return Array.isArray(O)}function C(O){return typeof O=="boolean"}function I(O){return O===null}function j(O){return typeof O=="number"}function y(O){return typeof O=="string"}function x(O){return O===void 0}function M(O){return T(O)&&J(O)==="[object RegExp]"}function T(O){return typeof O=="object"&&O!==null}function R(O){return T(O)&&J(O)==="[object Date]"}function ee(O){return T(O)&&(J(O)==="[object Error]"||O instanceof Error)}function le(O){return typeof O=="function"}function J(O){return Object.prototype.toString.call(O)}function _e(O){return O<10?"0"+O.toString(10):O.toString(10)}c.debuglog=function(O){if(x(_)&&(_=p.env.NODE_DEBUG||""),O=O.toUpperCase(),!v[O])if(new RegExp("\\b"+O+"\\b","i").test(_)){var z=p.pid;v[O]=function(){var B=c.format.apply(c,arguments);console.error("%s %d: %s",O,z,B)}}else v[O]=function(){};return v[O]},c.inspect=E,E.colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]},E.styles={special:"cyan",number:"yellow",boolean:"yellow",undefined:"grey",null:"bold",string:"green",date:"magenta",regexp:"red"},c.isArray=g,c.isBoolean=C,c.isNull=I,c.isNullOrUndefined=function(O){return O==null},c.isNumber=j,c.isString=y,c.isSymbol=function(O){return typeof O=="symbol"},c.isUndefined=x,c.isRegExp=M,c.isObject=T,c.isDate=R,c.isError=ee,c.isFunction=le,c.isPrimitive=function(O){return O===null||typeof O=="boolean"||typeof O=="number"||typeof O=="string"||typeof O=="symbol"||O===void 0},c.isBuffer=f(384);var Ae=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function Pe(){var O=new Date,z=[_e(O.getHours()),_e(O.getMinutes()),_e(O.getSeconds())].join(":");return[O.getDate(),Ae[O.getMonth()],z].join(" ")}function q(O,z){return Object.prototype.hasOwnProperty.call(O,z)}c.log=function(){console.log("%s - %s",Pe(),c.format.apply(c,arguments))},c.inherits=f(717),c._extend=function(O,z){if(!z||!T(z))return O;for(var B=Object.keys(z),ne=B.length;ne--;)O[B[ne]]=z[B[ne]];return O}}},r={};function o(a){var c=r[a];if(c!==void 0)return c.exports;var f=r[a]={exports:{}};return n[a].call(f.exports,f,f.exports,o),f.exports}o.n=a=>{var c=a&&a.__esModule?()=>a.default:()=>a;return o.d(c,{a:c}),c},o.d=(a,c)=>{for(var f in c)o.o(c,f)&&!o.o(a,f)&&Object.defineProperty(a,f,{enumerable:!0,get:c[f]})},o.g=function(){if(typeof globalThis=="object")return globalThis;try{return this||new Function("return this")()}catch{if(typeof window=="object")return window}}(),o.o=(a,c)=>Object.prototype.hasOwnProperty.call(a,c),o.r=a=>{typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(a,"__esModule",{value:!0})};var i={};return(()=>{o.r(i),o.d(i,{LSPluginUser:()=>Ps,setupPluginUserInstance:()=>gc});var a=o(520),c=(o(856),o(996)),f=o.n(c),p=function(){return p=Object.assign||function(s){for(var l,u=1,m=arguments.length;u<m;u++)for(var P in l=arguments[u])Object.prototype.hasOwnProperty.call(l,P)&&(s[P]=l[P]);return s},p.apply(this,arguments)};function w(s){return s.toLowerCase()}var _=[/([a-z0-9])([A-Z])/g,/([A-Z])([A-Z][a-z])/g],v=/[^A-Z0-9]+/gi;function E(s,l,u){return l instanceof RegExp?s.replace(l,u):l.reduce(function(m,P){return m.replace(P,u)},s)}function k(s,l){return l===void 0&&(l={}),function(u,m){m===void 0&&(m={});for(var P=m.splitRegexp,b=P===void 0?_:P,A=m.stripRegexp,D=A===void 0?v:A,H=m.transform,Z=H===void 0?w:H,te=m.delimiter,X=te===void 0?" ":te,ie=E(E(u,b,"$1\0$2"),D,"\0"),me=0,ue=ie.length;ie.charAt(me)==="\0";)me++;for(;ie.charAt(ue-1)==="\0";)ue--;return ie.slice(me,ue).split("\0").map(Z).join(X)}(s,p({delimiter:"."},l))}var N=o(729),L=o.n(N);function h(s,l,u){return l in s?Object.defineProperty(s,l,{value:u,enumerable:!0,configurable:!0,writable:!0}):s[l]=u,s}const d=navigator.platform.toLowerCase()==="win32"?a.win32:a.posix,g=function(s,l){return l===void 0&&(l={}),k(s,p({delimiter:"_"},l))};class C extends L(){constructor(l,u){super(),h(this,"_tag",void 0),h(this,"_opts",void 0),h(this,"_logs",[]),this._tag=l,this._opts=u}write(l,u,m){var P;u!=null&&u.length&&u[u.length-1]===!0&&(m=!0,u.pop());const b=u.reduce((D,H)=>(H&&H instanceof Error?D+=`${H.message} ${H.stack}`:D+=H.toString(),D),`[${this._tag}][${new Date().toLocaleTimeString()}] `);var A;this._logs.push([l,b]),(m||(P=this._opts)!==null&&P!==void 0&&P.console)&&((A=console)===null||A===void 0||A[l==="ERROR"?"error":"debug"](`${l}: ${b}`)),this.emit("change")}clear(){this._logs=[],this.emit("change")}info(...l){this.write("INFO",l)}error(...l){this.write("ERROR",l)}warn(...l){this.write("WARN",l)}setTag(l){this._tag=l}toJSON(){return this._logs}}function I(s,...l){try{const u=new URL(s);if(!u.origin)throw new Error(null);const m=d.join(s.substr(u.origin.length),...l);return u.origin+m}catch{return d.join(s,...l)}}function j(s,l){let u,m,P=!1;const b=D=>H=>{s&&clearTimeout(s),D(H),P=!0},A=new Promise((D,H)=>{u=b(D),m=b(H),s&&(s=setTimeout(()=>m(new Error(`[deferred timeout] ${l}`)),s))});return{created:Date.now(),setTag:D=>l=D,resolve:u,reject:m,promise:A,get settled(){return P}}}const y=new Map;window.__injectedUIEffects=y;var x=o(227),M=o.n(x);function T(s,l,u){return l in s?Object.defineProperty(s,l,{value:u,enumerable:!0,configurable:!0,writable:!0}):s[l]=u,s}const R="application/x-postmate-v1+json";let ee=0;const le={handshake:1,"handshake-reply":1,call:1,emit:1,reply:1,request:1},J=(s,l)=>(typeof l!="string"||s.origin===l)&&!!s.data&&(typeof s.data!="object"||"postmate"in s.data)&&s.data.type===R&&!!le[s.data.postmate];class _e{constructor(l){T(this,"parent",void 0),T(this,"frame",void 0),T(this,"child",void 0),T(this,"events",{}),T(this,"childOrigin",void 0),T(this,"listener",void 0),this.parent=l.parent,this.frame=l.frame,this.child=l.child,this.childOrigin=l.childOrigin,this.listener=u=>{if(!J(u,this.childOrigin))return!1;const{data:m,name:P}=((u||{}).data||{}).value||{};u.data.postmate==="emit"&&P in this.events&&this.events[P].forEach(b=>{b.call(this,m)})},this.parent.addEventListener("message",this.listener,!1)}get(l,...u){return new Promise((m,P)=>{const b=++ee,A=D=>{D.data.uid===b&&D.data.postmate==="reply"&&(this.parent.removeEventListener("message",A,!1),D.data.error?P(D.data.error):m(D.data.value))};this.parent.addEventListener("message",A,!1),this.child.postMessage({postmate:"request",type:R,property:l,args:u,uid:b},this.childOrigin)})}call(l,u){this.child.postMessage({postmate:"call",type:R,property:l,data:u},this.childOrigin)}on(l,u){this.events[l]||(this.events[l]=[]),this.events[l].push(u)}destroy(){window.removeEventListener("message",this.listener,!1),this.frame.parentNode.removeChild(this.frame)}}class Ae{constructor(l){T(this,"model",void 0),T(this,"parent",void 0),T(this,"parentOrigin",void 0),T(this,"child",void 0),this.model=l.model,this.parent=l.parent,this.parentOrigin=l.parentOrigin,this.child=l.child,this.child.addEventListener("message",u=>{if(!J(u,this.parentOrigin))return;const{property:m,uid:P,data:b,args:A}=u.data;u.data.postmate!=="call"?((D,H,Z)=>{const te=typeof D[H]=="function"?D[H].apply(null,Z):D[H];return Promise.resolve(te)})(this.model,m,A).then(D=>{u.source.postMessage({property:m,postmate:"reply",type:R,uid:P,value:D},u.origin)}).catch(D=>{u.source.postMessage({property:m,postmate:"reply",type:R,uid:P,error:D},u.origin)}):m in this.model&&typeof this.model[m]=="function"&&this.model[m](b)})}emit(l,u){this.parent.postMessage({postmate:"emit",type:R,value:{name:l,data:u}},this.parentOrigin)}}class Pe{constructor(l){T(this,"container",void 0),T(this,"parent",void 0),T(this,"frame",void 0),T(this,"child",void 0),T(this,"childOrigin",void 0),T(this,"url",void 0),T(this,"model",void 0),this.container=l.container,this.url=l.url,this.parent=window,this.frame=document.createElement("iframe"),l.id&&(this.frame.id=l.id),l.name&&(this.frame.name=l.name),this.frame.classList.add.apply(this.frame.classList,l.classListArray||[]),this.container.appendChild(this.frame),this.child=this.frame.contentWindow,this.model=l.model||{}}sendHandshake(l){const u=(b=>{const A=document.createElement("a");A.href=b;const D=A.protocol.length>4?A.protocol:window.location.protocol,H=A.host.length?A.port==="80"||A.port==="443"?A.hostname:A.host:window.location.host;return A.origin||`${D}//${H}`})(l=l||this.url);let m,P=0;return new Promise((b,A)=>{const D=Z=>!!J(Z,u)&&(Z.data.postmate==="handshake-reply"?(clearInterval(m),this.parent.removeEventListener("message",D,!1),this.childOrigin=Z.origin,b(new _e(this))):A("Failed handshake"));this.parent.addEventListener("message",D,!1);const H=()=>{P++,this.child.postMessage({postmate:"handshake",type:R,model:this.model},u),P===5&&clearInterval(m)};this.frame.addEventListener("load",()=>{H(),m=setInterval(H,500)}),this.frame.src=l})}destroy(){this.frame.parentNode.removeChild(this.frame)}}T(Pe,"debug",!1),T(Pe,"Model",void 0);class q{constructor(l){T(this,"child",void 0),T(this,"model",void 0),T(this,"parent",void 0),T(this,"parentOrigin",void 0),this.child=window,this.model=l,this.parent=this.child.parent}sendHandshakeReply(){return new Promise((l,u)=>{const m=P=>{if(P.data.postmate){if(P.data.postmate==="handshake"){this.child.removeEventListener("message",m,!1),P.source.postMessage({postmate:"handshake-reply",type:R},P.origin),this.parentOrigin=P.origin;const b=P.data.model;return b&&Object.keys(b).forEach(A=>{this.model[A]=b[A]}),l(new Ae(this))}return u("Handshake Reply Failed")}};this.child.addEventListener("message",m,!1)})}}function O(s,l,u){return l in s?Object.defineProperty(s,l,{value:u,enumerable:!0,configurable:!0,writable:!0}):s[l]=u,s}const{importHTML:z,createSandboxContainer:B}=window.QSandbox||{};function ne(s,l){return s.startsWith("http")?fetch(s,l):(s=s.replace("file://",""),new Promise(async(u,m)=>{try{const P=await window.apis.doAction(["readFile",s]);u({text:()=>P})}catch(P){console.error(P),m(P)}}))}class Ce extends L(){constructor(l){super(),O(this,"_pluginLocal",void 0),O(this,"_frame",void 0),O(this,"_root",void 0),O(this,"_loaded",!1),O(this,"_unmountFns",[]),this._pluginLocal=l,l._dispose(()=>{this._unmount()})}async load(){const{name:l,entry:u}=this._pluginLocal.options;if(this.loaded||!u)return;const{template:m,execScripts:P}=await z(u,{fetch:ne});this._mount(m,document.body);const b=B(l,{elementGetter:()=>{var D;return(D=this._root)===null||D===void 0?void 0:D.firstChild}}).instance.proxy;b.__shadow_mode__=!0,b.LSPluginLocal=this._pluginLocal,b.LSPluginShadow=this,b.LSPluginUser=b.logseq=new Ps(this._pluginLocal.toJSON(),this._pluginLocal.caller);const A=await P(b,!0);this._unmountFns.push(A.unmount),this._loaded=!0}_mount(l,u){const m=this._frame=document.createElement("div");m.classList.add("lsp-shadow-sandbox"),m.id=this._pluginLocal.id,this._root=m.attachShadow({mode:"open"}),this._root.innerHTML=`<div>${l}</div>`,u.appendChild(m),this.emit("mounted")}_unmount(){for(const l of this._unmountFns)l&&l.call(null)}destroy(){var l,u;(l=this.frame)===null||l===void 0||(u=l.parentNode)===null||u===void 0||u.removeChild(this.frame)}get loaded(){return this._loaded}get document(){var l;return(l=this._root)===null||l===void 0?void 0:l.firstChild}get frame(){return this._frame}}function V(s,l,u){return l in s?Object.defineProperty(s,l,{value:u,enumerable:!0,configurable:!0,writable:!0}):s[l]=u,s}const se=M()("LSPlugin:caller"),ce="#await#response#",ze="#lspmsg#",Qe="#lspmsg#error#",G=s=>`#lspmsg#${s}`;class oe extends L(){constructor(l){super(),V(this,"_pluginLocal",void 0),V(this,"_connected",!1),V(this,"_parent",void 0),V(this,"_child",void 0),V(this,"_shadow",void 0),V(this,"_status",void 0),V(this,"_userModel",{}),V(this,"_call",void 0),V(this,"_callUserModel",void 0),V(this,"_debugTag",""),this._pluginLocal=l,l&&(this._debugTag=l.debugTag)}async connectToChild(){if(this._connected)return;const{shadow:l}=this._pluginLocal;l?await this._setupShadowSandbox():await this._setupIframeSandbox()}async connectToParent(l={}){if(this._connected)return;const u=this,m=this._pluginLocal!=null;let P=0;const b=new Map,A=j(6e4),D=this._extendUserModel({"#lspmsg#ready#":async te=>{D[G(te?.pid)]=({type:X,payload:ie})=>{se(`[host (_call) -> *user] ${this._debugTag}`,X,ie),u.emit(X,ie)},await A.resolve()},"#lspmsg#beforeunload#":async te=>{const X=j(1e4);u.emit("beforeunload",Object.assign({actor:X},te)),await X.promise},"#lspmsg#settings#":async({type:te,payload:X})=>{u.emit("settings:changed",X)},[ze]:async({ns:te,type:X,payload:ie})=>{se(`[host (async) -> *user] ${this._debugTag} ns=${te} type=${X}`,ie),te&&te.startsWith("hook")?u.emit(`${te}:${X}`,ie):u.emit(X,ie)},"#lspmsg#reply#":({_sync:te,result:X})=>{if(se(`[sync host -> *user] #${te}`,X),b.has(te)){const ie=b.get(te);ie&&(X!=null&&X.hasOwnProperty(Qe)?ie.reject(X[Qe]):ie.resolve(X),b.delete(te))}},...l});var H;if(m)return await A.promise,JSON.parse(JSON.stringify((H=this._pluginLocal)===null||H===void 0?void 0:H.toJSON()));const Z=new q(D).sendHandshakeReply();return this._status="pending",await Z.then(te=>{this._child=te,this._connected=!0,this._call=async(X,ie={},me)=>{if(me){const ue=++P;b.set(ue,me),ie._sync=ue,me.setTag(`async call #${ue}`),se(`async call #${ue}`)}return te.emit(G(D.baseInfo.id),{type:X,payload:ie}),me?.promise},this._callUserModel=async(X,ie)=>{try{D[X](ie)}catch{se(`[model method] #${X} not existed`)}},setInterval(()=>{if(b.size>100)for(const[X,ie]of b)ie.settled&&b.delete(X)},18e5)}).finally(()=>{this._status=void 0}),await A.promise,D.baseInfo}async call(l,u={}){var m;return(m=this._call)===null||m===void 0?void 0:m.call(this,l,u)}async callAsync(l,u={}){var m;const P=j(1e4);return(m=this._call)===null||m===void 0?void 0:m.call(this,l,u,P)}async callUserModel(l,...u){var m;return(m=this._callUserModel)===null||m===void 0?void 0:m.apply(this,[l,...u])}async callUserModelAsync(l,...u){var m;return l=`${ce}${l}`,(m=this._callUserModel)===null||m===void 0?void 0:m.apply(this,[l,...u])}async _setupIframeSandbox(){const l=this._pluginLocal,u=l.id,m=`${u}_lsp_main`,P=new URL(l.options.entry);P.searchParams.set("__v__",l.options.version);const b=document.querySelector(`#${m}`);b&&b.parentElement.removeChild(b);const A=document.createElement("div");A.classList.add("lsp-iframe-sandbox-container"),A.id=m,A.dataset.pid=u;try{var D;const X=(D=await this._pluginLocal._loadLayoutsData())===null||D===void 0?void 0:D.$$0;if(X){A.dataset.inited_layout="true";let{width:ie,height:me,left:ue,top:ke,vw:xe,vh:We}=X;ue=Math.max(ue,0),ue=typeof xe=="number"?`${Math.min(100*ue/xe,99)}%`:`${ue}px`,ke=Math.max(ke,45),ke=typeof We=="number"?`${Math.min(100*ke/We,99)}%`:`${ke}px`,Object.assign(A.style,{width:ie+"px",height:me+"px",left:ue,top:ke})}}catch(X){console.error("[Restore Layout Error]",X)}document.body.appendChild(A);const H=new Pe({id:u+"_iframe",container:A,url:P.href,classListArray:["lsp-iframe-sandbox"],model:{baseInfo:JSON.parse(JSON.stringify(l.toJSON()))}});let Z,te=H.sendHandshake();return this._status="pending",new Promise((X,ie)=>{Z=setTimeout(()=>{ie(new Error("handshake Timeout")),H.destroy()},4e3),te.then(me=>{this._parent=me,this._connected=!0,this.emit("connected"),me.on(G(l.id),({type:ue,payload:ke})=>{var xe,We;se("[user -> *host] ",ue,ke),(xe=this._pluginLocal)===null||xe===void 0||xe.emit(ue,ke||{}),(We=this._pluginLocal)===null||We===void 0||We.caller.emit(ue,ke||{})}),this._call=async(...ue)=>{await me.call(G(l.id),{type:ue[0],payload:Object.assign(ue[1]||{},{$$pid:l.id})})},this._callUserModel=async(ue,...ke)=>{if(ue.startsWith(ce))return await me.get(ue.replace(ce,""),...ke);me.call(ue,ke?.[0])},X(null)}).catch(me=>{ie(me)}).finally(()=>{clearTimeout(Z)})}).catch(X=>{throw se("[iframe sandbox] error",X),X}).finally(()=>{this._status=void 0})}async _setupShadowSandbox(){const l=this._pluginLocal,u=this._shadow=new Ce(l);try{this._status="pending",await u.load(),this._connected=!0,this.emit("connected"),this._call=async(m,P={},b)=>{var A;return b&&(P.actor=b),(A=this._pluginLocal)===null||A===void 0||A.emit(m,Object.assign(P,{$$pid:l.id})),b?.promise},this._callUserModel=async(...m)=>{var P;let b=m[0];(P=b)!==null&&P!==void 0&&P.startsWith(ce)&&(b=b.replace(ce,""));const A=m[1]||{},D=this._userModel[b];typeof D=="function"&&await D.call(null,A)}}catch(m){throw se("[shadow sandbox] error",m),m}finally{this._status=void 0}}_extendUserModel(l){return Object.assign(this._userModel,l)}_getSandboxIframeContainer(){var l;return(l=this._parent)===null||l===void 0?void 0:l.frame.parentNode}_getSandboxShadowContainer(){var l;return(l=this._shadow)===null||l===void 0?void 0:l.frame.parentNode}_getSandboxIframeRoot(){var l;return(l=this._parent)===null||l===void 0?void 0:l.frame}_getSandboxShadowRoot(){var l;return(l=this._shadow)===null||l===void 0?void 0:l.frame}set debugTag(l){this._debugTag=l}async destroy(){var l;let u=null;this._parent&&(u=this._getSandboxIframeContainer(),await this._parent.destroy()),this._shadow&&(u=this._getSandboxShadowContainer(),this._shadow.destroy()),(l=u)===null||l===void 0||l.parentNode.removeChild(u)}}function ye(s,l,u){return l in s?Object.defineProperty(s,l,{value:u,enumerable:!0,configurable:!0,writable:!0}):s[l]=u,s}class pe{constructor(l,u){ye(this,"ctx",void 0),ye(this,"opts",void 0),this.ctx=l,this.opts=u}get ctxId(){return this.ctx.baseInfo.id}setItem(l,u){var m;return this.ctx.caller.callAsync("api:call",{method:"write-plugin-storage-file",args:[this.ctxId,l,u,(m=this.opts)===null||m===void 0?void 0:m.assets]})}getItem(l){var u;return this.ctx.caller.callAsync("api:call",{method:"read-plugin-storage-file",args:[this.ctxId,l,(u=this.opts)===null||u===void 0?void 0:u.assets]})}removeItem(l){var u;return this.ctx.caller.call("api:call",{method:"unlink-plugin-storage-file",args:[this.ctxId,l,(u=this.opts)===null||u===void 0?void 0:u.assets]})}allKeys(){var l;return this.ctx.caller.callAsync("api:call",{method:"list-plugin-storage-files",args:[this.ctxId,(l=this.opts)===null||l===void 0?void 0:l.assets]})}clear(){var l;return this.ctx.caller.call("api:call",{method:"clear-plugin-storage-files",args:[this.ctxId,(l=this.opts)===null||l===void 0?void 0:l.assets]})}hasItem(l){var u;return this.ctx.caller.callAsync("api:call",{method:"exist-plugin-storage-file",args:[this.ctxId,l,(u=this.opts)===null||u===void 0?void 0:u.assets]})}}class Ue{constructor(l){var u,m,P;P=void 0,(m="ctx")in(u=this)?Object.defineProperty(u,m,{value:P,enumerable:!0,configurable:!0,writable:!0}):u[m]=P,this.ctx=l}get React(){return this.ensureHostScope().React}get ReactDOM(){return this.ensureHostScope().ReactDOM}get pluginLocal(){return this.ensureHostScope().LSPluginCore.ensurePlugin(this.ctx.baseInfo.id)}invokeExperMethod(l,...u){var m,P;const b=this.ensureHostScope();return l=(m=g(l))===null||m===void 0?void 0:m.toLowerCase(),(P=b.logseq.api["exper_"+l])===null||P===void 0?void 0:P.apply(b,u)}async loadScripts(...l){(l=l.map(u=>u!=null&&u.startsWith("http")?u:this.ctx.resolveResourceFullUrl(u))).unshift(this.ctx.baseInfo.id),await this.invokeExperMethod("loadScripts",...l)}registerFencedCodeRenderer(l,u){return this.ensureHostScope().logseq.api.exper_register_fenced_code_renderer(this.ctx.baseInfo.id,l,u)}registerExtensionsEnhancer(l,u){const m=this.ensureHostScope();return l==="katex"&&m.katex&&u(m.katex).catch(console.error),m.logseq.api.exper_register_extensions_enhancer(this.ctx.baseInfo.id,l,u)}ensureHostScope(){if(window===top)throw new Error("Can not access host scope!");return top}}function Ge(s,l,u){return l in s?Object.defineProperty(s,l,{value:u,enumerable:!0,configurable:!0,writable:!0}):s[l]=u,s}const jt=s=>`task_callback_${s}`;class ln{constructor(l,u,m={}){Ge(this,"_client",void 0),Ge(this,"_requestId",void 0),Ge(this,"_requestOptions",void 0),Ge(this,"_promise",void 0),Ge(this,"_aborted",!1),this._client=l,this._requestId=u,this._requestOptions=m,this._promise=new Promise((D,H)=>{if(!this._requestId)return H(null);this._client.once(jt(this._requestId),Z=>{Z&&Z instanceof Error?H(Z):D(Z)})});const{success:P,fail:b,final:A}=this._requestOptions;this._promise.then(D=>{P?.(D)}).catch(D=>{b?.(D)}).finally(()=>{A?.()})}abort(){this._requestOptions.abortable&&!this._aborted&&(this._client.ctx._execCallableAPI("http_request_abort",this._requestId),this._aborted=!0)}get promise(){return this._promise}get client(){return this._client}get requestId(){return this._requestId}}class vt extends N.EventEmitter{constructor(l){super(),Ge(this,"_ctx",void 0),this._ctx=l,this.ctx.caller.on("#lsp#request#callback",u=>{const m=u?.requestId;m&&this.emit(jt(m),u?.payload)})}static createRequestTask(l,u,m){return new ln(l,u,m)}async _request(l){const u=this.ctx.baseInfo.id,{success:m,fail:P,final:b,...A}=l,D=this.ctx.Experiments.invokeExperMethod("request",u,A),H=vt.createRequestTask(this.ctx.Request,D,l);return A.abortable?H:H.promise}get ctx(){return this._ctx}}const ft=Array.isArray,Zn=typeof Ri=="object"&&Ri&&Ri.Object===Object&&Ri;var os=typeof self=="object"&&self&&self.Object===Object&&self;const Mt=Zn||os||Function("return this")(),Gt=Mt.Symbol;var mi=Object.prototype,is=mi.hasOwnProperty,ls=mi.toString,Jn=Gt?Gt.toStringTag:void 0;const ss=function(s){var l=is.call(s,Jn),u=s[Jn];try{s[Jn]=void 0;var m=!0}catch{}var P=ls.call(s);return m&&(l?s[Jn]=u:delete s[Jn]),P};var gi=Object.prototype.toString;const $=function(s){return gi.call(s)};var K=Gt?Gt.toStringTag:void 0;const re=function(s){return s==null?s===void 0?"[object Undefined]":"[object Null]":K&&K in Object(s)?ss(s):$(s)},fe=function(s){var l=typeof s;return s!=null&&(l=="object"||l=="function")},De=function(s){if(!fe(s))return!1;var l=re(s);return l=="[object Function]"||l=="[object GeneratorFunction]"||l=="[object AsyncFunction]"||l=="[object Proxy]"},At=Mt["__core-js_shared__"];var Yt,Sn=(Yt=/[^.]+$/.exec(At&&At.keys&&At.keys.IE_PROTO||""))?"Symbol(src)_1."+Yt:"";const po=function(s){return!!Sn&&Sn in s};var yi=Function.prototype.toString;const sn=function(s){if(s!=null){try{return yi.call(s)}catch{}try{return s+""}catch{}}return""};var as=/^\[object .+?Constructor\]$/,us=Function.prototype,cs=Object.prototype,xr=us.toString,fs=cs.hasOwnProperty,ds=RegExp("^"+xr.call(fs).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");const ps=function(s){return!(!fe(s)||po(s))&&(De(s)?ds:as).test(sn(s))},ho=function(s,l){return s?.[l]},zt=function(s,l){var u=ho(s,l);return ps(u)?u:void 0},Dt=function(){try{var s=zt(Object,"defineProperty");return s({},"",{}),s}catch{}}(),vi=function(s,l,u){l=="__proto__"&&Dt?Dt(s,l,{configurable:!0,enumerable:!0,value:u,writable:!0}):s[l]=u},Er=function(s){return function(l,u,m){for(var P=-1,b=Object(l),A=m(l),D=A.length;D--;){var H=A[s?D:++P];if(u(b[H],H,b)===!1)break}return l}}(),mo=function(s,l){for(var u=-1,m=Array(s);++u<s;)m[u]=l(u);return m},_n=function(s){return s!=null&&typeof s=="object"},wi=function(s){return _n(s)&&re(s)=="[object Arguments]"};var ki=Object.prototype,hs=ki.hasOwnProperty,Si=ki.propertyIsEnumerable;const wt=wi(function(){return arguments}())?wi:function(s){return _n(s)&&hs.call(s,"callee")&&!Si.call(s,"callee")},er=function(){return!1};var Xt=t&&!t.nodeType&&t,Tr=Xt&&!0&&e&&!e.nodeType&&e,Nr=Tr&&Tr.exports===Xt?Mt.Buffer:void 0;const go=(Nr?Nr.isBuffer:void 0)||er;var ms=/^(?:0|[1-9]\d*)$/;const _i=function(s,l){var u=typeof s;return!!(l=l??9007199254740991)&&(u=="number"||u!="symbol"&&ms.test(s))&&s>-1&&s%1==0&&s<l},Pr=function(s){return typeof s=="number"&&s>-1&&s%1==0&&s<=9007199254740991};var ve={};ve["[object Float32Array]"]=ve["[object Float64Array]"]=ve["[object Int8Array]"]=ve["[object Int16Array]"]=ve["[object Int32Array]"]=ve["[object Uint8Array]"]=ve["[object Uint8ClampedArray]"]=ve["[object Uint16Array]"]=ve["[object Uint32Array]"]=!0,ve["[object Arguments]"]=ve["[object Array]"]=ve["[object ArrayBuffer]"]=ve["[object Boolean]"]=ve["[object DataView]"]=ve["[object Date]"]=ve["[object Error]"]=ve["[object Function]"]=ve["[object Map]"]=ve["[object Number]"]=ve["[object Object]"]=ve["[object RegExp]"]=ve["[object Set]"]=ve["[object String]"]=ve["[object WeakMap]"]=!1;const $e=function(s){return _n(s)&&Pr(s.length)&&!!ve[re(s)]},Ci=function(s){return function(l){return s(l)}};var Be=t&&!t.nodeType&&t,Cn=Be&&!0&&e&&!e.nodeType&&e,Oe=Cn&&Cn.exports===Be&&Zn.process,xn=function(){try{var s=Cn&&Cn.require&&Cn.require("util").types;return s||Oe&&Oe.binding&&Oe.binding("util")}catch{}}(),Or=xn&&xn.isTypedArray;const yo=Or?Ci(Or):$e;var vo=Object.prototype.hasOwnProperty;const xi=function(s,l){var u=ft(s),m=!u&&wt(s),P=!u&&!m&&go(s),b=!u&&!m&&!P&&yo(s),A=u||m||P||b,D=A?mo(s.length,String):[],H=D.length;for(var Z in s)!l&&!vo.call(s,Z)||A&&(Z=="length"||P&&(Z=="offset"||Z=="parent")||b&&(Z=="buffer"||Z=="byteLength"||Z=="byteOffset")||_i(Z,H))||D.push(Z);return D};var En=Object.prototype;const an=function(s){var l=s&&s.constructor;return s===(typeof l=="function"&&l.prototype||En)},wo=function(s,l){return function(u){return s(l(u))}}(Object.keys,Object);var ko=Object.prototype.hasOwnProperty;const Tn=function(s){if(!an(s))return wo(s);var l=[];for(var u in Object(s))ko.call(s,u)&&u!="constructor"&&l.push(u);return l},Ir=function(s){return s!=null&&Pr(s.length)&&!De(s)},Nn=function(s){return Ir(s)?xi(s):Tn(s)},Ei=function(s,l){return s&&Er(s,l,Nn)},So=function(){this.__data__=[],this.size=0},Pn=function(s,l){return s===l||s!=s&&l!=l},Ft=function(s,l){for(var u=s.length;u--;)if(Pn(s[u][0],l))return u;return-1};var On=Array.prototype.splice;const Ti=function(s){var l=this.__data__,u=Ft(l,s);return!(u<0)&&(u==l.length-1?l.pop():On.call(l,u,1),--this.size,!0)},Ni=function(s){var l=this.__data__,u=Ft(l,s);return u<0?void 0:l[u][1]},Pi=function(s){return Ft(this.__data__,s)>-1},_o=function(s,l){var u=this.__data__,m=Ft(u,s);return m<0?(++this.size,u.push([s,l])):u[m][1]=l,this};function un(s){var l=-1,u=s==null?0:s.length;for(this.clear();++l<u;){var m=s[l];this.set(m[0],m[1])}}un.prototype.clear=So,un.prototype.delete=Ti,un.prototype.get=Ni,un.prototype.has=Pi,un.prototype.set=_o;const In=un,Co=function(){this.__data__=new In,this.size=0},Zt=function(s){var l=this.__data__,u=l.delete(s);return this.size=l.size,u},Lr=function(s){return this.__data__.get(s)},Oi=function(s){return this.__data__.has(s)},tr=zt(Mt,"Map"),nr=zt(Object,"create"),Ln=function(){this.__data__=nr?nr(null):{},this.size=0},gs=function(s){var l=this.has(s)&&delete this.__data__[s];return this.size-=l?1:0,l};var Ii=Object.prototype.hasOwnProperty;const xo=function(s){var l=this.__data__;if(nr){var u=l[s];return u==="__lodash_hash_undefined__"?void 0:u}return Ii.call(l,s)?l[s]:void 0};var Li=Object.prototype.hasOwnProperty;const bi=function(s){var l=this.__data__;return nr?l[s]!==void 0:Li.call(l,s)},ys=function(s,l){var u=this.__data__;return this.size+=this.has(s)?0:1,u[s]=nr&&l===void 0?"__lodash_hash_undefined__":l,this};function Rt(s){var l=-1,u=s==null?0:s.length;for(this.clear();++l<u;){var m=s[l];this.set(m[0],m[1])}}Rt.prototype.clear=Ln,Rt.prototype.delete=gs,Rt.prototype.get=xo,Rt.prototype.has=bi,Rt.prototype.set=ys;const br=Rt,vs=function(){this.size=0,this.__data__={hash:new br,map:new(tr||In),string:new br}},Ut=function(s){var l=typeof s;return l=="string"||l=="number"||l=="symbol"||l=="boolean"?s!=="__proto__":s===null},rr=function(s,l){var u=s.__data__;return Ut(l)?u[typeof l=="string"?"string":"hash"]:u.map},ji=function(s){var l=rr(this,s).delete(s);return this.size-=l?1:0,l},Mi=function(s){return rr(this,s).get(s)},ws=function(s){return rr(this,s).has(s)},or=function(s,l){var u=rr(this,s),m=u.size;return u.set(s,l),this.size+=u.size==m?0:1,this};function nt(s){var l=-1,u=s==null?0:s.length;for(this.clear();++l<u;){var m=s[l];this.set(m[0],m[1])}}nt.prototype.clear=vs,nt.prototype.delete=ji,nt.prototype.get=Mi,nt.prototype.has=ws,nt.prototype.set=or;const ir=nt,Ai=function(s,l){var u=this.__data__;if(u instanceof In){var m=u.__data__;if(!tr||m.length<199)return m.push([s,l]),this.size=++u.size,this;u=this.__data__=new ir(m)}return u.set(s,l),this.size=u.size,this};function cn(s){var l=this.__data__=new In(s);this.size=l.size}cn.prototype.clear=Co,cn.prototype.delete=Zt,cn.prototype.get=Lr,cn.prototype.has=Oi,cn.prototype.set=Ai;const lr=cn,ks=function(s){return this.__data__.set(s,"__lodash_hash_undefined__"),this},S=function(s){return this.__data__.has(s)};function Q(s){var l=-1,u=s==null?0:s.length;for(this.__data__=new ir;++l<u;)this.add(s[l])}Q.prototype.add=Q.prototype.push=ks,Q.prototype.has=S;const U=Q,we=function(s,l){for(var u=-1,m=s==null?0:s.length;++u<m;)if(l(s[u],u,s))return!0;return!1},Le=function(s,l){return s.has(l)},Ye=function(s,l,u,m,P,b){var A=1&u,D=s.length,H=l.length;if(D!=H&&!(A&&H>D))return!1;var Z=b.get(s),te=b.get(l);if(Z&&te)return Z==l&&te==s;var X=-1,ie=!0,me=2&u?new U:void 0;for(b.set(s,l),b.set(l,s);++X<D;){var ue=s[X],ke=l[X];if(m)var xe=A?m(ke,ue,X,l,s,b):m(ue,ke,X,s,l,b);if(xe!==void 0){if(xe)continue;ie=!1;break}if(me){if(!we(l,function(We,Tt){if(!Le(me,Tt)&&(ue===We||P(ue,We,u,m,b)))return me.push(Tt)})){ie=!1;break}}else if(ue!==ke&&!P(ue,ke,u,m,b)){ie=!1;break}}return b.delete(s),b.delete(l),ie},He=Mt.Uint8Array,sr=function(s){var l=-1,u=Array(s.size);return s.forEach(function(m,P){u[++l]=[P,m]}),u},Jt=function(s){var l=-1,u=Array(s.size);return s.forEach(function(m){u[++l]=m}),u};var dt=Gt?Gt.prototype:void 0,Eo=dt?dt.valueOf:void 0;const Qu=function(s,l,u,m,P,b,A){switch(u){case"[object DataView]":if(s.byteLength!=l.byteLength||s.byteOffset!=l.byteOffset)return!1;s=s.buffer,l=l.buffer;case"[object ArrayBuffer]":return!(s.byteLength!=l.byteLength||!b(new He(s),new He(l)));case"[object Boolean]":case"[object Date]":case"[object Number]":return Pn(+s,+l);case"[object Error]":return s.name==l.name&&s.message==l.message;case"[object RegExp]":case"[object String]":return s==l+"";case"[object Map]":var D=sr;case"[object Set]":var H=1&m;if(D||(D=Jt),s.size!=l.size&&!H)return!1;var Z=A.get(s);if(Z)return Z==l;m|=2,A.set(s,l);var te=Ye(D(s),D(l),m,P,b,A);return A.delete(s),te;case"[object Symbol]":if(Eo)return Eo.call(s)==Eo.call(l)}return!1},Wp=function(s,l){for(var u=-1,m=l.length,P=s.length;++u<m;)s[P+u]=l[u];return s},Vp=function(s,l,u){var m=l(s);return ft(s)?m:Wp(m,u(s))},Qp=function(s,l){for(var u=-1,m=s==null?0:s.length,P=0,b=[];++u<m;){var A=s[u];l(A,u,s)&&(b[P++]=A)}return b},qp=function(){return[]};var Kp=Object.prototype.propertyIsEnumerable,qu=Object.getOwnPropertySymbols;const Gp=qu?function(s){return s==null?[]:(s=Object(s),Qp(qu(s),function(l){return Kp.call(s,l)}))}:qp,Ku=function(s){return Vp(s,Nn,Gp)};var Yp=Object.prototype.hasOwnProperty;const Xp=function(s,l,u,m,P,b){var A=1&u,D=Ku(s),H=D.length;if(H!=Ku(l).length&&!A)return!1;for(var Z=H;Z--;){var te=D[Z];if(!(A?te in l:Yp.call(l,te)))return!1}var X=b.get(s),ie=b.get(l);if(X&&ie)return X==l&&ie==s;var me=!0;b.set(s,l),b.set(l,s);for(var ue=A;++Z<H;){var ke=s[te=D[Z]],xe=l[te];if(m)var We=A?m(xe,ke,te,l,s,b):m(ke,xe,te,s,l,b);if(!(We===void 0?ke===xe||P(ke,xe,u,m,b):We)){me=!1;break}ue||(ue=te=="constructor")}if(me&&!ue){var Tt=s.constructor,ur=l.constructor;Tt==ur||!("constructor"in s)||!("constructor"in l)||typeof Tt=="function"&&Tt instanceof Tt&&typeof ur=="function"&&ur instanceof ur||(me=!1)}return b.delete(s),b.delete(l),me},Ss=zt(Mt,"DataView"),_s=zt(Mt,"Promise"),Cs=zt(Mt,"Set"),xs=zt(Mt,"WeakMap");var Gu="[object Map]",Yu="[object Promise]",Xu="[object Set]",Zu="[object WeakMap]",Ju="[object DataView]",Zp=sn(Ss),Jp=sn(tr),eh=sn(_s),th=sn(Cs),nh=sn(xs),ar=re;(Ss&&ar(new Ss(new ArrayBuffer(1)))!=Ju||tr&&ar(new tr)!=Gu||_s&&ar(_s.resolve())!=Yu||Cs&&ar(new Cs)!=Xu||xs&&ar(new xs)!=Zu)&&(ar=function(s){var l=re(s),u=l=="[object Object]"?s.constructor:void 0,m=u?sn(u):"";if(m)switch(m){case Zp:return Ju;case Jp:return Gu;case eh:return Yu;case th:return Xu;case nh:return Zu}return l});const ec=ar;var tc="[object Arguments]",nc="[object Array]",zi="[object Object]",rc=Object.prototype.hasOwnProperty;const rh=function(s,l,u,m,P,b){var A=ft(s),D=ft(l),H=A?nc:ec(s),Z=D?nc:ec(l),te=(H=H==tc?zi:H)==zi,X=(Z=Z==tc?zi:Z)==zi,ie=H==Z;if(ie&&go(s)){if(!go(l))return!1;A=!0,te=!1}if(ie&&!te)return b||(b=new lr),A||yo(s)?Ye(s,l,u,m,P,b):Qu(s,l,H,u,m,P,b);if(!(1&u)){var me=te&&rc.call(s,"__wrapped__"),ue=X&&rc.call(l,"__wrapped__");if(me||ue){var ke=me?s.value():s,xe=ue?l.value():l;return b||(b=new lr),P(ke,xe,u,m,b)}}return!!ie&&(b||(b=new lr),Xp(s,l,u,m,P,b))},oc=function s(l,u,m,P,b){return l===u||(l==null||u==null||!_n(l)&&!_n(u)?l!=l&&u!=u:rh(l,u,m,P,s,b))},oh=function(s,l,u,m){var P=u.length,b=P,A=!m;if(s==null)return!b;for(s=Object(s);P--;){var D=u[P];if(A&&D[2]?D[1]!==s[D[0]]:!(D[0]in s))return!1}for(;++P<b;){var H=(D=u[P])[0],Z=s[H],te=D[1];if(A&&D[2]){if(Z===void 0&&!(H in s))return!1}else{var X=new lr;if(m)var ie=m(Z,te,H,s,l,X);if(!(ie===void 0?oc(te,Z,3,m,X):ie))return!1}}return!0},ic=function(s){return s==s&&!fe(s)},ih=function(s){for(var l=Nn(s),u=l.length;u--;){var m=l[u],P=s[m];l[u]=[m,P,ic(P)]}return l},lc=function(s,l){return function(u){return u!=null&&u[s]===l&&(l!==void 0||s in Object(u))}},lh=function(s){var l=ih(s);return l.length==1&&l[0][2]?lc(l[0][0],l[0][1]):function(u){return u===s||oh(u,s,l)}},Es=function(s){return typeof s=="symbol"||_n(s)&&re(s)=="[object Symbol]"};var sh=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,ah=/^\w*$/;const Ts=function(s,l){if(ft(s))return!1;var u=typeof s;return!(u!="number"&&u!="symbol"&&u!="boolean"&&s!=null&&!Es(s))||ah.test(s)||!sh.test(s)||l!=null&&s in Object(l)};function Ns(s,l){if(typeof s!="function"||l!=null&&typeof l!="function")throw new TypeError("Expected a function");var u=function(){var m=arguments,P=l?l.apply(this,m):m[0],b=u.cache;if(b.has(P))return b.get(P);var A=s.apply(this,m);return u.cache=b.set(P,A)||b,A};return u.cache=new(Ns.Cache||ir),u}Ns.Cache=ir;const uh=Ns;var ch=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,fh=/\\(\\)?/g;const dh=function(s){var l=uh(s,function(m){return u.size===500&&u.clear(),m}),u=l.cache;return l}(function(s){var l=[];return s.charCodeAt(0)===46&&l.push(""),s.replace(ch,function(u,m,P,b){l.push(P?b.replace(fh,"$1"):m||u)}),l}),ph=function(s,l){for(var u=-1,m=s==null?0:s.length,P=Array(m);++u<m;)P[u]=l(s[u],u,s);return P};var sc=Gt?Gt.prototype:void 0,ac=sc?sc.toString:void 0;const hh=function s(l){if(typeof l=="string")return l;if(ft(l))return ph(l,s)+"";if(Es(l))return ac?ac.call(l):"";var u=l+"";return u=="0"&&1/l==-1/0?"-0":u},mh=function(s){return s==null?"":hh(s)},uc=function(s,l){return ft(s)?s:Ts(s,l)?[s]:dh(mh(s))},Di=function(s){if(typeof s=="string"||Es(s))return s;var l=s+"";return l=="0"&&1/s==-1/0?"-0":l},cc=function(s,l){for(var u=0,m=(l=uc(l,s)).length;s!=null&&u<m;)s=s[Di(l[u++])];return u&&u==m?s:void 0},gh=function(s,l,u){var m=s==null?void 0:cc(s,l);return m===void 0?u:m},yh=function(s,l){return s!=null&&l in Object(s)},vh=function(s,l,u){for(var m=-1,P=(l=uc(l,s)).length,b=!1;++m<P;){var A=Di(l[m]);if(!(b=s!=null&&u(s,A)))break;s=s[A]}return b||++m!=P?b:!!(P=s==null?0:s.length)&&Pr(P)&&_i(A,P)&&(ft(s)||wt(s))},wh=function(s,l){return s!=null&&vh(s,l,yh)},kh=function(s,l){return Ts(s)&&ic(l)?lc(Di(s),l):function(u){var m=gh(u,s);return m===void 0&&m===l?wh(u,s):oc(l,m,3)}},Sh=function(s){return s},_h=function(s){return function(l){return l?.[s]}},Ch=function(s){return function(l){return cc(l,s)}},xh=function(s){return Ts(s)?_h(Di(s)):Ch(s)},Eh=function(s){return typeof s=="function"?s:s==null?Sh:typeof s=="object"?ft(s)?kh(s[0],s[1]):lh(s):xh(s)},Th=function(s,l){var u={};return l=Eh(l),Ei(s,function(m,P,b){vi(u,l(m,P,b),m)}),u};function fc(s,l,u){return l in s?Object.defineProperty(s,l,{value:u,enumerable:!0,configurable:!0,writable:!0}):s[l]=u,s}class Nh{constructor(l,u){fc(this,"ctx",void 0),fc(this,"serviceHooks",void 0),this.ctx=l,this.serviceHooks=u,l._execCallableAPI("register-search-service",l.baseInfo.id,u.name,u.options),Object.entries({query:{f:"onQuery",args:["graph","q",!0],reply:!0,transformOutput:m=>(ft(m?.blocks)&&(m.blocks=m.blocks.map(P=>P&&Th(P,(b,A)=>`block/${A}`))),m)},rebuildBlocksIndice:{f:"onIndiceInit",args:["graph","blocks"]},transactBlocks:{f:"onBlocksChanged",args:["graph","data"]},truncateBlocks:{f:"onIndiceReset",args:["graph"]},removeDb:{f:"onGraph",args:["graph"]}}).forEach(([m,P])=>{const b=(A=>`service:search:${A}:${u.name}`)(m);l.caller.on(b,async A=>{if(De(u?.[P.f])){let D=null;try{D=await u[P.f].apply(u,(P.args||[]).map(H=>{if(A){if(H===!0)return A;if(A.hasOwnProperty(H)){const Z=A[H];return delete A[H],Z}}})),P.transformOutput&&(D=P.transformOutput(D))}catch(H){console.error("[SearchService] ",H),D=H}finally{P.reply&&l.caller.call(`${b}:reply`,D)}}})})}}function $t(s,l,u){return l in s?Object.defineProperty(s,l,{value:u,enumerable:!0,configurable:!0,writable:!0}):s[l]=u,s}const Ph=Symbol.for("proxy-continue"),Oh=M()("LSPlugin:user"),dc=new C("",{console:!0});function jr(s,l,u){var m;const{key:P,label:b,desc:A,palette:D,keybinding:H,extras:Z}=l;if(typeof u!="function")return this.logger.error(`${P||b}: command action should be function.`),!1;const te=function(ie){if(typeof ie=="string")return ie.trim().replace(/\s/g,"_").toLowerCase()}(P);if(!te)return this.logger.error(`${b}: command key is required.`),!1;const X=`SimpleCommandHook${te}${++mc}`;this.Editor["on"+X](u),(m=this.caller)===null||m===void 0||m.call("api:call",{method:"register-plugin-simple-command",args:[this.baseInfo.id,[{key:te,label:b,type:s,desc:A,keybinding:H,extras:Z},["editor/hook",X]],D]})}function pc(s){return!(typeof(l=s)!="string"||l.length!==36||!/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/gi.test(l))||(dc.error(`#${s} is not a valid UUID string.`),!1);var l}let Fi=null,hc=new Map;const Ih={async getInfo(s){return Fi||(Fi=await this._execCallableAPIAsync("get-app-info")),typeof s=="string"?Fi[s]:Fi},registerCommand:jr,registerSearchService(s){if(hc.has(s.name))throw new Error(`SearchService: #${s.name} has registered!`);hc.set(s.name,new Nh(this,s))},registerCommandPalette(s,l){const{key:u,label:m,keybinding:P}=s;return jr.call(this,"$palette$",{key:u,label:m,palette:!0,keybinding:P},l)},registerCommandShortcut(s,l,u={}){typeof s=="string"&&(s={mode:"global",binding:s});const{binding:m}=s,P="$shortcut$",b=u.key||P+g(m?.toString());return jr.call(this,P,{...u,key:b,palette:!1,keybinding:s},l)},registerUIItem(s,l){var u;const m=this.baseInfo.id;(u=this.caller)===null||u===void 0||u.call("api:call",{method:"register-plugin-ui-item",args:[m,s,l]})},registerPageMenuItem(s,l){if(typeof l!="function")return!1;const u=s+"_"+this.baseInfo.id,m=s;jr.call(this,"page-menu-item",{key:u,label:m},l)},onBlockRendererSlotted(s,l){if(!pc(s))return;const u=this.baseInfo.id,m=`hook:editor:${g(`slot:${s}`)}`;return this.caller.on(m,l),this.App._installPluginHook(u,m),()=>{this.caller.off(m,l),this.App._uninstallPluginHook(u,m)}},invokeExternalPlugin(s,...l){var u;if(!(s=(u=s)===null||u===void 0?void 0:u.trim()))return;let[m,P]=s.split(".");if(!["models","commands"].includes(P?.toLowerCase()))throw new Error("Type only support '.models' or '.commands' currently.");const b=s.replace(`${m}.${P}.`,"");if(!m||!P||!b)throw new Error(`Illegal type of #${s} to invoke external plugin.`);return this._execCallableAPIAsync("invoke_external_plugin_cmd",m,P.toLowerCase(),b,l)},setFullScreen(s){const l=(...u)=>this._callWin("setFullScreen",...u);s==="toggle"?this._callWin("isFullScreen").then(u=>{u?l():l(!0)}):s?l(!0):l()}};let mc=0;const Lh={newBlockUUID(){return this._execCallableAPIAsync("new_block_uuid")},registerSlashCommand(s,l){var u;Oh("Register slash command #",this.baseInfo.id,s,l),typeof l=="function"&&(l=[["editor/clear-current-slash",!1],["editor/restore-saved-cursor"],["editor/hook",l]]),l=l.map(m=>{const[P,...b]=m;if(P==="editor/hook"){let A=b[0],D=()=>{var Z;(Z=this.caller)===null||Z===void 0||Z.callUserModel(A)};typeof A=="function"&&(D=A);const H=`SlashCommandHook${P}${++mc}`;m[1]=H,this.Editor["on"+H](D)}return m}),(u=this.caller)===null||u===void 0||u.call("api:call",{method:"register-plugin-slash-command",args:[this.baseInfo.id,[s,l]]})},registerBlockContextMenuItem(s,l){if(typeof l!="function")return!1;const u=s+"_"+this.baseInfo.id;jr.call(this,"block-context-menu-item",{key:u,label:s},l)},registerHighlightContextMenuItem(s,l,u){if(typeof l!="function")return!1;const m=s+"_"+this.baseInfo.id;jr.call(this,"highlight-context-menu-item",{key:m,label:s,extras:u},l)},scrollToBlockInPage(s,l,u){const m="block-content-"+l;u!=null&&u.replaceState?this.App.replaceState("page",{name:s},{anchor:m}):this.App.pushState("page",{name:s},{anchor:m})}},bh={onBlockChanged(s,l){if(!pc(s))return;const u=this.baseInfo.id,m=`hook:db:${g(`block:${s}`)}`,P=({block:b,txData:A,txMeta:D})=>{b.uuid===s&&l(b,A,D)};return this.caller.on(m,P),this.App._installPluginHook(u,m),()=>{this.caller.off(m,P),this.App._uninstallPluginHook(u,m)}},datascriptQuery(s,...l){return l.pop(),l!=null&&l.some(u=>typeof u=="function")?this.Experiments.ensureHostScope().logseq.api.datascript_query(s,...l):this._execCallableAPIAsync("datascript_query",s,...l)}},jh={},Mh={},Ah={makeSandboxStorage(){return new pe(this,{assets:!0})}};class Ps extends L(){constructor(l,u){super(),$t(this,"_baseInfo",void 0),$t(this,"_caller",void 0),$t(this,"_version","0.0.17"),$t(this,"_debugTag",""),$t(this,"_settingsSchema",void 0),$t(this,"_connected",!1),$t(this,"_ui",new Map),$t(this,"_mFileStorage",void 0),$t(this,"_mRequest",void 0),$t(this,"_mExperiments",void 0),$t(this,"_beforeunloadCallback",void 0),this._baseInfo=l,this._caller=u,u.on("sys:ui:visible",m=>{m!=null&&m.toggle&&this.toggleMainUI()}),u.on("settings:changed",m=>{const P=Object.assign({},this.settings),b=Object.assign(this._baseInfo.settings,m);this.emit("settings:changed",{...b},P)}),u.on("beforeunload",async m=>{const{actor:P,...b}=m,A=this._beforeunloadCallback;try{A&&await A(b),P?.resolve(null)}catch(D){this.logger.error("[beforeunload] ",D),P?.reject(D)}})}async ready(l,u){var m,P;if(!this._connected)try{var b;typeof l=="function"&&(u=l,l={});let A=await this._caller.connectToParent(l);this._connected=!0,m=this._baseInfo,P=A,A=f()(m,P,{arrayMerge:(D,H)=>H}),this._baseInfo=A,(b=A)!==null&&b!==void 0&&b.id&&(this._debugTag=this._caller.debugTag=`#${A.id} [${A.name}]`,this.logger.setTag(this._debugTag)),this._settingsSchema&&(A.settings=function(D,H){const Z=(H||[]).reduce((te,X)=>("default"in X&&(te[X.key]=X.default),te),{});return Object.assign(Z,D)}(A.settings,this._settingsSchema),await this.useSettingsSchema(this._settingsSchema));try{await this._execCallableAPIAsync("setSDKMetadata",{version:this._version})}catch(D){console.warn(D)}u&&u.call(this,A)}catch(A){console.error(`${this._debugTag} [Ready Error]`,A)}}ensureConnected(){if(!this._connected)throw new Error("not connected")}beforeunload(l){typeof l=="function"&&(this._beforeunloadCallback=l)}provideModel(l){return this.caller._extendUserModel(l),this}provideTheme(l){return this.caller.call("provider:theme",l),this}provideStyle(l){return this.caller.call("provider:style",l),this}provideUI(l){return this.caller.call("provider:ui",l),this}useSettingsSchema(l){return this.connected&&this.caller.call("settings:schema",{schema:l,isSync:!0}),this._settingsSchema=l,this}updateSettings(l){this.caller.call("settings:update",l)}onSettingsChanged(l){const u="settings:changed";return this.on(u,l),()=>this.off(u,l)}showSettingsUI(){this.caller.call("settings:visible:changed",{visible:!0})}hideSettingsUI(){this.caller.call("settings:visible:changed",{visible:!1})}setMainUIAttrs(l){this.caller.call("main-ui:attrs",l)}setMainUIInlineStyle(l){this.caller.call("main-ui:style",l)}hideMainUI(l){const u={key:0,visible:!1,cursor:l?.restoreEditingCursor};this.caller.call("main-ui:visible",u),this.emit("ui:visible:changed",u),this._ui.set(u.key,u)}showMainUI(l){const u={key:0,visible:!0,autoFocus:l?.autoFocus};this.caller.call("main-ui:visible",u),this.emit("ui:visible:changed",u),this._ui.set(u.key,u)}toggleMainUI(){const u=this._ui.get(0);u&&u.visible?this.hideMainUI():this.showMainUI()}get version(){return this._version}get isMainUIVisible(){const l=this._ui.get(0);return!!(l&&l.visible)}get connected(){return this._connected}get baseInfo(){return this._baseInfo}get effect(){return(l=this)&&(((u=l.baseInfo)===null||u===void 0?void 0:u.effect)||!((m=l.baseInfo)!==null&&m!==void 0&&m.iir));var l,u,m}get logger(){return dc}get settings(){var l;return(l=this.baseInfo)===null||l===void 0?void 0:l.settings}get caller(){return this._caller}resolveResourceFullUrl(l){if(this.ensureConnected(),l)return l=l.replace(/^[.\\/]+/,""),I(this._baseInfo.lsr,l)}_makeUserProxy(l,u){const m=this,P=this.caller;return new Proxy(l,{get(b,A,D){const H=b[A];return function(...Z){if(H){const X=H.apply(m,Z.concat(u));if(X!==Ph)return X}if(u){const X=A.toString().match(/^(once|off|on)/i);if(X!=null){const ie=X[0].toLowerCase(),me=X.input,ue=ie==="off",ke=m.baseInfo.id;let xe=me.slice(ie.length),We=Z[0],Tt=Z[1];typeof We=="string"&&typeof Tt=="function"&&(We=We.replace(/^logseq./,":"),xe=`${xe}${We}`,We=Tt,Tt=Z[2]),xe=`hook:${u}:${g(xe)}`,P[ie](xe,We);const ur=()=>{P.off(xe,We),P.listenerCount(xe)||m.App._uninstallPluginHook(ke,xe)};return ue?void ur():(m.App._installPluginHook(ke,xe,Tt),ur)}}let te=A;return["git","ui","assets"].includes(u)&&(te=u+"_"+te),P.callAsync("api:call",{tag:u,method:te,args:Z})}}})}_execCallableAPIAsync(l,...u){return this._caller.callAsync("api:call",{method:l,args:u})}_execCallableAPI(l,...u){this._caller.call("api:call",{method:l,args:u})}_callWin(...l){return this._execCallableAPIAsync("_callMainWin",...l)}get App(){return this._makeUserProxy(Ih,"app")}get Editor(){return this._makeUserProxy(Lh,"editor")}get DB(){return this._makeUserProxy(bh,"db")}get Git(){return this._makeUserProxy(jh,"git")}get UI(){return this._makeUserProxy(Mh,"ui")}get Assets(){return this._makeUserProxy(Ah,"assets")}get FileStorage(){let l=this._mFileStorage;return l||(l=this._mFileStorage=new pe(this)),l}get Request(){let l=this._mRequest;return l||(l=this._mRequest=new vt(this)),l}get Experiments(){let l=this._mExperiments;return l||(l=this._mExperiments=new Ue(this)),l}}function gc(s,l){return new Ps(s,l)}if(window.__LSP__HOST__==null){const s=new oe(null);window.logseq=gc({},s)}})(),i})())})(gl,gl.exports);gl.exports;var Uf={exports:{}},xt={},$f={exports:{}},Bf={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(q,O){var z=q.length;q.push(O);e:for(;0<z;){var B=z-1>>>1,ne=q[B];if(0<o(ne,O))q[B]=O,q[z]=ne,z=B;else break e}}function n(q){return q.length===0?null:q[0]}function r(q){if(q.length===0)return null;var O=q[0],z=q.pop();if(z!==O){q[0]=z;e:for(var B=0,ne=q.length,Ce=ne>>>1;B<Ce;){var V=2*(B+1)-1,se=q[V],ce=V+1,ze=q[ce];if(0>o(se,z))ce<ne&&0>o(ze,se)?(q[B]=ze,q[ce]=z,B=ce):(q[B]=se,q[V]=z,B=V);else if(ce<ne&&0>o(ze,z))q[B]=ze,q[ce]=z,B=ce;else break e}}return O}function o(q,O){var z=q.sortIndex-O.sortIndex;return z!==0?z:q.id-O.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var a=Date,c=a.now();e.unstable_now=function(){return a.now()-c}}var f=[],p=[],w=1,_=null,v=3,E=!1,k=!1,N=!1,L=typeof setTimeout=="function"?setTimeout:null,h=typeof clearTimeout=="function"?clearTimeout:null,d=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function g(q){for(var O=n(p);O!==null;){if(O.callback===null)r(p);else if(O.startTime<=q)r(p),O.sortIndex=O.expirationTime,t(f,O);else break;O=n(p)}}function C(q){if(N=!1,g(q),!k)if(n(f)!==null)k=!0,Ae(I);else{var O=n(p);O!==null&&Pe(C,O.startTime-q)}}function I(q,O){k=!1,N&&(N=!1,h(x),x=-1),E=!0;var z=v;try{for(g(O),_=n(f);_!==null&&(!(_.expirationTime>O)||q&&!R());){var B=_.callback;if(typeof B=="function"){_.callback=null,v=_.priorityLevel;var ne=B(_.expirationTime<=O);O=e.unstable_now(),typeof ne=="function"?_.callback=ne:_===n(f)&&r(f),g(O)}else r(f);_=n(f)}if(_!==null)var Ce=!0;else{var V=n(p);V!==null&&Pe(C,V.startTime-O),Ce=!1}return Ce}finally{_=null,v=z,E=!1}}var j=!1,y=null,x=-1,M=5,T=-1;function R(){return!(e.unstable_now()-T<M)}function ee(){if(y!==null){var q=e.unstable_now();T=q;var O=!0;try{O=y(!0,q)}finally{O?le():(j=!1,y=null)}}else j=!1}var le;if(typeof d=="function")le=function(){d(ee)};else if(typeof MessageChannel<"u"){var J=new MessageChannel,_e=J.port2;J.port1.onmessage=ee,le=function(){_e.postMessage(null)}}else le=function(){L(ee,0)};function Ae(q){y=q,j||(j=!0,le())}function Pe(q,O){x=L(function(){q(e.unstable_now())},O)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(q){q.callback=null},e.unstable_continueExecution=function(){k||E||(k=!0,Ae(I))},e.unstable_forceFrameRate=function(q){0>q||125<q?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):M=0<q?Math.floor(1e3/q):5},e.unstable_getCurrentPriorityLevel=function(){return v},e.unstable_getFirstCallbackNode=function(){return n(f)},e.unstable_next=function(q){switch(v){case 1:case 2:case 3:var O=3;break;default:O=v}var z=v;v=O;try{return q()}finally{v=z}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(q,O){switch(q){case 1:case 2:case 3:case 4:case 5:break;default:q=3}var z=v;v=q;try{return O()}finally{v=z}},e.unstable_scheduleCallback=function(q,O,z){var B=e.unstable_now();switch(typeof z=="object"&&z!==null?(z=z.delay,z=typeof z=="number"&&0<z?B+z:B):z=B,q){case 1:var ne=-1;break;case 2:ne=250;break;case 5:ne=**********;break;case 4:ne=1e4;break;default:ne=5e3}return ne=z+ne,q={id:w++,callback:O,priorityLevel:q,startTime:z,expirationTime:ne,sortIndex:-1},z>B?(q.sortIndex=z,t(p,q),n(f)===null&&q===n(p)&&(N?(h(x),x=-1):N=!0,Pe(C,z-B))):(q.sortIndex=ne,t(f,q),k||E||(k=!0,Ae(I))),q},e.unstable_shouldYield=R,e.unstable_wrapCallback=function(q){var O=v;return function(){var z=v;v=O;try{return q.apply(this,arguments)}finally{v=z}}}})(Bf);$f.exports=Bf;var im=$f.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var lm=ae,Ct=im;function F(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Hf=new Set,qo={};function _r(e,t){to(e,t),to(e+"Capture",t)}function to(e,t){for(qo[e]=t,e=0;e<t.length;e++)Hf.add(t[e])}var gn=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),ra=Object.prototype.hasOwnProperty,sm=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,kc={},Sc={};function am(e){return ra.call(Sc,e)?!0:ra.call(kc,e)?!1:sm.test(e)?Sc[e]=!0:(kc[e]=!0,!1)}function um(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function cm(e,t,n,r){if(t===null||typeof t>"u"||um(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function ct(e,t,n,r,o,i,a){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=a}var tt={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){tt[e]=new ct(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];tt[t]=new ct(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){tt[e]=new ct(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){tt[e]=new ct(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){tt[e]=new ct(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){tt[e]=new ct(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){tt[e]=new ct(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){tt[e]=new ct(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){tt[e]=new ct(e,5,!1,e.toLowerCase(),null,!1,!1)});var tu=/[\-:]([a-z])/g;function nu(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(tu,nu);tt[t]=new ct(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(tu,nu);tt[t]=new ct(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(tu,nu);tt[t]=new ct(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){tt[e]=new ct(e,1,!1,e.toLowerCase(),null,!1,!1)});tt.xlinkHref=new ct("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){tt[e]=new ct(e,1,!1,e.toLowerCase(),null,!0,!0)});function ru(e,t,n,r){var o=tt.hasOwnProperty(t)?tt[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(cm(t,n,o,r)&&(n=null),r||o===null?am(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var kn=lm.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,$i=Symbol.for("react.element"),Ar=Symbol.for("react.portal"),zr=Symbol.for("react.fragment"),ou=Symbol.for("react.strict_mode"),oa=Symbol.for("react.profiler"),Wf=Symbol.for("react.provider"),Vf=Symbol.for("react.context"),iu=Symbol.for("react.forward_ref"),ia=Symbol.for("react.suspense"),la=Symbol.for("react.suspense_list"),lu=Symbol.for("react.memo"),jn=Symbol.for("react.lazy"),Qf=Symbol.for("react.offscreen"),_c=Symbol.iterator;function To(e){return e===null||typeof e!="object"?null:(e=_c&&e[_c]||e["@@iterator"],typeof e=="function"?e:null)}var Me=Object.assign,Is;function Mo(e){if(Is===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Is=t&&t[1]||""}return`
`+Is+e}var Ls=!1;function bs(e,t){if(!e||Ls)return"";Ls=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(p){var r=p}Reflect.construct(e,[],t)}else{try{t.call()}catch(p){r=p}e.call(t.prototype)}else{try{throw Error()}catch(p){r=p}e()}}catch(p){if(p&&r&&typeof p.stack=="string"){for(var o=p.stack.split(`
`),i=r.stack.split(`
`),a=o.length-1,c=i.length-1;1<=a&&0<=c&&o[a]!==i[c];)c--;for(;1<=a&&0<=c;a--,c--)if(o[a]!==i[c]){if(a!==1||c!==1)do if(a--,c--,0>c||o[a]!==i[c]){var f=`
`+o[a].replace(" at new "," at ");return e.displayName&&f.includes("<anonymous>")&&(f=f.replace("<anonymous>",e.displayName)),f}while(1<=a&&0<=c);break}}}finally{Ls=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Mo(e):""}function fm(e){switch(e.tag){case 5:return Mo(e.type);case 16:return Mo("Lazy");case 13:return Mo("Suspense");case 19:return Mo("SuspenseList");case 0:case 2:case 15:return e=bs(e.type,!1),e;case 11:return e=bs(e.type.render,!1),e;case 1:return e=bs(e.type,!0),e;default:return""}}function sa(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case zr:return"Fragment";case Ar:return"Portal";case oa:return"Profiler";case ou:return"StrictMode";case ia:return"Suspense";case la:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Vf:return(e.displayName||"Context")+".Consumer";case Wf:return(e._context.displayName||"Context")+".Provider";case iu:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case lu:return t=e.displayName||null,t!==null?t:sa(e.type)||"Memo";case jn:t=e._payload,e=e._init;try{return sa(e(t))}catch{}}return null}function dm(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return sa(t);case 8:return t===ou?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function qn(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function qf(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function pm(e){var t=qf(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(a){r=""+a,i.call(this,a)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(a){r=""+a},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Bi(e){e._valueTracker||(e._valueTracker=pm(e))}function Kf(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=qf(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function yl(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function aa(e,t){var n=t.checked;return Me({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Cc(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=qn(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Gf(e,t){t=t.checked,t!=null&&ru(e,"checked",t,!1)}function ua(e,t){Gf(e,t);var n=qn(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?ca(e,t.type,n):t.hasOwnProperty("defaultValue")&&ca(e,t.type,qn(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function xc(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function ca(e,t,n){(t!=="number"||yl(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Ao=Array.isArray;function Gr(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+qn(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function fa(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(F(91));return Me({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Ec(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(F(92));if(Ao(n)){if(1<n.length)throw Error(F(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:qn(n)}}function Yf(e,t){var n=qn(t.value),r=qn(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Tc(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Xf(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function da(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Xf(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Hi,Zf=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Hi=Hi||document.createElement("div"),Hi.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Hi.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Ko(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Fo={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},hm=["Webkit","ms","Moz","O"];Object.keys(Fo).forEach(function(e){hm.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Fo[t]=Fo[e]})});function Jf(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Fo.hasOwnProperty(e)&&Fo[e]?(""+t).trim():t+"px"}function ed(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=Jf(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var mm=Me({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function pa(e,t){if(t){if(mm[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(F(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(F(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(F(61))}if(t.style!=null&&typeof t.style!="object")throw Error(F(62))}}function ha(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var ma=null;function su(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var ga=null,Yr=null,Xr=null;function Nc(e){if(e=pi(e)){if(typeof ga!="function")throw Error(F(280));var t=e.stateNode;t&&(t=ql(t),ga(e.stateNode,e.type,t))}}function td(e){Yr?Xr?Xr.push(e):Xr=[e]:Yr=e}function nd(){if(Yr){var e=Yr,t=Xr;if(Xr=Yr=null,Nc(e),t)for(e=0;e<t.length;e++)Nc(t[e])}}function rd(e,t){return e(t)}function od(){}var js=!1;function id(e,t,n){if(js)return e(t,n);js=!0;try{return rd(e,t,n)}finally{js=!1,(Yr!==null||Xr!==null)&&(od(),nd())}}function Go(e,t){var n=e.stateNode;if(n===null)return null;var r=ql(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(F(231,t,typeof n));return n}var ya=!1;if(gn)try{var No={};Object.defineProperty(No,"passive",{get:function(){ya=!0}}),window.addEventListener("test",No,No),window.removeEventListener("test",No,No)}catch{ya=!1}function gm(e,t,n,r,o,i,a,c,f){var p=Array.prototype.slice.call(arguments,3);try{t.apply(n,p)}catch(w){this.onError(w)}}var Ro=!1,vl=null,wl=!1,va=null,ym={onError:function(e){Ro=!0,vl=e}};function vm(e,t,n,r,o,i,a,c,f){Ro=!1,vl=null,gm.apply(ym,arguments)}function wm(e,t,n,r,o,i,a,c,f){if(vm.apply(this,arguments),Ro){if(Ro){var p=vl;Ro=!1,vl=null}else throw Error(F(198));wl||(wl=!0,va=p)}}function Cr(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function ld(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Pc(e){if(Cr(e)!==e)throw Error(F(188))}function km(e){var t=e.alternate;if(!t){if(t=Cr(e),t===null)throw Error(F(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var i=o.alternate;if(i===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return Pc(o),e;if(i===r)return Pc(o),t;i=i.sibling}throw Error(F(188))}if(n.return!==r.return)n=o,r=i;else{for(var a=!1,c=o.child;c;){if(c===n){a=!0,n=o,r=i;break}if(c===r){a=!0,r=o,n=i;break}c=c.sibling}if(!a){for(c=i.child;c;){if(c===n){a=!0,n=i,r=o;break}if(c===r){a=!0,r=i,n=o;break}c=c.sibling}if(!a)throw Error(F(189))}}if(n.alternate!==r)throw Error(F(190))}if(n.tag!==3)throw Error(F(188));return n.stateNode.current===n?e:t}function sd(e){return e=km(e),e!==null?ad(e):null}function ad(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=ad(e);if(t!==null)return t;e=e.sibling}return null}var ud=Ct.unstable_scheduleCallback,Oc=Ct.unstable_cancelCallback,Sm=Ct.unstable_shouldYield,_m=Ct.unstable_requestPaint,Re=Ct.unstable_now,Cm=Ct.unstable_getCurrentPriorityLevel,au=Ct.unstable_ImmediatePriority,cd=Ct.unstable_UserBlockingPriority,kl=Ct.unstable_NormalPriority,xm=Ct.unstable_LowPriority,fd=Ct.unstable_IdlePriority,Hl=null,rn=null;function Em(e){if(rn&&typeof rn.onCommitFiberRoot=="function")try{rn.onCommitFiberRoot(Hl,e,void 0,(e.current.flags&128)===128)}catch{}}var Qt=Math.clz32?Math.clz32:Pm,Tm=Math.log,Nm=Math.LN2;function Pm(e){return e>>>=0,e===0?32:31-(Tm(e)/Nm|0)|0}var Wi=64,Vi=4194304;function zo(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Sl(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,i=e.pingedLanes,a=n&268435455;if(a!==0){var c=a&~o;c!==0?r=zo(c):(i&=a,i!==0&&(r=zo(i)))}else a=n&~o,a!==0?r=zo(a):i!==0&&(r=zo(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,i=t&-t,o>=i||o===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Qt(t),o=1<<n,r|=e[n],t&=~o;return r}function Om(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Im(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,i=e.pendingLanes;0<i;){var a=31-Qt(i),c=1<<a,f=o[a];f===-1?(!(c&n)||c&r)&&(o[a]=Om(c,t)):f<=t&&(e.expiredLanes|=c),i&=~c}}function wa(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function dd(){var e=Wi;return Wi<<=1,!(Wi&4194240)&&(Wi=64),e}function Ms(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function fi(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Qt(t),e[t]=n}function Lm(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-Qt(n),i=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~i}}function uu(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Qt(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var Se=0;function pd(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var hd,cu,md,gd,yd,ka=!1,Qi=[],Rn=null,Un=null,$n=null,Yo=new Map,Xo=new Map,An=[],bm="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Ic(e,t){switch(e){case"focusin":case"focusout":Rn=null;break;case"dragenter":case"dragleave":Un=null;break;case"mouseover":case"mouseout":$n=null;break;case"pointerover":case"pointerout":Yo.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Xo.delete(t.pointerId)}}function Po(e,t,n,r,o,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[o]},t!==null&&(t=pi(t),t!==null&&cu(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function jm(e,t,n,r,o){switch(t){case"focusin":return Rn=Po(Rn,e,t,n,r,o),!0;case"dragenter":return Un=Po(Un,e,t,n,r,o),!0;case"mouseover":return $n=Po($n,e,t,n,r,o),!0;case"pointerover":var i=o.pointerId;return Yo.set(i,Po(Yo.get(i)||null,e,t,n,r,o)),!0;case"gotpointercapture":return i=o.pointerId,Xo.set(i,Po(Xo.get(i)||null,e,t,n,r,o)),!0}return!1}function vd(e){var t=dr(e.target);if(t!==null){var n=Cr(t);if(n!==null){if(t=n.tag,t===13){if(t=ld(n),t!==null){e.blockedOn=t,yd(e.priority,function(){md(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function ll(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Sa(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);ma=r,n.target.dispatchEvent(r),ma=null}else return t=pi(n),t!==null&&cu(t),e.blockedOn=n,!1;t.shift()}return!0}function Lc(e,t,n){ll(e)&&n.delete(t)}function Mm(){ka=!1,Rn!==null&&ll(Rn)&&(Rn=null),Un!==null&&ll(Un)&&(Un=null),$n!==null&&ll($n)&&($n=null),Yo.forEach(Lc),Xo.forEach(Lc)}function Oo(e,t){e.blockedOn===t&&(e.blockedOn=null,ka||(ka=!0,Ct.unstable_scheduleCallback(Ct.unstable_NormalPriority,Mm)))}function Zo(e){function t(o){return Oo(o,e)}if(0<Qi.length){Oo(Qi[0],e);for(var n=1;n<Qi.length;n++){var r=Qi[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Rn!==null&&Oo(Rn,e),Un!==null&&Oo(Un,e),$n!==null&&Oo($n,e),Yo.forEach(t),Xo.forEach(t),n=0;n<An.length;n++)r=An[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<An.length&&(n=An[0],n.blockedOn===null);)vd(n),n.blockedOn===null&&An.shift()}var Zr=kn.ReactCurrentBatchConfig,_l=!0;function Am(e,t,n,r){var o=Se,i=Zr.transition;Zr.transition=null;try{Se=1,fu(e,t,n,r)}finally{Se=o,Zr.transition=i}}function zm(e,t,n,r){var o=Se,i=Zr.transition;Zr.transition=null;try{Se=4,fu(e,t,n,r)}finally{Se=o,Zr.transition=i}}function fu(e,t,n,r){if(_l){var o=Sa(e,t,n,r);if(o===null)Ws(e,t,r,Cl,n),Ic(e,r);else if(jm(o,e,t,n,r))r.stopPropagation();else if(Ic(e,r),t&4&&-1<bm.indexOf(e)){for(;o!==null;){var i=pi(o);if(i!==null&&hd(i),i=Sa(e,t,n,r),i===null&&Ws(e,t,r,Cl,n),i===o)break;o=i}o!==null&&r.stopPropagation()}else Ws(e,t,r,null,n)}}var Cl=null;function Sa(e,t,n,r){if(Cl=null,e=su(r),e=dr(e),e!==null)if(t=Cr(e),t===null)e=null;else if(n=t.tag,n===13){if(e=ld(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Cl=e,null}function wd(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Cm()){case au:return 1;case cd:return 4;case kl:case xm:return 16;case fd:return 536870912;default:return 16}default:return 16}}var Dn=null,du=null,sl=null;function kd(){if(sl)return sl;var e,t=du,n=t.length,r,o="value"in Dn?Dn.value:Dn.textContent,i=o.length;for(e=0;e<n&&t[e]===o[e];e++);var a=n-e;for(r=1;r<=a&&t[n-r]===o[i-r];r++);return sl=o.slice(e,1<r?1-r:void 0)}function al(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function qi(){return!0}function bc(){return!1}function Et(e){function t(n,r,o,i,a){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=i,this.target=a,this.currentTarget=null;for(var c in e)e.hasOwnProperty(c)&&(n=e[c],this[c]=n?n(i):i[c]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?qi:bc,this.isPropagationStopped=bc,this}return Me(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=qi)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=qi)},persist:function(){},isPersistent:qi}),t}var uo={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},pu=Et(uo),di=Me({},uo,{view:0,detail:0}),Dm=Et(di),As,zs,Io,Wl=Me({},di,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:hu,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Io&&(Io&&e.type==="mousemove"?(As=e.screenX-Io.screenX,zs=e.screenY-Io.screenY):zs=As=0,Io=e),As)},movementY:function(e){return"movementY"in e?e.movementY:zs}}),jc=Et(Wl),Fm=Me({},Wl,{dataTransfer:0}),Rm=Et(Fm),Um=Me({},di,{relatedTarget:0}),Ds=Et(Um),$m=Me({},uo,{animationName:0,elapsedTime:0,pseudoElement:0}),Bm=Et($m),Hm=Me({},uo,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Wm=Et(Hm),Vm=Me({},uo,{data:0}),Mc=Et(Vm),Qm={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},qm={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Km={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Gm(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Km[e])?!!t[e]:!1}function hu(){return Gm}var Ym=Me({},di,{key:function(e){if(e.key){var t=Qm[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=al(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?qm[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:hu,charCode:function(e){return e.type==="keypress"?al(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?al(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Xm=Et(Ym),Zm=Me({},Wl,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Ac=Et(Zm),Jm=Me({},di,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:hu}),eg=Et(Jm),tg=Me({},uo,{propertyName:0,elapsedTime:0,pseudoElement:0}),ng=Et(tg),rg=Me({},Wl,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),og=Et(rg),ig=[9,13,27,32],mu=gn&&"CompositionEvent"in window,Uo=null;gn&&"documentMode"in document&&(Uo=document.documentMode);var lg=gn&&"TextEvent"in window&&!Uo,Sd=gn&&(!mu||Uo&&8<Uo&&11>=Uo),zc=String.fromCharCode(32),Dc=!1;function _d(e,t){switch(e){case"keyup":return ig.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Cd(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Dr=!1;function sg(e,t){switch(e){case"compositionend":return Cd(t);case"keypress":return t.which!==32?null:(Dc=!0,zc);case"textInput":return e=t.data,e===zc&&Dc?null:e;default:return null}}function ag(e,t){if(Dr)return e==="compositionend"||!mu&&_d(e,t)?(e=kd(),sl=du=Dn=null,Dr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Sd&&t.locale!=="ko"?null:t.data;default:return null}}var ug={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Fc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!ug[e.type]:t==="textarea"}function xd(e,t,n,r){td(r),t=xl(t,"onChange"),0<t.length&&(n=new pu("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var $o=null,Jo=null;function cg(e){Ad(e,0)}function Vl(e){var t=Ur(e);if(Kf(t))return e}function fg(e,t){if(e==="change")return t}var Ed=!1;if(gn){var Fs;if(gn){var Rs="oninput"in document;if(!Rs){var Rc=document.createElement("div");Rc.setAttribute("oninput","return;"),Rs=typeof Rc.oninput=="function"}Fs=Rs}else Fs=!1;Ed=Fs&&(!document.documentMode||9<document.documentMode)}function Uc(){$o&&($o.detachEvent("onpropertychange",Td),Jo=$o=null)}function Td(e){if(e.propertyName==="value"&&Vl(Jo)){var t=[];xd(t,Jo,e,su(e)),id(cg,t)}}function dg(e,t,n){e==="focusin"?(Uc(),$o=t,Jo=n,$o.attachEvent("onpropertychange",Td)):e==="focusout"&&Uc()}function pg(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Vl(Jo)}function hg(e,t){if(e==="click")return Vl(t)}function mg(e,t){if(e==="input"||e==="change")return Vl(t)}function gg(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Kt=typeof Object.is=="function"?Object.is:gg;function ei(e,t){if(Kt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!ra.call(t,o)||!Kt(e[o],t[o]))return!1}return!0}function $c(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Bc(e,t){var n=$c(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=$c(n)}}function Nd(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Nd(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Pd(){for(var e=window,t=yl();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=yl(e.document)}return t}function gu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function yg(e){var t=Pd(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Nd(n.ownerDocument.documentElement,n)){if(r!==null&&gu(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,i=Math.min(r.start,o);r=r.end===void 0?i:Math.min(r.end,o),!e.extend&&i>r&&(o=r,r=i,i=o),o=Bc(n,i);var a=Bc(n,r);o&&a&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==a.node||e.focusOffset!==a.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(a.node,a.offset)):(t.setEnd(a.node,a.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var vg=gn&&"documentMode"in document&&11>=document.documentMode,Fr=null,_a=null,Bo=null,Ca=!1;function Hc(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Ca||Fr==null||Fr!==yl(r)||(r=Fr,"selectionStart"in r&&gu(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Bo&&ei(Bo,r)||(Bo=r,r=xl(_a,"onSelect"),0<r.length&&(t=new pu("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Fr)))}function Ki(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Rr={animationend:Ki("Animation","AnimationEnd"),animationiteration:Ki("Animation","AnimationIteration"),animationstart:Ki("Animation","AnimationStart"),transitionend:Ki("Transition","TransitionEnd")},Us={},Od={};gn&&(Od=document.createElement("div").style,"AnimationEvent"in window||(delete Rr.animationend.animation,delete Rr.animationiteration.animation,delete Rr.animationstart.animation),"TransitionEvent"in window||delete Rr.transitionend.transition);function Ql(e){if(Us[e])return Us[e];if(!Rr[e])return e;var t=Rr[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Od)return Us[e]=t[n];return e}var Id=Ql("animationend"),Ld=Ql("animationiteration"),bd=Ql("animationstart"),jd=Ql("transitionend"),Md=new Map,Wc="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Gn(e,t){Md.set(e,t),_r(t,[e])}for(var $s=0;$s<Wc.length;$s++){var Bs=Wc[$s],wg=Bs.toLowerCase(),kg=Bs[0].toUpperCase()+Bs.slice(1);Gn(wg,"on"+kg)}Gn(Id,"onAnimationEnd");Gn(Ld,"onAnimationIteration");Gn(bd,"onAnimationStart");Gn("dblclick","onDoubleClick");Gn("focusin","onFocus");Gn("focusout","onBlur");Gn(jd,"onTransitionEnd");to("onMouseEnter",["mouseout","mouseover"]);to("onMouseLeave",["mouseout","mouseover"]);to("onPointerEnter",["pointerout","pointerover"]);to("onPointerLeave",["pointerout","pointerover"]);_r("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));_r("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));_r("onBeforeInput",["compositionend","keypress","textInput","paste"]);_r("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));_r("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));_r("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Do="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Sg=new Set("cancel close invalid load scroll toggle".split(" ").concat(Do));function Vc(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,wm(r,t,void 0,e),e.currentTarget=null}function Ad(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var a=r.length-1;0<=a;a--){var c=r[a],f=c.instance,p=c.currentTarget;if(c=c.listener,f!==i&&o.isPropagationStopped())break e;Vc(o,c,p),i=f}else for(a=0;a<r.length;a++){if(c=r[a],f=c.instance,p=c.currentTarget,c=c.listener,f!==i&&o.isPropagationStopped())break e;Vc(o,c,p),i=f}}}if(wl)throw e=va,wl=!1,va=null,e}function Te(e,t){var n=t[Pa];n===void 0&&(n=t[Pa]=new Set);var r=e+"__bubble";n.has(r)||(zd(t,e,2,!1),n.add(r))}function Hs(e,t,n){var r=0;t&&(r|=4),zd(n,e,r,t)}var Gi="_reactListening"+Math.random().toString(36).slice(2);function ti(e){if(!e[Gi]){e[Gi]=!0,Hf.forEach(function(n){n!=="selectionchange"&&(Sg.has(n)||Hs(n,!1,e),Hs(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Gi]||(t[Gi]=!0,Hs("selectionchange",!1,t))}}function zd(e,t,n,r){switch(wd(t)){case 1:var o=Am;break;case 4:o=zm;break;default:o=fu}n=o.bind(null,t,n,e),o=void 0,!ya||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Ws(e,t,n,r,o){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var a=r.tag;if(a===3||a===4){var c=r.stateNode.containerInfo;if(c===o||c.nodeType===8&&c.parentNode===o)break;if(a===4)for(a=r.return;a!==null;){var f=a.tag;if((f===3||f===4)&&(f=a.stateNode.containerInfo,f===o||f.nodeType===8&&f.parentNode===o))return;a=a.return}for(;c!==null;){if(a=dr(c),a===null)return;if(f=a.tag,f===5||f===6){r=i=a;continue e}c=c.parentNode}}r=r.return}id(function(){var p=i,w=su(n),_=[];e:{var v=Md.get(e);if(v!==void 0){var E=pu,k=e;switch(e){case"keypress":if(al(n)===0)break e;case"keydown":case"keyup":E=Xm;break;case"focusin":k="focus",E=Ds;break;case"focusout":k="blur",E=Ds;break;case"beforeblur":case"afterblur":E=Ds;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":E=jc;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":E=Rm;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":E=eg;break;case Id:case Ld:case bd:E=Bm;break;case jd:E=ng;break;case"scroll":E=Dm;break;case"wheel":E=og;break;case"copy":case"cut":case"paste":E=Wm;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":E=Ac}var N=(t&4)!==0,L=!N&&e==="scroll",h=N?v!==null?v+"Capture":null:v;N=[];for(var d=p,g;d!==null;){g=d;var C=g.stateNode;if(g.tag===5&&C!==null&&(g=C,h!==null&&(C=Go(d,h),C!=null&&N.push(ni(d,C,g)))),L)break;d=d.return}0<N.length&&(v=new E(v,k,null,n,w),_.push({event:v,listeners:N}))}}if(!(t&7)){e:{if(v=e==="mouseover"||e==="pointerover",E=e==="mouseout"||e==="pointerout",v&&n!==ma&&(k=n.relatedTarget||n.fromElement)&&(dr(k)||k[yn]))break e;if((E||v)&&(v=w.window===w?w:(v=w.ownerDocument)?v.defaultView||v.parentWindow:window,E?(k=n.relatedTarget||n.toElement,E=p,k=k?dr(k):null,k!==null&&(L=Cr(k),k!==L||k.tag!==5&&k.tag!==6)&&(k=null)):(E=null,k=p),E!==k)){if(N=jc,C="onMouseLeave",h="onMouseEnter",d="mouse",(e==="pointerout"||e==="pointerover")&&(N=Ac,C="onPointerLeave",h="onPointerEnter",d="pointer"),L=E==null?v:Ur(E),g=k==null?v:Ur(k),v=new N(C,d+"leave",E,n,w),v.target=L,v.relatedTarget=g,C=null,dr(w)===p&&(N=new N(h,d+"enter",k,n,w),N.target=g,N.relatedTarget=L,C=N),L=C,E&&k)t:{for(N=E,h=k,d=0,g=N;g;g=Mr(g))d++;for(g=0,C=h;C;C=Mr(C))g++;for(;0<d-g;)N=Mr(N),d--;for(;0<g-d;)h=Mr(h),g--;for(;d--;){if(N===h||h!==null&&N===h.alternate)break t;N=Mr(N),h=Mr(h)}N=null}else N=null;E!==null&&Qc(_,v,E,N,!1),k!==null&&L!==null&&Qc(_,L,k,N,!0)}}e:{if(v=p?Ur(p):window,E=v.nodeName&&v.nodeName.toLowerCase(),E==="select"||E==="input"&&v.type==="file")var I=fg;else if(Fc(v))if(Ed)I=mg;else{I=pg;var j=dg}else(E=v.nodeName)&&E.toLowerCase()==="input"&&(v.type==="checkbox"||v.type==="radio")&&(I=hg);if(I&&(I=I(e,p))){xd(_,I,n,w);break e}j&&j(e,v,p),e==="focusout"&&(j=v._wrapperState)&&j.controlled&&v.type==="number"&&ca(v,"number",v.value)}switch(j=p?Ur(p):window,e){case"focusin":(Fc(j)||j.contentEditable==="true")&&(Fr=j,_a=p,Bo=null);break;case"focusout":Bo=_a=Fr=null;break;case"mousedown":Ca=!0;break;case"contextmenu":case"mouseup":case"dragend":Ca=!1,Hc(_,n,w);break;case"selectionchange":if(vg)break;case"keydown":case"keyup":Hc(_,n,w)}var y;if(mu)e:{switch(e){case"compositionstart":var x="onCompositionStart";break e;case"compositionend":x="onCompositionEnd";break e;case"compositionupdate":x="onCompositionUpdate";break e}x=void 0}else Dr?_d(e,n)&&(x="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(x="onCompositionStart");x&&(Sd&&n.locale!=="ko"&&(Dr||x!=="onCompositionStart"?x==="onCompositionEnd"&&Dr&&(y=kd()):(Dn=w,du="value"in Dn?Dn.value:Dn.textContent,Dr=!0)),j=xl(p,x),0<j.length&&(x=new Mc(x,e,null,n,w),_.push({event:x,listeners:j}),y?x.data=y:(y=Cd(n),y!==null&&(x.data=y)))),(y=lg?sg(e,n):ag(e,n))&&(p=xl(p,"onBeforeInput"),0<p.length&&(w=new Mc("onBeforeInput","beforeinput",null,n,w),_.push({event:w,listeners:p}),w.data=y))}Ad(_,t)})}function ni(e,t,n){return{instance:e,listener:t,currentTarget:n}}function xl(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,i=o.stateNode;o.tag===5&&i!==null&&(o=i,i=Go(e,n),i!=null&&r.unshift(ni(e,i,o)),i=Go(e,t),i!=null&&r.push(ni(e,i,o))),e=e.return}return r}function Mr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Qc(e,t,n,r,o){for(var i=t._reactName,a=[];n!==null&&n!==r;){var c=n,f=c.alternate,p=c.stateNode;if(f!==null&&f===r)break;c.tag===5&&p!==null&&(c=p,o?(f=Go(n,i),f!=null&&a.unshift(ni(n,f,c))):o||(f=Go(n,i),f!=null&&a.push(ni(n,f,c)))),n=n.return}a.length!==0&&e.push({event:t,listeners:a})}var _g=/\r\n?/g,Cg=/\u0000|\uFFFD/g;function qc(e){return(typeof e=="string"?e:""+e).replace(_g,`
`).replace(Cg,"")}function Yi(e,t,n){if(t=qc(t),qc(e)!==t&&n)throw Error(F(425))}function El(){}var xa=null,Ea=null;function Ta(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Na=typeof setTimeout=="function"?setTimeout:void 0,xg=typeof clearTimeout=="function"?clearTimeout:void 0,Kc=typeof Promise=="function"?Promise:void 0,Eg=typeof queueMicrotask=="function"?queueMicrotask:typeof Kc<"u"?function(e){return Kc.resolve(null).then(e).catch(Tg)}:Na;function Tg(e){setTimeout(function(){throw e})}function Vs(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),Zo(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);Zo(t)}function Bn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Gc(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var co=Math.random().toString(36).slice(2),nn="__reactFiber$"+co,ri="__reactProps$"+co,yn="__reactContainer$"+co,Pa="__reactEvents$"+co,Ng="__reactListeners$"+co,Pg="__reactHandles$"+co;function dr(e){var t=e[nn];if(t)return t;for(var n=e.parentNode;n;){if(t=n[yn]||n[nn]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Gc(e);e!==null;){if(n=e[nn])return n;e=Gc(e)}return t}e=n,n=e.parentNode}return null}function pi(e){return e=e[nn]||e[yn],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Ur(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(F(33))}function ql(e){return e[ri]||null}var Oa=[],$r=-1;function Yn(e){return{current:e}}function Ne(e){0>$r||(e.current=Oa[$r],Oa[$r]=null,$r--)}function Ee(e,t){$r++,Oa[$r]=e.current,e.current=t}var Kn={},lt=Yn(Kn),mt=Yn(!1),yr=Kn;function no(e,t){var n=e.type.contextTypes;if(!n)return Kn;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},i;for(i in n)o[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function gt(e){return e=e.childContextTypes,e!=null}function Tl(){Ne(mt),Ne(lt)}function Yc(e,t,n){if(lt.current!==Kn)throw Error(F(168));Ee(lt,t),Ee(mt,n)}function Dd(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(F(108,dm(e)||"Unknown",o));return Me({},n,r)}function Nl(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Kn,yr=lt.current,Ee(lt,e),Ee(mt,mt.current),!0}function Xc(e,t,n){var r=e.stateNode;if(!r)throw Error(F(169));n?(e=Dd(e,t,yr),r.__reactInternalMemoizedMergedChildContext=e,Ne(mt),Ne(lt),Ee(lt,e)):Ne(mt),Ee(mt,n)}var dn=null,Kl=!1,Qs=!1;function Fd(e){dn===null?dn=[e]:dn.push(e)}function Og(e){Kl=!0,Fd(e)}function Xn(){if(!Qs&&dn!==null){Qs=!0;var e=0,t=Se;try{var n=dn;for(Se=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}dn=null,Kl=!1}catch(o){throw dn!==null&&(dn=dn.slice(e+1)),ud(au,Xn),o}finally{Se=t,Qs=!1}}return null}var Br=[],Hr=0,Pl=null,Ol=0,Nt=[],Pt=0,vr=null,pn=1,hn="";function cr(e,t){Br[Hr++]=Ol,Br[Hr++]=Pl,Pl=e,Ol=t}function Rd(e,t,n){Nt[Pt++]=pn,Nt[Pt++]=hn,Nt[Pt++]=vr,vr=e;var r=pn;e=hn;var o=32-Qt(r)-1;r&=~(1<<o),n+=1;var i=32-Qt(t)+o;if(30<i){var a=o-o%5;i=(r&(1<<a)-1).toString(32),r>>=a,o-=a,pn=1<<32-Qt(t)+o|n<<o|r,hn=i+e}else pn=1<<i|n<<o|r,hn=e}function yu(e){e.return!==null&&(cr(e,1),Rd(e,1,0))}function vu(e){for(;e===Pl;)Pl=Br[--Hr],Br[Hr]=null,Ol=Br[--Hr],Br[Hr]=null;for(;e===vr;)vr=Nt[--Pt],Nt[Pt]=null,hn=Nt[--Pt],Nt[Pt]=null,pn=Nt[--Pt],Nt[Pt]=null}var _t=null,St=null,Ie=!1,Vt=null;function Ud(e,t){var n=Ot(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Zc(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,_t=e,St=Bn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,_t=e,St=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=vr!==null?{id:pn,overflow:hn}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Ot(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,_t=e,St=null,!0):!1;default:return!1}}function Ia(e){return(e.mode&1)!==0&&(e.flags&128)===0}function La(e){if(Ie){var t=St;if(t){var n=t;if(!Zc(e,t)){if(Ia(e))throw Error(F(418));t=Bn(n.nextSibling);var r=_t;t&&Zc(e,t)?Ud(r,n):(e.flags=e.flags&-4097|2,Ie=!1,_t=e)}}else{if(Ia(e))throw Error(F(418));e.flags=e.flags&-4097|2,Ie=!1,_t=e}}}function Jc(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;_t=e}function Xi(e){if(e!==_t)return!1;if(!Ie)return Jc(e),Ie=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Ta(e.type,e.memoizedProps)),t&&(t=St)){if(Ia(e))throw $d(),Error(F(418));for(;t;)Ud(e,t),t=Bn(t.nextSibling)}if(Jc(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(F(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){St=Bn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}St=null}}else St=_t?Bn(e.stateNode.nextSibling):null;return!0}function $d(){for(var e=St;e;)e=Bn(e.nextSibling)}function ro(){St=_t=null,Ie=!1}function wu(e){Vt===null?Vt=[e]:Vt.push(e)}var Ig=kn.ReactCurrentBatchConfig;function Lo(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(F(309));var r=n.stateNode}if(!r)throw Error(F(147,e));var o=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(a){var c=o.refs;a===null?delete c[i]:c[i]=a},t._stringRef=i,t)}if(typeof e!="string")throw Error(F(284));if(!n._owner)throw Error(F(290,e))}return e}function Zi(e,t){throw e=Object.prototype.toString.call(t),Error(F(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function ef(e){var t=e._init;return t(e._payload)}function Bd(e){function t(h,d){if(e){var g=h.deletions;g===null?(h.deletions=[d],h.flags|=16):g.push(d)}}function n(h,d){if(!e)return null;for(;d!==null;)t(h,d),d=d.sibling;return null}function r(h,d){for(h=new Map;d!==null;)d.key!==null?h.set(d.key,d):h.set(d.index,d),d=d.sibling;return h}function o(h,d){return h=Qn(h,d),h.index=0,h.sibling=null,h}function i(h,d,g){return h.index=g,e?(g=h.alternate,g!==null?(g=g.index,g<d?(h.flags|=2,d):g):(h.flags|=2,d)):(h.flags|=1048576,d)}function a(h){return e&&h.alternate===null&&(h.flags|=2),h}function c(h,d,g,C){return d===null||d.tag!==6?(d=Js(g,h.mode,C),d.return=h,d):(d=o(d,g),d.return=h,d)}function f(h,d,g,C){var I=g.type;return I===zr?w(h,d,g.props.children,C,g.key):d!==null&&(d.elementType===I||typeof I=="object"&&I!==null&&I.$$typeof===jn&&ef(I)===d.type)?(C=o(d,g.props),C.ref=Lo(h,d,g),C.return=h,C):(C=ml(g.type,g.key,g.props,null,h.mode,C),C.ref=Lo(h,d,g),C.return=h,C)}function p(h,d,g,C){return d===null||d.tag!==4||d.stateNode.containerInfo!==g.containerInfo||d.stateNode.implementation!==g.implementation?(d=ea(g,h.mode,C),d.return=h,d):(d=o(d,g.children||[]),d.return=h,d)}function w(h,d,g,C,I){return d===null||d.tag!==7?(d=gr(g,h.mode,C,I),d.return=h,d):(d=o(d,g),d.return=h,d)}function _(h,d,g){if(typeof d=="string"&&d!==""||typeof d=="number")return d=Js(""+d,h.mode,g),d.return=h,d;if(typeof d=="object"&&d!==null){switch(d.$$typeof){case $i:return g=ml(d.type,d.key,d.props,null,h.mode,g),g.ref=Lo(h,null,d),g.return=h,g;case Ar:return d=ea(d,h.mode,g),d.return=h,d;case jn:var C=d._init;return _(h,C(d._payload),g)}if(Ao(d)||To(d))return d=gr(d,h.mode,g,null),d.return=h,d;Zi(h,d)}return null}function v(h,d,g,C){var I=d!==null?d.key:null;if(typeof g=="string"&&g!==""||typeof g=="number")return I!==null?null:c(h,d,""+g,C);if(typeof g=="object"&&g!==null){switch(g.$$typeof){case $i:return g.key===I?f(h,d,g,C):null;case Ar:return g.key===I?p(h,d,g,C):null;case jn:return I=g._init,v(h,d,I(g._payload),C)}if(Ao(g)||To(g))return I!==null?null:w(h,d,g,C,null);Zi(h,g)}return null}function E(h,d,g,C,I){if(typeof C=="string"&&C!==""||typeof C=="number")return h=h.get(g)||null,c(d,h,""+C,I);if(typeof C=="object"&&C!==null){switch(C.$$typeof){case $i:return h=h.get(C.key===null?g:C.key)||null,f(d,h,C,I);case Ar:return h=h.get(C.key===null?g:C.key)||null,p(d,h,C,I);case jn:var j=C._init;return E(h,d,g,j(C._payload),I)}if(Ao(C)||To(C))return h=h.get(g)||null,w(d,h,C,I,null);Zi(d,C)}return null}function k(h,d,g,C){for(var I=null,j=null,y=d,x=d=0,M=null;y!==null&&x<g.length;x++){y.index>x?(M=y,y=null):M=y.sibling;var T=v(h,y,g[x],C);if(T===null){y===null&&(y=M);break}e&&y&&T.alternate===null&&t(h,y),d=i(T,d,x),j===null?I=T:j.sibling=T,j=T,y=M}if(x===g.length)return n(h,y),Ie&&cr(h,x),I;if(y===null){for(;x<g.length;x++)y=_(h,g[x],C),y!==null&&(d=i(y,d,x),j===null?I=y:j.sibling=y,j=y);return Ie&&cr(h,x),I}for(y=r(h,y);x<g.length;x++)M=E(y,h,x,g[x],C),M!==null&&(e&&M.alternate!==null&&y.delete(M.key===null?x:M.key),d=i(M,d,x),j===null?I=M:j.sibling=M,j=M);return e&&y.forEach(function(R){return t(h,R)}),Ie&&cr(h,x),I}function N(h,d,g,C){var I=To(g);if(typeof I!="function")throw Error(F(150));if(g=I.call(g),g==null)throw Error(F(151));for(var j=I=null,y=d,x=d=0,M=null,T=g.next();y!==null&&!T.done;x++,T=g.next()){y.index>x?(M=y,y=null):M=y.sibling;var R=v(h,y,T.value,C);if(R===null){y===null&&(y=M);break}e&&y&&R.alternate===null&&t(h,y),d=i(R,d,x),j===null?I=R:j.sibling=R,j=R,y=M}if(T.done)return n(h,y),Ie&&cr(h,x),I;if(y===null){for(;!T.done;x++,T=g.next())T=_(h,T.value,C),T!==null&&(d=i(T,d,x),j===null?I=T:j.sibling=T,j=T);return Ie&&cr(h,x),I}for(y=r(h,y);!T.done;x++,T=g.next())T=E(y,h,x,T.value,C),T!==null&&(e&&T.alternate!==null&&y.delete(T.key===null?x:T.key),d=i(T,d,x),j===null?I=T:j.sibling=T,j=T);return e&&y.forEach(function(ee){return t(h,ee)}),Ie&&cr(h,x),I}function L(h,d,g,C){if(typeof g=="object"&&g!==null&&g.type===zr&&g.key===null&&(g=g.props.children),typeof g=="object"&&g!==null){switch(g.$$typeof){case $i:e:{for(var I=g.key,j=d;j!==null;){if(j.key===I){if(I=g.type,I===zr){if(j.tag===7){n(h,j.sibling),d=o(j,g.props.children),d.return=h,h=d;break e}}else if(j.elementType===I||typeof I=="object"&&I!==null&&I.$$typeof===jn&&ef(I)===j.type){n(h,j.sibling),d=o(j,g.props),d.ref=Lo(h,j,g),d.return=h,h=d;break e}n(h,j);break}else t(h,j);j=j.sibling}g.type===zr?(d=gr(g.props.children,h.mode,C,g.key),d.return=h,h=d):(C=ml(g.type,g.key,g.props,null,h.mode,C),C.ref=Lo(h,d,g),C.return=h,h=C)}return a(h);case Ar:e:{for(j=g.key;d!==null;){if(d.key===j)if(d.tag===4&&d.stateNode.containerInfo===g.containerInfo&&d.stateNode.implementation===g.implementation){n(h,d.sibling),d=o(d,g.children||[]),d.return=h,h=d;break e}else{n(h,d);break}else t(h,d);d=d.sibling}d=ea(g,h.mode,C),d.return=h,h=d}return a(h);case jn:return j=g._init,L(h,d,j(g._payload),C)}if(Ao(g))return k(h,d,g,C);if(To(g))return N(h,d,g,C);Zi(h,g)}return typeof g=="string"&&g!==""||typeof g=="number"?(g=""+g,d!==null&&d.tag===6?(n(h,d.sibling),d=o(d,g),d.return=h,h=d):(n(h,d),d=Js(g,h.mode,C),d.return=h,h=d),a(h)):n(h,d)}return L}var oo=Bd(!0),Hd=Bd(!1),Il=Yn(null),Ll=null,Wr=null,ku=null;function Su(){ku=Wr=Ll=null}function _u(e){var t=Il.current;Ne(Il),e._currentValue=t}function ba(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Jr(e,t){Ll=e,ku=Wr=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(ht=!0),e.firstContext=null)}function Lt(e){var t=e._currentValue;if(ku!==e)if(e={context:e,memoizedValue:t,next:null},Wr===null){if(Ll===null)throw Error(F(308));Wr=e,Ll.dependencies={lanes:0,firstContext:e}}else Wr=Wr.next=e;return t}var pr=null;function Cu(e){pr===null?pr=[e]:pr.push(e)}function Wd(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,Cu(t)):(n.next=o.next,o.next=n),t.interleaved=n,vn(e,r)}function vn(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Mn=!1;function xu(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Vd(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function mn(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Hn(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,ge&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,vn(e,n)}return o=r.interleaved,o===null?(t.next=t,Cu(r)):(t.next=o.next,o.next=t),r.interleaved=t,vn(e,n)}function ul(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,uu(e,n)}}function tf(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var a={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?o=i=a:i=i.next=a,n=n.next}while(n!==null);i===null?o=i=t:i=i.next=t}else o=i=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function bl(e,t,n,r){var o=e.updateQueue;Mn=!1;var i=o.firstBaseUpdate,a=o.lastBaseUpdate,c=o.shared.pending;if(c!==null){o.shared.pending=null;var f=c,p=f.next;f.next=null,a===null?i=p:a.next=p,a=f;var w=e.alternate;w!==null&&(w=w.updateQueue,c=w.lastBaseUpdate,c!==a&&(c===null?w.firstBaseUpdate=p:c.next=p,w.lastBaseUpdate=f))}if(i!==null){var _=o.baseState;a=0,w=p=f=null,c=i;do{var v=c.lane,E=c.eventTime;if((r&v)===v){w!==null&&(w=w.next={eventTime:E,lane:0,tag:c.tag,payload:c.payload,callback:c.callback,next:null});e:{var k=e,N=c;switch(v=t,E=n,N.tag){case 1:if(k=N.payload,typeof k=="function"){_=k.call(E,_,v);break e}_=k;break e;case 3:k.flags=k.flags&-65537|128;case 0:if(k=N.payload,v=typeof k=="function"?k.call(E,_,v):k,v==null)break e;_=Me({},_,v);break e;case 2:Mn=!0}}c.callback!==null&&c.lane!==0&&(e.flags|=64,v=o.effects,v===null?o.effects=[c]:v.push(c))}else E={eventTime:E,lane:v,tag:c.tag,payload:c.payload,callback:c.callback,next:null},w===null?(p=w=E,f=_):w=w.next=E,a|=v;if(c=c.next,c===null){if(c=o.shared.pending,c===null)break;v=c,c=v.next,v.next=null,o.lastBaseUpdate=v,o.shared.pending=null}}while(1);if(w===null&&(f=_),o.baseState=f,o.firstBaseUpdate=p,o.lastBaseUpdate=w,t=o.shared.interleaved,t!==null){o=t;do a|=o.lane,o=o.next;while(o!==t)}else i===null&&(o.shared.lanes=0);kr|=a,e.lanes=a,e.memoizedState=_}}function nf(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(F(191,o));o.call(r)}}}var hi={},on=Yn(hi),oi=Yn(hi),ii=Yn(hi);function hr(e){if(e===hi)throw Error(F(174));return e}function Eu(e,t){switch(Ee(ii,t),Ee(oi,e),Ee(on,hi),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:da(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=da(t,e)}Ne(on),Ee(on,t)}function io(){Ne(on),Ne(oi),Ne(ii)}function Qd(e){hr(ii.current);var t=hr(on.current),n=da(t,e.type);t!==n&&(Ee(oi,e),Ee(on,n))}function Tu(e){oi.current===e&&(Ne(on),Ne(oi))}var be=Yn(0);function jl(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var qs=[];function Nu(){for(var e=0;e<qs.length;e++)qs[e]._workInProgressVersionPrimary=null;qs.length=0}var cl=kn.ReactCurrentDispatcher,Ks=kn.ReactCurrentBatchConfig,wr=0,je=null,qe=null,Xe=null,Ml=!1,Ho=!1,li=0,Lg=0;function rt(){throw Error(F(321))}function Pu(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Kt(e[n],t[n]))return!1;return!0}function Ou(e,t,n,r,o,i){if(wr=i,je=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,cl.current=e===null||e.memoizedState===null?Ag:zg,e=n(r,o),Ho){i=0;do{if(Ho=!1,li=0,25<=i)throw Error(F(301));i+=1,Xe=qe=null,t.updateQueue=null,cl.current=Dg,e=n(r,o)}while(Ho)}if(cl.current=Al,t=qe!==null&&qe.next!==null,wr=0,Xe=qe=je=null,Ml=!1,t)throw Error(F(300));return e}function Iu(){var e=li!==0;return li=0,e}function tn(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Xe===null?je.memoizedState=Xe=e:Xe=Xe.next=e,Xe}function bt(){if(qe===null){var e=je.alternate;e=e!==null?e.memoizedState:null}else e=qe.next;var t=Xe===null?je.memoizedState:Xe.next;if(t!==null)Xe=t,qe=e;else{if(e===null)throw Error(F(310));qe=e,e={memoizedState:qe.memoizedState,baseState:qe.baseState,baseQueue:qe.baseQueue,queue:qe.queue,next:null},Xe===null?je.memoizedState=Xe=e:Xe=Xe.next=e}return Xe}function si(e,t){return typeof t=="function"?t(e):t}function Gs(e){var t=bt(),n=t.queue;if(n===null)throw Error(F(311));n.lastRenderedReducer=e;var r=qe,o=r.baseQueue,i=n.pending;if(i!==null){if(o!==null){var a=o.next;o.next=i.next,i.next=a}r.baseQueue=o=i,n.pending=null}if(o!==null){i=o.next,r=r.baseState;var c=a=null,f=null,p=i;do{var w=p.lane;if((wr&w)===w)f!==null&&(f=f.next={lane:0,action:p.action,hasEagerState:p.hasEagerState,eagerState:p.eagerState,next:null}),r=p.hasEagerState?p.eagerState:e(r,p.action);else{var _={lane:w,action:p.action,hasEagerState:p.hasEagerState,eagerState:p.eagerState,next:null};f===null?(c=f=_,a=r):f=f.next=_,je.lanes|=w,kr|=w}p=p.next}while(p!==null&&p!==i);f===null?a=r:f.next=c,Kt(r,t.memoizedState)||(ht=!0),t.memoizedState=r,t.baseState=a,t.baseQueue=f,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do i=o.lane,je.lanes|=i,kr|=i,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Ys(e){var t=bt(),n=t.queue;if(n===null)throw Error(F(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,i=t.memoizedState;if(o!==null){n.pending=null;var a=o=o.next;do i=e(i,a.action),a=a.next;while(a!==o);Kt(i,t.memoizedState)||(ht=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function qd(){}function Kd(e,t){var n=je,r=bt(),o=t(),i=!Kt(r.memoizedState,o);if(i&&(r.memoizedState=o,ht=!0),r=r.queue,Lu(Xd.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||Xe!==null&&Xe.memoizedState.tag&1){if(n.flags|=2048,ai(9,Yd.bind(null,n,r,o,t),void 0,null),Ze===null)throw Error(F(349));wr&30||Gd(n,t,o)}return o}function Gd(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=je.updateQueue,t===null?(t={lastEffect:null,stores:null},je.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Yd(e,t,n,r){t.value=n,t.getSnapshot=r,Zd(t)&&Jd(e)}function Xd(e,t,n){return n(function(){Zd(t)&&Jd(e)})}function Zd(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Kt(e,n)}catch{return!0}}function Jd(e){var t=vn(e,1);t!==null&&qt(t,e,1,-1)}function rf(e){var t=tn();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:si,lastRenderedState:e},t.queue=e,e=e.dispatch=Mg.bind(null,je,e),[t.memoizedState,e]}function ai(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=je.updateQueue,t===null?(t={lastEffect:null,stores:null},je.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function ep(){return bt().memoizedState}function fl(e,t,n,r){var o=tn();je.flags|=e,o.memoizedState=ai(1|t,n,void 0,r===void 0?null:r)}function Gl(e,t,n,r){var o=bt();r=r===void 0?null:r;var i=void 0;if(qe!==null){var a=qe.memoizedState;if(i=a.destroy,r!==null&&Pu(r,a.deps)){o.memoizedState=ai(t,n,i,r);return}}je.flags|=e,o.memoizedState=ai(1|t,n,i,r)}function of(e,t){return fl(8390656,8,e,t)}function Lu(e,t){return Gl(2048,8,e,t)}function tp(e,t){return Gl(4,2,e,t)}function np(e,t){return Gl(4,4,e,t)}function rp(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function op(e,t,n){return n=n!=null?n.concat([e]):null,Gl(4,4,rp.bind(null,t,e),n)}function bu(){}function ip(e,t){var n=bt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Pu(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function lp(e,t){var n=bt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Pu(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function sp(e,t,n){return wr&21?(Kt(n,t)||(n=dd(),je.lanes|=n,kr|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,ht=!0),e.memoizedState=n)}function bg(e,t){var n=Se;Se=n!==0&&4>n?n:4,e(!0);var r=Ks.transition;Ks.transition={};try{e(!1),t()}finally{Se=n,Ks.transition=r}}function ap(){return bt().memoizedState}function jg(e,t,n){var r=Vn(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},up(e))cp(t,n);else if(n=Wd(e,t,n,r),n!==null){var o=at();qt(n,e,r,o),fp(n,t,r)}}function Mg(e,t,n){var r=Vn(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(up(e))cp(t,o);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var a=t.lastRenderedState,c=i(a,n);if(o.hasEagerState=!0,o.eagerState=c,Kt(c,a)){var f=t.interleaved;f===null?(o.next=o,Cu(t)):(o.next=f.next,f.next=o),t.interleaved=o;return}}catch{}finally{}n=Wd(e,t,o,r),n!==null&&(o=at(),qt(n,e,r,o),fp(n,t,r))}}function up(e){var t=e.alternate;return e===je||t!==null&&t===je}function cp(e,t){Ho=Ml=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function fp(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,uu(e,n)}}var Al={readContext:Lt,useCallback:rt,useContext:rt,useEffect:rt,useImperativeHandle:rt,useInsertionEffect:rt,useLayoutEffect:rt,useMemo:rt,useReducer:rt,useRef:rt,useState:rt,useDebugValue:rt,useDeferredValue:rt,useTransition:rt,useMutableSource:rt,useSyncExternalStore:rt,useId:rt,unstable_isNewReconciler:!1},Ag={readContext:Lt,useCallback:function(e,t){return tn().memoizedState=[e,t===void 0?null:t],e},useContext:Lt,useEffect:of,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,fl(4194308,4,rp.bind(null,t,e),n)},useLayoutEffect:function(e,t){return fl(4194308,4,e,t)},useInsertionEffect:function(e,t){return fl(4,2,e,t)},useMemo:function(e,t){var n=tn();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=tn();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=jg.bind(null,je,e),[r.memoizedState,e]},useRef:function(e){var t=tn();return e={current:e},t.memoizedState=e},useState:rf,useDebugValue:bu,useDeferredValue:function(e){return tn().memoizedState=e},useTransition:function(){var e=rf(!1),t=e[0];return e=bg.bind(null,e[1]),tn().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=je,o=tn();if(Ie){if(n===void 0)throw Error(F(407));n=n()}else{if(n=t(),Ze===null)throw Error(F(349));wr&30||Gd(r,t,n)}o.memoizedState=n;var i={value:n,getSnapshot:t};return o.queue=i,of(Xd.bind(null,r,i,e),[e]),r.flags|=2048,ai(9,Yd.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=tn(),t=Ze.identifierPrefix;if(Ie){var n=hn,r=pn;n=(r&~(1<<32-Qt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=li++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Lg++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},zg={readContext:Lt,useCallback:ip,useContext:Lt,useEffect:Lu,useImperativeHandle:op,useInsertionEffect:tp,useLayoutEffect:np,useMemo:lp,useReducer:Gs,useRef:ep,useState:function(){return Gs(si)},useDebugValue:bu,useDeferredValue:function(e){var t=bt();return sp(t,qe.memoizedState,e)},useTransition:function(){var e=Gs(si)[0],t=bt().memoizedState;return[e,t]},useMutableSource:qd,useSyncExternalStore:Kd,useId:ap,unstable_isNewReconciler:!1},Dg={readContext:Lt,useCallback:ip,useContext:Lt,useEffect:Lu,useImperativeHandle:op,useInsertionEffect:tp,useLayoutEffect:np,useMemo:lp,useReducer:Ys,useRef:ep,useState:function(){return Ys(si)},useDebugValue:bu,useDeferredValue:function(e){var t=bt();return qe===null?t.memoizedState=e:sp(t,qe.memoizedState,e)},useTransition:function(){var e=Ys(si)[0],t=bt().memoizedState;return[e,t]},useMutableSource:qd,useSyncExternalStore:Kd,useId:ap,unstable_isNewReconciler:!1};function Ht(e,t){if(e&&e.defaultProps){t=Me({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function ja(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:Me({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Yl={isMounted:function(e){return(e=e._reactInternals)?Cr(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=at(),o=Vn(e),i=mn(r,o);i.payload=t,n!=null&&(i.callback=n),t=Hn(e,i,o),t!==null&&(qt(t,e,o,r),ul(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=at(),o=Vn(e),i=mn(r,o);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=Hn(e,i,o),t!==null&&(qt(t,e,o,r),ul(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=at(),r=Vn(e),o=mn(n,r);o.tag=2,t!=null&&(o.callback=t),t=Hn(e,o,r),t!==null&&(qt(t,e,r,n),ul(t,e,r))}};function lf(e,t,n,r,o,i,a){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,a):t.prototype&&t.prototype.isPureReactComponent?!ei(n,r)||!ei(o,i):!0}function dp(e,t,n){var r=!1,o=Kn,i=t.contextType;return typeof i=="object"&&i!==null?i=Lt(i):(o=gt(t)?yr:lt.current,r=t.contextTypes,i=(r=r!=null)?no(e,o):Kn),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Yl,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=i),t}function sf(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Yl.enqueueReplaceState(t,t.state,null)}function Ma(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},xu(e);var i=t.contextType;typeof i=="object"&&i!==null?o.context=Lt(i):(i=gt(t)?yr:lt.current,o.context=no(e,i)),o.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(ja(e,t,i,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&Yl.enqueueReplaceState(o,o.state,null),bl(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function lo(e,t){try{var n="",r=t;do n+=fm(r),r=r.return;while(r);var o=n}catch(i){o=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:o,digest:null}}function Xs(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Aa(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Fg=typeof WeakMap=="function"?WeakMap:Map;function pp(e,t,n){n=mn(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Dl||(Dl=!0,Va=r),Aa(e,t)},n}function hp(e,t,n){n=mn(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){Aa(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){Aa(e,t),typeof r!="function"&&(Wn===null?Wn=new Set([this]):Wn.add(this));var a=t.stack;this.componentDidCatch(t.value,{componentStack:a!==null?a:""})}),n}function af(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Fg;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=Zg.bind(null,e,t,n),t.then(e,e))}function uf(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function cf(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=mn(-1,1),t.tag=2,Hn(n,t,1))),n.lanes|=1),e)}var Rg=kn.ReactCurrentOwner,ht=!1;function st(e,t,n,r){t.child=e===null?Hd(t,null,n,r):oo(t,e.child,n,r)}function ff(e,t,n,r,o){n=n.render;var i=t.ref;return Jr(t,o),r=Ou(e,t,n,r,i,o),n=Iu(),e!==null&&!ht?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,wn(e,t,o)):(Ie&&n&&yu(t),t.flags|=1,st(e,t,r,o),t.child)}function df(e,t,n,r,o){if(e===null){var i=n.type;return typeof i=="function"&&!Uu(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,mp(e,t,i,r,o)):(e=ml(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&o)){var a=i.memoizedProps;if(n=n.compare,n=n!==null?n:ei,n(a,r)&&e.ref===t.ref)return wn(e,t,o)}return t.flags|=1,e=Qn(i,r),e.ref=t.ref,e.return=t,t.child=e}function mp(e,t,n,r,o){if(e!==null){var i=e.memoizedProps;if(ei(i,r)&&e.ref===t.ref)if(ht=!1,t.pendingProps=r=i,(e.lanes&o)!==0)e.flags&131072&&(ht=!0);else return t.lanes=e.lanes,wn(e,t,o)}return za(e,t,n,r,o)}function gp(e,t,n){var r=t.pendingProps,o=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Ee(Qr,kt),kt|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Ee(Qr,kt),kt|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,Ee(Qr,kt),kt|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,Ee(Qr,kt),kt|=r;return st(e,t,o,n),t.child}function yp(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function za(e,t,n,r,o){var i=gt(n)?yr:lt.current;return i=no(t,i),Jr(t,o),n=Ou(e,t,n,r,i,o),r=Iu(),e!==null&&!ht?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,wn(e,t,o)):(Ie&&r&&yu(t),t.flags|=1,st(e,t,n,o),t.child)}function pf(e,t,n,r,o){if(gt(n)){var i=!0;Nl(t)}else i=!1;if(Jr(t,o),t.stateNode===null)dl(e,t),dp(t,n,r),Ma(t,n,r,o),r=!0;else if(e===null){var a=t.stateNode,c=t.memoizedProps;a.props=c;var f=a.context,p=n.contextType;typeof p=="object"&&p!==null?p=Lt(p):(p=gt(n)?yr:lt.current,p=no(t,p));var w=n.getDerivedStateFromProps,_=typeof w=="function"||typeof a.getSnapshotBeforeUpdate=="function";_||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(c!==r||f!==p)&&sf(t,a,r,p),Mn=!1;var v=t.memoizedState;a.state=v,bl(t,r,a,o),f=t.memoizedState,c!==r||v!==f||mt.current||Mn?(typeof w=="function"&&(ja(t,n,w,r),f=t.memoizedState),(c=Mn||lf(t,n,c,r,v,f,p))?(_||typeof a.UNSAFE_componentWillMount!="function"&&typeof a.componentWillMount!="function"||(typeof a.componentWillMount=="function"&&a.componentWillMount(),typeof a.UNSAFE_componentWillMount=="function"&&a.UNSAFE_componentWillMount()),typeof a.componentDidMount=="function"&&(t.flags|=4194308)):(typeof a.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=f),a.props=r,a.state=f,a.context=p,r=c):(typeof a.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{a=t.stateNode,Vd(e,t),c=t.memoizedProps,p=t.type===t.elementType?c:Ht(t.type,c),a.props=p,_=t.pendingProps,v=a.context,f=n.contextType,typeof f=="object"&&f!==null?f=Lt(f):(f=gt(n)?yr:lt.current,f=no(t,f));var E=n.getDerivedStateFromProps;(w=typeof E=="function"||typeof a.getSnapshotBeforeUpdate=="function")||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(c!==_||v!==f)&&sf(t,a,r,f),Mn=!1,v=t.memoizedState,a.state=v,bl(t,r,a,o);var k=t.memoizedState;c!==_||v!==k||mt.current||Mn?(typeof E=="function"&&(ja(t,n,E,r),k=t.memoizedState),(p=Mn||lf(t,n,p,r,v,k,f)||!1)?(w||typeof a.UNSAFE_componentWillUpdate!="function"&&typeof a.componentWillUpdate!="function"||(typeof a.componentWillUpdate=="function"&&a.componentWillUpdate(r,k,f),typeof a.UNSAFE_componentWillUpdate=="function"&&a.UNSAFE_componentWillUpdate(r,k,f)),typeof a.componentDidUpdate=="function"&&(t.flags|=4),typeof a.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof a.componentDidUpdate!="function"||c===e.memoizedProps&&v===e.memoizedState||(t.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||c===e.memoizedProps&&v===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=k),a.props=r,a.state=k,a.context=f,r=p):(typeof a.componentDidUpdate!="function"||c===e.memoizedProps&&v===e.memoizedState||(t.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||c===e.memoizedProps&&v===e.memoizedState||(t.flags|=1024),r=!1)}return Da(e,t,n,r,i,o)}function Da(e,t,n,r,o,i){yp(e,t);var a=(t.flags&128)!==0;if(!r&&!a)return o&&Xc(t,n,!1),wn(e,t,i);r=t.stateNode,Rg.current=t;var c=a&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&a?(t.child=oo(t,e.child,null,i),t.child=oo(t,null,c,i)):st(e,t,c,i),t.memoizedState=r.state,o&&Xc(t,n,!0),t.child}function vp(e){var t=e.stateNode;t.pendingContext?Yc(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Yc(e,t.context,!1),Eu(e,t.containerInfo)}function hf(e,t,n,r,o){return ro(),wu(o),t.flags|=256,st(e,t,n,r),t.child}var Fa={dehydrated:null,treeContext:null,retryLane:0};function Ra(e){return{baseLanes:e,cachePool:null,transitions:null}}function wp(e,t,n){var r=t.pendingProps,o=be.current,i=!1,a=(t.flags&128)!==0,c;if((c=a)||(c=e!==null&&e.memoizedState===null?!1:(o&2)!==0),c?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),Ee(be,o&1),e===null)return La(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(a=r.children,e=r.fallback,i?(r=t.mode,i=t.child,a={mode:"hidden",children:a},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=a):i=Jl(a,r,0,null),e=gr(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=Ra(n),t.memoizedState=Fa,e):ju(t,a));if(o=e.memoizedState,o!==null&&(c=o.dehydrated,c!==null))return Ug(e,t,a,r,c,o,n);if(i){i=r.fallback,a=t.mode,o=e.child,c=o.sibling;var f={mode:"hidden",children:r.children};return!(a&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=f,t.deletions=null):(r=Qn(o,f),r.subtreeFlags=o.subtreeFlags&14680064),c!==null?i=Qn(c,i):(i=gr(i,a,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,a=e.child.memoizedState,a=a===null?Ra(n):{baseLanes:a.baseLanes|n,cachePool:null,transitions:a.transitions},i.memoizedState=a,i.childLanes=e.childLanes&~n,t.memoizedState=Fa,r}return i=e.child,e=i.sibling,r=Qn(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function ju(e,t){return t=Jl({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Ji(e,t,n,r){return r!==null&&wu(r),oo(t,e.child,null,n),e=ju(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Ug(e,t,n,r,o,i,a){if(n)return t.flags&256?(t.flags&=-257,r=Xs(Error(F(422))),Ji(e,t,a,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,o=t.mode,r=Jl({mode:"visible",children:r.children},o,0,null),i=gr(i,o,a,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&oo(t,e.child,null,a),t.child.memoizedState=Ra(a),t.memoizedState=Fa,i);if(!(t.mode&1))return Ji(e,t,a,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var c=r.dgst;return r=c,i=Error(F(419)),r=Xs(i,r,void 0),Ji(e,t,a,r)}if(c=(a&e.childLanes)!==0,ht||c){if(r=Ze,r!==null){switch(a&-a){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|a)?0:o,o!==0&&o!==i.retryLane&&(i.retryLane=o,vn(e,o),qt(r,e,o,-1))}return Ru(),r=Xs(Error(F(421))),Ji(e,t,a,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=Jg.bind(null,e),o._reactRetry=t,null):(e=i.treeContext,St=Bn(o.nextSibling),_t=t,Ie=!0,Vt=null,e!==null&&(Nt[Pt++]=pn,Nt[Pt++]=hn,Nt[Pt++]=vr,pn=e.id,hn=e.overflow,vr=t),t=ju(t,r.children),t.flags|=4096,t)}function mf(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),ba(e.return,t,n)}function Zs(e,t,n,r,o){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=o)}function kp(e,t,n){var r=t.pendingProps,o=r.revealOrder,i=r.tail;if(st(e,t,r.children,n),r=be.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&mf(e,n,t);else if(e.tag===19)mf(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Ee(be,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&jl(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),Zs(t,!1,o,n,i);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&jl(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}Zs(t,!0,n,null,i);break;case"together":Zs(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function dl(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function wn(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),kr|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(F(153));if(t.child!==null){for(e=t.child,n=Qn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Qn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function $g(e,t,n){switch(t.tag){case 3:vp(t),ro();break;case 5:Qd(t);break;case 1:gt(t.type)&&Nl(t);break;case 4:Eu(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;Ee(Il,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(Ee(be,be.current&1),t.flags|=128,null):n&t.child.childLanes?wp(e,t,n):(Ee(be,be.current&1),e=wn(e,t,n),e!==null?e.sibling:null);Ee(be,be.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return kp(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),Ee(be,be.current),r)break;return null;case 22:case 23:return t.lanes=0,gp(e,t,n)}return wn(e,t,n)}var Sp,Ua,_p,Cp;Sp=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Ua=function(){};_p=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,hr(on.current);var i=null;switch(n){case"input":o=aa(e,o),r=aa(e,r),i=[];break;case"select":o=Me({},o,{value:void 0}),r=Me({},r,{value:void 0}),i=[];break;case"textarea":o=fa(e,o),r=fa(e,r),i=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=El)}pa(n,r);var a;n=null;for(p in o)if(!r.hasOwnProperty(p)&&o.hasOwnProperty(p)&&o[p]!=null)if(p==="style"){var c=o[p];for(a in c)c.hasOwnProperty(a)&&(n||(n={}),n[a]="")}else p!=="dangerouslySetInnerHTML"&&p!=="children"&&p!=="suppressContentEditableWarning"&&p!=="suppressHydrationWarning"&&p!=="autoFocus"&&(qo.hasOwnProperty(p)?i||(i=[]):(i=i||[]).push(p,null));for(p in r){var f=r[p];if(c=o?.[p],r.hasOwnProperty(p)&&f!==c&&(f!=null||c!=null))if(p==="style")if(c){for(a in c)!c.hasOwnProperty(a)||f&&f.hasOwnProperty(a)||(n||(n={}),n[a]="");for(a in f)f.hasOwnProperty(a)&&c[a]!==f[a]&&(n||(n={}),n[a]=f[a])}else n||(i||(i=[]),i.push(p,n)),n=f;else p==="dangerouslySetInnerHTML"?(f=f?f.__html:void 0,c=c?c.__html:void 0,f!=null&&c!==f&&(i=i||[]).push(p,f)):p==="children"?typeof f!="string"&&typeof f!="number"||(i=i||[]).push(p,""+f):p!=="suppressContentEditableWarning"&&p!=="suppressHydrationWarning"&&(qo.hasOwnProperty(p)?(f!=null&&p==="onScroll"&&Te("scroll",e),i||c===f||(i=[])):(i=i||[]).push(p,f))}n&&(i=i||[]).push("style",n);var p=i;(t.updateQueue=p)&&(t.flags|=4)}};Cp=function(e,t,n,r){n!==r&&(t.flags|=4)};function bo(e,t){if(!Ie)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function ot(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Bg(e,t,n){var r=t.pendingProps;switch(vu(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ot(t),null;case 1:return gt(t.type)&&Tl(),ot(t),null;case 3:return r=t.stateNode,io(),Ne(mt),Ne(lt),Nu(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Xi(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Vt!==null&&(Ka(Vt),Vt=null))),Ua(e,t),ot(t),null;case 5:Tu(t);var o=hr(ii.current);if(n=t.type,e!==null&&t.stateNode!=null)_p(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(F(166));return ot(t),null}if(e=hr(on.current),Xi(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[nn]=t,r[ri]=i,e=(t.mode&1)!==0,n){case"dialog":Te("cancel",r),Te("close",r);break;case"iframe":case"object":case"embed":Te("load",r);break;case"video":case"audio":for(o=0;o<Do.length;o++)Te(Do[o],r);break;case"source":Te("error",r);break;case"img":case"image":case"link":Te("error",r),Te("load",r);break;case"details":Te("toggle",r);break;case"input":Cc(r,i),Te("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},Te("invalid",r);break;case"textarea":Ec(r,i),Te("invalid",r)}pa(n,i),o=null;for(var a in i)if(i.hasOwnProperty(a)){var c=i[a];a==="children"?typeof c=="string"?r.textContent!==c&&(i.suppressHydrationWarning!==!0&&Yi(r.textContent,c,e),o=["children",c]):typeof c=="number"&&r.textContent!==""+c&&(i.suppressHydrationWarning!==!0&&Yi(r.textContent,c,e),o=["children",""+c]):qo.hasOwnProperty(a)&&c!=null&&a==="onScroll"&&Te("scroll",r)}switch(n){case"input":Bi(r),xc(r,i,!0);break;case"textarea":Bi(r),Tc(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=El)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{a=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Xf(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=a.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=a.createElement(n,{is:r.is}):(e=a.createElement(n),n==="select"&&(a=e,r.multiple?a.multiple=!0:r.size&&(a.size=r.size))):e=a.createElementNS(e,n),e[nn]=t,e[ri]=r,Sp(e,t,!1,!1),t.stateNode=e;e:{switch(a=ha(n,r),n){case"dialog":Te("cancel",e),Te("close",e),o=r;break;case"iframe":case"object":case"embed":Te("load",e),o=r;break;case"video":case"audio":for(o=0;o<Do.length;o++)Te(Do[o],e);o=r;break;case"source":Te("error",e),o=r;break;case"img":case"image":case"link":Te("error",e),Te("load",e),o=r;break;case"details":Te("toggle",e),o=r;break;case"input":Cc(e,r),o=aa(e,r),Te("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=Me({},r,{value:void 0}),Te("invalid",e);break;case"textarea":Ec(e,r),o=fa(e,r),Te("invalid",e);break;default:o=r}pa(n,o),c=o;for(i in c)if(c.hasOwnProperty(i)){var f=c[i];i==="style"?ed(e,f):i==="dangerouslySetInnerHTML"?(f=f?f.__html:void 0,f!=null&&Zf(e,f)):i==="children"?typeof f=="string"?(n!=="textarea"||f!=="")&&Ko(e,f):typeof f=="number"&&Ko(e,""+f):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(qo.hasOwnProperty(i)?f!=null&&i==="onScroll"&&Te("scroll",e):f!=null&&ru(e,i,f,a))}switch(n){case"input":Bi(e),xc(e,r,!1);break;case"textarea":Bi(e),Tc(e);break;case"option":r.value!=null&&e.setAttribute("value",""+qn(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?Gr(e,!!r.multiple,i,!1):r.defaultValue!=null&&Gr(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=El)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return ot(t),null;case 6:if(e&&t.stateNode!=null)Cp(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(F(166));if(n=hr(ii.current),hr(on.current),Xi(t)){if(r=t.stateNode,n=t.memoizedProps,r[nn]=t,(i=r.nodeValue!==n)&&(e=_t,e!==null))switch(e.tag){case 3:Yi(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Yi(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[nn]=t,t.stateNode=r}return ot(t),null;case 13:if(Ne(be),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(Ie&&St!==null&&t.mode&1&&!(t.flags&128))$d(),ro(),t.flags|=98560,i=!1;else if(i=Xi(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(F(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(F(317));i[nn]=t}else ro(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;ot(t),i=!1}else Vt!==null&&(Ka(Vt),Vt=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||be.current&1?Ke===0&&(Ke=3):Ru())),t.updateQueue!==null&&(t.flags|=4),ot(t),null);case 4:return io(),Ua(e,t),e===null&&ti(t.stateNode.containerInfo),ot(t),null;case 10:return _u(t.type._context),ot(t),null;case 17:return gt(t.type)&&Tl(),ot(t),null;case 19:if(Ne(be),i=t.memoizedState,i===null)return ot(t),null;if(r=(t.flags&128)!==0,a=i.rendering,a===null)if(r)bo(i,!1);else{if(Ke!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(a=jl(e),a!==null){for(t.flags|=128,bo(i,!1),r=a.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,a=i.alternate,a===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=a.childLanes,i.lanes=a.lanes,i.child=a.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=a.memoizedProps,i.memoizedState=a.memoizedState,i.updateQueue=a.updateQueue,i.type=a.type,e=a.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Ee(be,be.current&1|2),t.child}e=e.sibling}i.tail!==null&&Re()>so&&(t.flags|=128,r=!0,bo(i,!1),t.lanes=4194304)}else{if(!r)if(e=jl(a),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),bo(i,!0),i.tail===null&&i.tailMode==="hidden"&&!a.alternate&&!Ie)return ot(t),null}else 2*Re()-i.renderingStartTime>so&&n!==1073741824&&(t.flags|=128,r=!0,bo(i,!1),t.lanes=4194304);i.isBackwards?(a.sibling=t.child,t.child=a):(n=i.last,n!==null?n.sibling=a:t.child=a,i.last=a)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=Re(),t.sibling=null,n=be.current,Ee(be,r?n&1|2:n&1),t):(ot(t),null);case 22:case 23:return Fu(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?kt&1073741824&&(ot(t),t.subtreeFlags&6&&(t.flags|=8192)):ot(t),null;case 24:return null;case 25:return null}throw Error(F(156,t.tag))}function Hg(e,t){switch(vu(t),t.tag){case 1:return gt(t.type)&&Tl(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return io(),Ne(mt),Ne(lt),Nu(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Tu(t),null;case 13:if(Ne(be),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(F(340));ro()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return Ne(be),null;case 4:return io(),null;case 10:return _u(t.type._context),null;case 22:case 23:return Fu(),null;case 24:return null;default:return null}}var el=!1,it=!1,Wg=typeof WeakSet=="function"?WeakSet:Set,Y=null;function Vr(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){Fe(e,t,r)}else n.current=null}function $a(e,t,n){try{n()}catch(r){Fe(e,t,r)}}var gf=!1;function Vg(e,t){if(xa=_l,e=Pd(),gu(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var a=0,c=-1,f=-1,p=0,w=0,_=e,v=null;t:for(;;){for(var E;_!==n||o!==0&&_.nodeType!==3||(c=a+o),_!==i||r!==0&&_.nodeType!==3||(f=a+r),_.nodeType===3&&(a+=_.nodeValue.length),(E=_.firstChild)!==null;)v=_,_=E;for(;;){if(_===e)break t;if(v===n&&++p===o&&(c=a),v===i&&++w===r&&(f=a),(E=_.nextSibling)!==null)break;_=v,v=_.parentNode}_=E}n=c===-1||f===-1?null:{start:c,end:f}}else n=null}n=n||{start:0,end:0}}else n=null;for(Ea={focusedElem:e,selectionRange:n},_l=!1,Y=t;Y!==null;)if(t=Y,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,Y=e;else for(;Y!==null;){t=Y;try{var k=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(k!==null){var N=k.memoizedProps,L=k.memoizedState,h=t.stateNode,d=h.getSnapshotBeforeUpdate(t.elementType===t.type?N:Ht(t.type,N),L);h.__reactInternalSnapshotBeforeUpdate=d}break;case 3:var g=t.stateNode.containerInfo;g.nodeType===1?g.textContent="":g.nodeType===9&&g.documentElement&&g.removeChild(g.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(F(163))}}catch(C){Fe(t,t.return,C)}if(e=t.sibling,e!==null){e.return=t.return,Y=e;break}Y=t.return}return k=gf,gf=!1,k}function Wo(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var i=o.destroy;o.destroy=void 0,i!==void 0&&$a(t,n,i)}o=o.next}while(o!==r)}}function Xl(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Ba(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function xp(e){var t=e.alternate;t!==null&&(e.alternate=null,xp(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[nn],delete t[ri],delete t[Pa],delete t[Ng],delete t[Pg])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Ep(e){return e.tag===5||e.tag===3||e.tag===4}function yf(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Ep(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Ha(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=El));else if(r!==4&&(e=e.child,e!==null))for(Ha(e,t,n),e=e.sibling;e!==null;)Ha(e,t,n),e=e.sibling}function Wa(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Wa(e,t,n),e=e.sibling;e!==null;)Wa(e,t,n),e=e.sibling}var Je=null,Wt=!1;function bn(e,t,n){for(n=n.child;n!==null;)Tp(e,t,n),n=n.sibling}function Tp(e,t,n){if(rn&&typeof rn.onCommitFiberUnmount=="function")try{rn.onCommitFiberUnmount(Hl,n)}catch{}switch(n.tag){case 5:it||Vr(n,t);case 6:var r=Je,o=Wt;Je=null,bn(e,t,n),Je=r,Wt=o,Je!==null&&(Wt?(e=Je,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Je.removeChild(n.stateNode));break;case 18:Je!==null&&(Wt?(e=Je,n=n.stateNode,e.nodeType===8?Vs(e.parentNode,n):e.nodeType===1&&Vs(e,n),Zo(e)):Vs(Je,n.stateNode));break;case 4:r=Je,o=Wt,Je=n.stateNode.containerInfo,Wt=!0,bn(e,t,n),Je=r,Wt=o;break;case 0:case 11:case 14:case 15:if(!it&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var i=o,a=i.destroy;i=i.tag,a!==void 0&&(i&2||i&4)&&$a(n,t,a),o=o.next}while(o!==r)}bn(e,t,n);break;case 1:if(!it&&(Vr(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(c){Fe(n,t,c)}bn(e,t,n);break;case 21:bn(e,t,n);break;case 22:n.mode&1?(it=(r=it)||n.memoizedState!==null,bn(e,t,n),it=r):bn(e,t,n);break;default:bn(e,t,n)}}function vf(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Wg),t.forEach(function(r){var o=ey.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function Bt(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var i=e,a=t,c=a;e:for(;c!==null;){switch(c.tag){case 5:Je=c.stateNode,Wt=!1;break e;case 3:Je=c.stateNode.containerInfo,Wt=!0;break e;case 4:Je=c.stateNode.containerInfo,Wt=!0;break e}c=c.return}if(Je===null)throw Error(F(160));Tp(i,a,o),Je=null,Wt=!1;var f=o.alternate;f!==null&&(f.return=null),o.return=null}catch(p){Fe(o,t,p)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Np(t,e),t=t.sibling}function Np(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Bt(t,e),en(e),r&4){try{Wo(3,e,e.return),Xl(3,e)}catch(N){Fe(e,e.return,N)}try{Wo(5,e,e.return)}catch(N){Fe(e,e.return,N)}}break;case 1:Bt(t,e),en(e),r&512&&n!==null&&Vr(n,n.return);break;case 5:if(Bt(t,e),en(e),r&512&&n!==null&&Vr(n,n.return),e.flags&32){var o=e.stateNode;try{Ko(o,"")}catch(N){Fe(e,e.return,N)}}if(r&4&&(o=e.stateNode,o!=null)){var i=e.memoizedProps,a=n!==null?n.memoizedProps:i,c=e.type,f=e.updateQueue;if(e.updateQueue=null,f!==null)try{c==="input"&&i.type==="radio"&&i.name!=null&&Gf(o,i),ha(c,a);var p=ha(c,i);for(a=0;a<f.length;a+=2){var w=f[a],_=f[a+1];w==="style"?ed(o,_):w==="dangerouslySetInnerHTML"?Zf(o,_):w==="children"?Ko(o,_):ru(o,w,_,p)}switch(c){case"input":ua(o,i);break;case"textarea":Yf(o,i);break;case"select":var v=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var E=i.value;E!=null?Gr(o,!!i.multiple,E,!1):v!==!!i.multiple&&(i.defaultValue!=null?Gr(o,!!i.multiple,i.defaultValue,!0):Gr(o,!!i.multiple,i.multiple?[]:"",!1))}o[ri]=i}catch(N){Fe(e,e.return,N)}}break;case 6:if(Bt(t,e),en(e),r&4){if(e.stateNode===null)throw Error(F(162));o=e.stateNode,i=e.memoizedProps;try{o.nodeValue=i}catch(N){Fe(e,e.return,N)}}break;case 3:if(Bt(t,e),en(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Zo(t.containerInfo)}catch(N){Fe(e,e.return,N)}break;case 4:Bt(t,e),en(e);break;case 13:Bt(t,e),en(e),o=e.child,o.flags&8192&&(i=o.memoizedState!==null,o.stateNode.isHidden=i,!i||o.alternate!==null&&o.alternate.memoizedState!==null||(zu=Re())),r&4&&vf(e);break;case 22:if(w=n!==null&&n.memoizedState!==null,e.mode&1?(it=(p=it)||w,Bt(t,e),it=p):Bt(t,e),en(e),r&8192){if(p=e.memoizedState!==null,(e.stateNode.isHidden=p)&&!w&&e.mode&1)for(Y=e,w=e.child;w!==null;){for(_=Y=w;Y!==null;){switch(v=Y,E=v.child,v.tag){case 0:case 11:case 14:case 15:Wo(4,v,v.return);break;case 1:Vr(v,v.return);var k=v.stateNode;if(typeof k.componentWillUnmount=="function"){r=v,n=v.return;try{t=r,k.props=t.memoizedProps,k.state=t.memoizedState,k.componentWillUnmount()}catch(N){Fe(r,n,N)}}break;case 5:Vr(v,v.return);break;case 22:if(v.memoizedState!==null){kf(_);continue}}E!==null?(E.return=v,Y=E):kf(_)}w=w.sibling}e:for(w=null,_=e;;){if(_.tag===5){if(w===null){w=_;try{o=_.stateNode,p?(i=o.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(c=_.stateNode,f=_.memoizedProps.style,a=f!=null&&f.hasOwnProperty("display")?f.display:null,c.style.display=Jf("display",a))}catch(N){Fe(e,e.return,N)}}}else if(_.tag===6){if(w===null)try{_.stateNode.nodeValue=p?"":_.memoizedProps}catch(N){Fe(e,e.return,N)}}else if((_.tag!==22&&_.tag!==23||_.memoizedState===null||_===e)&&_.child!==null){_.child.return=_,_=_.child;continue}if(_===e)break e;for(;_.sibling===null;){if(_.return===null||_.return===e)break e;w===_&&(w=null),_=_.return}w===_&&(w=null),_.sibling.return=_.return,_=_.sibling}}break;case 19:Bt(t,e),en(e),r&4&&vf(e);break;case 21:break;default:Bt(t,e),en(e)}}function en(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Ep(n)){var r=n;break e}n=n.return}throw Error(F(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(Ko(o,""),r.flags&=-33);var i=yf(e);Wa(e,i,o);break;case 3:case 4:var a=r.stateNode.containerInfo,c=yf(e);Ha(e,c,a);break;default:throw Error(F(161))}}catch(f){Fe(e,e.return,f)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Qg(e,t,n){Y=e,Pp(e)}function Pp(e,t,n){for(var r=(e.mode&1)!==0;Y!==null;){var o=Y,i=o.child;if(o.tag===22&&r){var a=o.memoizedState!==null||el;if(!a){var c=o.alternate,f=c!==null&&c.memoizedState!==null||it;c=el;var p=it;if(el=a,(it=f)&&!p)for(Y=o;Y!==null;)a=Y,f=a.child,a.tag===22&&a.memoizedState!==null?Sf(o):f!==null?(f.return=a,Y=f):Sf(o);for(;i!==null;)Y=i,Pp(i),i=i.sibling;Y=o,el=c,it=p}wf(e)}else o.subtreeFlags&8772&&i!==null?(i.return=o,Y=i):wf(e)}}function wf(e){for(;Y!==null;){var t=Y;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:it||Xl(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!it)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:Ht(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&nf(t,i,r);break;case 3:var a=t.updateQueue;if(a!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}nf(t,a,n)}break;case 5:var c=t.stateNode;if(n===null&&t.flags&4){n=c;var f=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":f.autoFocus&&n.focus();break;case"img":f.src&&(n.src=f.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var p=t.alternate;if(p!==null){var w=p.memoizedState;if(w!==null){var _=w.dehydrated;_!==null&&Zo(_)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(F(163))}it||t.flags&512&&Ba(t)}catch(v){Fe(t,t.return,v)}}if(t===e){Y=null;break}if(n=t.sibling,n!==null){n.return=t.return,Y=n;break}Y=t.return}}function kf(e){for(;Y!==null;){var t=Y;if(t===e){Y=null;break}var n=t.sibling;if(n!==null){n.return=t.return,Y=n;break}Y=t.return}}function Sf(e){for(;Y!==null;){var t=Y;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Xl(4,t)}catch(f){Fe(t,n,f)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(f){Fe(t,o,f)}}var i=t.return;try{Ba(t)}catch(f){Fe(t,i,f)}break;case 5:var a=t.return;try{Ba(t)}catch(f){Fe(t,a,f)}}}catch(f){Fe(t,t.return,f)}if(t===e){Y=null;break}var c=t.sibling;if(c!==null){c.return=t.return,Y=c;break}Y=t.return}}var qg=Math.ceil,zl=kn.ReactCurrentDispatcher,Mu=kn.ReactCurrentOwner,It=kn.ReactCurrentBatchConfig,ge=0,Ze=null,Ve=null,et=0,kt=0,Qr=Yn(0),Ke=0,ui=null,kr=0,Zl=0,Au=0,Vo=null,pt=null,zu=0,so=1/0,fn=null,Dl=!1,Va=null,Wn=null,tl=!1,Fn=null,Fl=0,Qo=0,Qa=null,pl=-1,hl=0;function at(){return ge&6?Re():pl!==-1?pl:pl=Re()}function Vn(e){return e.mode&1?ge&2&&et!==0?et&-et:Ig.transition!==null?(hl===0&&(hl=dd()),hl):(e=Se,e!==0||(e=window.event,e=e===void 0?16:wd(e.type)),e):1}function qt(e,t,n,r){if(50<Qo)throw Qo=0,Qa=null,Error(F(185));fi(e,n,r),(!(ge&2)||e!==Ze)&&(e===Ze&&(!(ge&2)&&(Zl|=n),Ke===4&&zn(e,et)),yt(e,r),n===1&&ge===0&&!(t.mode&1)&&(so=Re()+500,Kl&&Xn()))}function yt(e,t){var n=e.callbackNode;Im(e,t);var r=Sl(e,e===Ze?et:0);if(r===0)n!==null&&Oc(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Oc(n),t===1)e.tag===0?Og(_f.bind(null,e)):Fd(_f.bind(null,e)),Eg(function(){!(ge&6)&&Xn()}),n=null;else{switch(pd(r)){case 1:n=au;break;case 4:n=cd;break;case 16:n=kl;break;case 536870912:n=fd;break;default:n=kl}n=zp(n,Op.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Op(e,t){if(pl=-1,hl=0,ge&6)throw Error(F(327));var n=e.callbackNode;if(eo()&&e.callbackNode!==n)return null;var r=Sl(e,e===Ze?et:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Rl(e,r);else{t=r;var o=ge;ge|=2;var i=Lp();(Ze!==e||et!==t)&&(fn=null,so=Re()+500,mr(e,t));do try{Yg();break}catch(c){Ip(e,c)}while(1);Su(),zl.current=i,ge=o,Ve!==null?t=0:(Ze=null,et=0,t=Ke)}if(t!==0){if(t===2&&(o=wa(e),o!==0&&(r=o,t=qa(e,o))),t===1)throw n=ui,mr(e,0),zn(e,r),yt(e,Re()),n;if(t===6)zn(e,r);else{if(o=e.current.alternate,!(r&30)&&!Kg(o)&&(t=Rl(e,r),t===2&&(i=wa(e),i!==0&&(r=i,t=qa(e,i))),t===1))throw n=ui,mr(e,0),zn(e,r),yt(e,Re()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(F(345));case 2:fr(e,pt,fn);break;case 3:if(zn(e,r),(r&130023424)===r&&(t=zu+500-Re(),10<t)){if(Sl(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){at(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=Na(fr.bind(null,e,pt,fn),t);break}fr(e,pt,fn);break;case 4:if(zn(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var a=31-Qt(r);i=1<<a,a=t[a],a>o&&(o=a),r&=~i}if(r=o,r=Re()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*qg(r/1960))-r,10<r){e.timeoutHandle=Na(fr.bind(null,e,pt,fn),r);break}fr(e,pt,fn);break;case 5:fr(e,pt,fn);break;default:throw Error(F(329))}}}return yt(e,Re()),e.callbackNode===n?Op.bind(null,e):null}function qa(e,t){var n=Vo;return e.current.memoizedState.isDehydrated&&(mr(e,t).flags|=256),e=Rl(e,t),e!==2&&(t=pt,pt=n,t!==null&&Ka(t)),e}function Ka(e){pt===null?pt=e:pt.push.apply(pt,e)}function Kg(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],i=o.getSnapshot;o=o.value;try{if(!Kt(i(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function zn(e,t){for(t&=~Au,t&=~Zl,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Qt(t),r=1<<n;e[n]=-1,t&=~r}}function _f(e){if(ge&6)throw Error(F(327));eo();var t=Sl(e,0);if(!(t&1))return yt(e,Re()),null;var n=Rl(e,t);if(e.tag!==0&&n===2){var r=wa(e);r!==0&&(t=r,n=qa(e,r))}if(n===1)throw n=ui,mr(e,0),zn(e,t),yt(e,Re()),n;if(n===6)throw Error(F(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,fr(e,pt,fn),yt(e,Re()),null}function Du(e,t){var n=ge;ge|=1;try{return e(t)}finally{ge=n,ge===0&&(so=Re()+500,Kl&&Xn())}}function Sr(e){Fn!==null&&Fn.tag===0&&!(ge&6)&&eo();var t=ge;ge|=1;var n=It.transition,r=Se;try{if(It.transition=null,Se=1,e)return e()}finally{Se=r,It.transition=n,ge=t,!(ge&6)&&Xn()}}function Fu(){kt=Qr.current,Ne(Qr)}function mr(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,xg(n)),Ve!==null)for(n=Ve.return;n!==null;){var r=n;switch(vu(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Tl();break;case 3:io(),Ne(mt),Ne(lt),Nu();break;case 5:Tu(r);break;case 4:io();break;case 13:Ne(be);break;case 19:Ne(be);break;case 10:_u(r.type._context);break;case 22:case 23:Fu()}n=n.return}if(Ze=e,Ve=e=Qn(e.current,null),et=kt=t,Ke=0,ui=null,Au=Zl=kr=0,pt=Vo=null,pr!==null){for(t=0;t<pr.length;t++)if(n=pr[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,i=n.pending;if(i!==null){var a=i.next;i.next=o,r.next=a}n.pending=r}pr=null}return e}function Ip(e,t){do{var n=Ve;try{if(Su(),cl.current=Al,Ml){for(var r=je.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}Ml=!1}if(wr=0,Xe=qe=je=null,Ho=!1,li=0,Mu.current=null,n===null||n.return===null){Ke=1,ui=t,Ve=null;break}e:{var i=e,a=n.return,c=n,f=t;if(t=et,c.flags|=32768,f!==null&&typeof f=="object"&&typeof f.then=="function"){var p=f,w=c,_=w.tag;if(!(w.mode&1)&&(_===0||_===11||_===15)){var v=w.alternate;v?(w.updateQueue=v.updateQueue,w.memoizedState=v.memoizedState,w.lanes=v.lanes):(w.updateQueue=null,w.memoizedState=null)}var E=uf(a);if(E!==null){E.flags&=-257,cf(E,a,c,i,t),E.mode&1&&af(i,p,t),t=E,f=p;var k=t.updateQueue;if(k===null){var N=new Set;N.add(f),t.updateQueue=N}else k.add(f);break e}else{if(!(t&1)){af(i,p,t),Ru();break e}f=Error(F(426))}}else if(Ie&&c.mode&1){var L=uf(a);if(L!==null){!(L.flags&65536)&&(L.flags|=256),cf(L,a,c,i,t),wu(lo(f,c));break e}}i=f=lo(f,c),Ke!==4&&(Ke=2),Vo===null?Vo=[i]:Vo.push(i),i=a;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var h=pp(i,f,t);tf(i,h);break e;case 1:c=f;var d=i.type,g=i.stateNode;if(!(i.flags&128)&&(typeof d.getDerivedStateFromError=="function"||g!==null&&typeof g.componentDidCatch=="function"&&(Wn===null||!Wn.has(g)))){i.flags|=65536,t&=-t,i.lanes|=t;var C=hp(i,c,t);tf(i,C);break e}}i=i.return}while(i!==null)}jp(n)}catch(I){t=I,Ve===n&&n!==null&&(Ve=n=n.return);continue}break}while(1)}function Lp(){var e=zl.current;return zl.current=Al,e===null?Al:e}function Ru(){(Ke===0||Ke===3||Ke===2)&&(Ke=4),Ze===null||!(kr&268435455)&&!(Zl&268435455)||zn(Ze,et)}function Rl(e,t){var n=ge;ge|=2;var r=Lp();(Ze!==e||et!==t)&&(fn=null,mr(e,t));do try{Gg();break}catch(o){Ip(e,o)}while(1);if(Su(),ge=n,zl.current=r,Ve!==null)throw Error(F(261));return Ze=null,et=0,Ke}function Gg(){for(;Ve!==null;)bp(Ve)}function Yg(){for(;Ve!==null&&!Sm();)bp(Ve)}function bp(e){var t=Ap(e.alternate,e,kt);e.memoizedProps=e.pendingProps,t===null?jp(e):Ve=t,Mu.current=null}function jp(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Hg(n,t),n!==null){n.flags&=32767,Ve=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Ke=6,Ve=null;return}}else if(n=Bg(n,t,kt),n!==null){Ve=n;return}if(t=t.sibling,t!==null){Ve=t;return}Ve=t=e}while(t!==null);Ke===0&&(Ke=5)}function fr(e,t,n){var r=Se,o=It.transition;try{It.transition=null,Se=1,Xg(e,t,n,r)}finally{It.transition=o,Se=r}return null}function Xg(e,t,n,r){do eo();while(Fn!==null);if(ge&6)throw Error(F(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(F(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(Lm(e,i),e===Ze&&(Ve=Ze=null,et=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||tl||(tl=!0,zp(kl,function(){return eo(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=It.transition,It.transition=null;var a=Se;Se=1;var c=ge;ge|=4,Mu.current=null,Vg(e,n),Np(n,e),yg(Ea),_l=!!xa,Ea=xa=null,e.current=n,Qg(n),_m(),ge=c,Se=a,It.transition=i}else e.current=n;if(tl&&(tl=!1,Fn=e,Fl=o),i=e.pendingLanes,i===0&&(Wn=null),Em(n.stateNode),yt(e,Re()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(Dl)throw Dl=!1,e=Va,Va=null,e;return Fl&1&&e.tag!==0&&eo(),i=e.pendingLanes,i&1?e===Qa?Qo++:(Qo=0,Qa=e):Qo=0,Xn(),null}function eo(){if(Fn!==null){var e=pd(Fl),t=It.transition,n=Se;try{if(It.transition=null,Se=16>e?16:e,Fn===null)var r=!1;else{if(e=Fn,Fn=null,Fl=0,ge&6)throw Error(F(331));var o=ge;for(ge|=4,Y=e.current;Y!==null;){var i=Y,a=i.child;if(Y.flags&16){var c=i.deletions;if(c!==null){for(var f=0;f<c.length;f++){var p=c[f];for(Y=p;Y!==null;){var w=Y;switch(w.tag){case 0:case 11:case 15:Wo(8,w,i)}var _=w.child;if(_!==null)_.return=w,Y=_;else for(;Y!==null;){w=Y;var v=w.sibling,E=w.return;if(xp(w),w===p){Y=null;break}if(v!==null){v.return=E,Y=v;break}Y=E}}}var k=i.alternate;if(k!==null){var N=k.child;if(N!==null){k.child=null;do{var L=N.sibling;N.sibling=null,N=L}while(N!==null)}}Y=i}}if(i.subtreeFlags&2064&&a!==null)a.return=i,Y=a;else e:for(;Y!==null;){if(i=Y,i.flags&2048)switch(i.tag){case 0:case 11:case 15:Wo(9,i,i.return)}var h=i.sibling;if(h!==null){h.return=i.return,Y=h;break e}Y=i.return}}var d=e.current;for(Y=d;Y!==null;){a=Y;var g=a.child;if(a.subtreeFlags&2064&&g!==null)g.return=a,Y=g;else e:for(a=d;Y!==null;){if(c=Y,c.flags&2048)try{switch(c.tag){case 0:case 11:case 15:Xl(9,c)}}catch(I){Fe(c,c.return,I)}if(c===a){Y=null;break e}var C=c.sibling;if(C!==null){C.return=c.return,Y=C;break e}Y=c.return}}if(ge=o,Xn(),rn&&typeof rn.onPostCommitFiberRoot=="function")try{rn.onPostCommitFiberRoot(Hl,e)}catch{}r=!0}return r}finally{Se=n,It.transition=t}}return!1}function Cf(e,t,n){t=lo(n,t),t=pp(e,t,1),e=Hn(e,t,1),t=at(),e!==null&&(fi(e,1,t),yt(e,t))}function Fe(e,t,n){if(e.tag===3)Cf(e,e,n);else for(;t!==null;){if(t.tag===3){Cf(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Wn===null||!Wn.has(r))){e=lo(n,e),e=hp(t,e,1),t=Hn(t,e,1),e=at(),t!==null&&(fi(t,1,e),yt(t,e));break}}t=t.return}}function Zg(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=at(),e.pingedLanes|=e.suspendedLanes&n,Ze===e&&(et&n)===n&&(Ke===4||Ke===3&&(et&130023424)===et&&500>Re()-zu?mr(e,0):Au|=n),yt(e,t)}function Mp(e,t){t===0&&(e.mode&1?(t=Vi,Vi<<=1,!(Vi&130023424)&&(Vi=4194304)):t=1);var n=at();e=vn(e,t),e!==null&&(fi(e,t,n),yt(e,n))}function Jg(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Mp(e,n)}function ey(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(F(314))}r!==null&&r.delete(t),Mp(e,n)}var Ap;Ap=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||mt.current)ht=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return ht=!1,$g(e,t,n);ht=!!(e.flags&131072)}else ht=!1,Ie&&t.flags&1048576&&Rd(t,Ol,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;dl(e,t),e=t.pendingProps;var o=no(t,lt.current);Jr(t,n),o=Ou(null,t,r,e,o,n);var i=Iu();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,gt(r)?(i=!0,Nl(t)):i=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,xu(t),o.updater=Yl,t.stateNode=o,o._reactInternals=t,Ma(t,r,e,n),t=Da(null,t,r,!0,i,n)):(t.tag=0,Ie&&i&&yu(t),st(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(dl(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=ny(r),e=Ht(r,e),o){case 0:t=za(null,t,r,e,n);break e;case 1:t=pf(null,t,r,e,n);break e;case 11:t=ff(null,t,r,e,n);break e;case 14:t=df(null,t,r,Ht(r.type,e),n);break e}throw Error(F(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ht(r,o),za(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ht(r,o),pf(e,t,r,o,n);case 3:e:{if(vp(t),e===null)throw Error(F(387));r=t.pendingProps,i=t.memoizedState,o=i.element,Vd(e,t),bl(t,r,null,n);var a=t.memoizedState;if(r=a.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:a.cache,pendingSuspenseBoundaries:a.pendingSuspenseBoundaries,transitions:a.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){o=lo(Error(F(423)),t),t=hf(e,t,r,n,o);break e}else if(r!==o){o=lo(Error(F(424)),t),t=hf(e,t,r,n,o);break e}else for(St=Bn(t.stateNode.containerInfo.firstChild),_t=t,Ie=!0,Vt=null,n=Hd(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(ro(),r===o){t=wn(e,t,n);break e}st(e,t,r,n)}t=t.child}return t;case 5:return Qd(t),e===null&&La(t),r=t.type,o=t.pendingProps,i=e!==null?e.memoizedProps:null,a=o.children,Ta(r,o)?a=null:i!==null&&Ta(r,i)&&(t.flags|=32),yp(e,t),st(e,t,a,n),t.child;case 6:return e===null&&La(t),null;case 13:return wp(e,t,n);case 4:return Eu(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=oo(t,null,r,n):st(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ht(r,o),ff(e,t,r,o,n);case 7:return st(e,t,t.pendingProps,n),t.child;case 8:return st(e,t,t.pendingProps.children,n),t.child;case 12:return st(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,i=t.memoizedProps,a=o.value,Ee(Il,r._currentValue),r._currentValue=a,i!==null)if(Kt(i.value,a)){if(i.children===o.children&&!mt.current){t=wn(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var c=i.dependencies;if(c!==null){a=i.child;for(var f=c.firstContext;f!==null;){if(f.context===r){if(i.tag===1){f=mn(-1,n&-n),f.tag=2;var p=i.updateQueue;if(p!==null){p=p.shared;var w=p.pending;w===null?f.next=f:(f.next=w.next,w.next=f),p.pending=f}}i.lanes|=n,f=i.alternate,f!==null&&(f.lanes|=n),ba(i.return,n,t),c.lanes|=n;break}f=f.next}}else if(i.tag===10)a=i.type===t.type?null:i.child;else if(i.tag===18){if(a=i.return,a===null)throw Error(F(341));a.lanes|=n,c=a.alternate,c!==null&&(c.lanes|=n),ba(a,n,t),a=i.sibling}else a=i.child;if(a!==null)a.return=i;else for(a=i;a!==null;){if(a===t){a=null;break}if(i=a.sibling,i!==null){i.return=a.return,a=i;break}a=a.return}i=a}st(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,Jr(t,n),o=Lt(o),r=r(o),t.flags|=1,st(e,t,r,n),t.child;case 14:return r=t.type,o=Ht(r,t.pendingProps),o=Ht(r.type,o),df(e,t,r,o,n);case 15:return mp(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ht(r,o),dl(e,t),t.tag=1,gt(r)?(e=!0,Nl(t)):e=!1,Jr(t,n),dp(t,r,o),Ma(t,r,o,n),Da(null,t,r,!0,e,n);case 19:return kp(e,t,n);case 22:return gp(e,t,n)}throw Error(F(156,t.tag))};function zp(e,t){return ud(e,t)}function ty(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ot(e,t,n,r){return new ty(e,t,n,r)}function Uu(e){return e=e.prototype,!(!e||!e.isReactComponent)}function ny(e){if(typeof e=="function")return Uu(e)?1:0;if(e!=null){if(e=e.$$typeof,e===iu)return 11;if(e===lu)return 14}return 2}function Qn(e,t){var n=e.alternate;return n===null?(n=Ot(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function ml(e,t,n,r,o,i){var a=2;if(r=e,typeof e=="function")Uu(e)&&(a=1);else if(typeof e=="string")a=5;else e:switch(e){case zr:return gr(n.children,o,i,t);case ou:a=8,o|=8;break;case oa:return e=Ot(12,n,t,o|2),e.elementType=oa,e.lanes=i,e;case ia:return e=Ot(13,n,t,o),e.elementType=ia,e.lanes=i,e;case la:return e=Ot(19,n,t,o),e.elementType=la,e.lanes=i,e;case Qf:return Jl(n,o,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Wf:a=10;break e;case Vf:a=9;break e;case iu:a=11;break e;case lu:a=14;break e;case jn:a=16,r=null;break e}throw Error(F(130,e==null?e:typeof e,""))}return t=Ot(a,n,t,o),t.elementType=e,t.type=r,t.lanes=i,t}function gr(e,t,n,r){return e=Ot(7,e,r,t),e.lanes=n,e}function Jl(e,t,n,r){return e=Ot(22,e,r,t),e.elementType=Qf,e.lanes=n,e.stateNode={isHidden:!1},e}function Js(e,t,n){return e=Ot(6,e,null,t),e.lanes=n,e}function ea(e,t,n){return t=Ot(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function ry(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Ms(0),this.expirationTimes=Ms(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ms(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function $u(e,t,n,r,o,i,a,c,f){return e=new ry(e,t,n,c,f),t===1?(t=1,i===!0&&(t|=8)):t=0,i=Ot(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},xu(i),e}function oy(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Ar,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Dp(e){if(!e)return Kn;e=e._reactInternals;e:{if(Cr(e)!==e||e.tag!==1)throw Error(F(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(gt(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(F(171))}if(e.tag===1){var n=e.type;if(gt(n))return Dd(e,n,t)}return t}function Fp(e,t,n,r,o,i,a,c,f){return e=$u(n,r,!0,e,o,i,a,c,f),e.context=Dp(null),n=e.current,r=at(),o=Vn(n),i=mn(r,o),i.callback=t??null,Hn(n,i,o),e.current.lanes=o,fi(e,o,r),yt(e,r),e}function es(e,t,n,r){var o=t.current,i=at(),a=Vn(o);return n=Dp(n),t.context===null?t.context=n:t.pendingContext=n,t=mn(i,a),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Hn(o,t,a),e!==null&&(qt(e,o,a,i),ul(e,o,a)),a}function Ul(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function xf(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Bu(e,t){xf(e,t),(e=e.alternate)&&xf(e,t)}function iy(){return null}var Rp=typeof reportError=="function"?reportError:function(e){console.error(e)};function Hu(e){this._internalRoot=e}ts.prototype.render=Hu.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(F(409));es(e,t,null,null)};ts.prototype.unmount=Hu.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Sr(function(){es(null,e,null,null)}),t[yn]=null}};function ts(e){this._internalRoot=e}ts.prototype.unstable_scheduleHydration=function(e){if(e){var t=gd();e={blockedOn:null,target:e,priority:t};for(var n=0;n<An.length&&t!==0&&t<An[n].priority;n++);An.splice(n,0,e),n===0&&vd(e)}};function Wu(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function ns(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Ef(){}function ly(e,t,n,r,o){if(o){if(typeof r=="function"){var i=r;r=function(){var p=Ul(a);i.call(p)}}var a=Fp(t,r,e,0,null,!1,!1,"",Ef);return e._reactRootContainer=a,e[yn]=a.current,ti(e.nodeType===8?e.parentNode:e),Sr(),a}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var c=r;r=function(){var p=Ul(f);c.call(p)}}var f=$u(e,0,!1,null,null,!1,!1,"",Ef);return e._reactRootContainer=f,e[yn]=f.current,ti(e.nodeType===8?e.parentNode:e),Sr(function(){es(t,f,n,r)}),f}function rs(e,t,n,r,o){var i=n._reactRootContainer;if(i){var a=i;if(typeof o=="function"){var c=o;o=function(){var f=Ul(a);c.call(f)}}es(t,a,e,o)}else a=ly(n,t,e,o,r);return Ul(a)}hd=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=zo(t.pendingLanes);n!==0&&(uu(t,n|1),yt(t,Re()),!(ge&6)&&(so=Re()+500,Xn()))}break;case 13:Sr(function(){var r=vn(e,1);if(r!==null){var o=at();qt(r,e,1,o)}}),Bu(e,1)}};cu=function(e){if(e.tag===13){var t=vn(e,134217728);if(t!==null){var n=at();qt(t,e,134217728,n)}Bu(e,134217728)}};md=function(e){if(e.tag===13){var t=Vn(e),n=vn(e,t);if(n!==null){var r=at();qt(n,e,t,r)}Bu(e,t)}};gd=function(){return Se};yd=function(e,t){var n=Se;try{return Se=e,t()}finally{Se=n}};ga=function(e,t,n){switch(t){case"input":if(ua(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=ql(r);if(!o)throw Error(F(90));Kf(r),ua(r,o)}}}break;case"textarea":Yf(e,n);break;case"select":t=n.value,t!=null&&Gr(e,!!n.multiple,t,!1)}};rd=Du;od=Sr;var sy={usingClientEntryPoint:!1,Events:[pi,Ur,ql,td,nd,Du]},jo={findFiberByHostInstance:dr,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},ay={bundleType:jo.bundleType,version:jo.version,rendererPackageName:jo.rendererPackageName,rendererConfig:jo.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:kn.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=sd(e),e===null?null:e.stateNode},findFiberByHostInstance:jo.findFiberByHostInstance||iy,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var nl=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!nl.isDisabled&&nl.supportsFiber)try{Hl=nl.inject(ay),rn=nl}catch{}}xt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=sy;xt.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Wu(t))throw Error(F(200));return oy(e,t,null,n)};xt.createRoot=function(e,t){if(!Wu(e))throw Error(F(299));var n=!1,r="",o=Rp;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=$u(e,1,!1,null,null,n,!1,r,o),e[yn]=t.current,ti(e.nodeType===8?e.parentNode:e),new Hu(t)};xt.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(F(188)):(e=Object.keys(e).join(","),Error(F(268,e)));return e=sd(t),e=e===null?null:e.stateNode,e};xt.flushSync=function(e){return Sr(e)};xt.hydrate=function(e,t,n){if(!ns(t))throw Error(F(200));return rs(null,e,t,!0,n)};xt.hydrateRoot=function(e,t,n){if(!Wu(e))throw Error(F(405));var r=n!=null&&n.hydratedSources||null,o=!1,i="",a=Rp;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(a=n.onRecoverableError)),t=Fp(t,null,e,1,n??null,o,!1,i,a),e[yn]=t.current,ti(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new ts(t)};xt.render=function(e,t,n){if(!ns(t))throw Error(F(200));return rs(null,e,t,!1,n)};xt.unmountComponentAtNode=function(e){if(!ns(e))throw Error(F(40));return e._reactRootContainer?(Sr(function(){rs(null,null,e,!1,function(){e._reactRootContainer=null,e[yn]=null})}),!0):!1};xt.unstable_batchedUpdates=Du;xt.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!ns(n))throw Error(F(200));if(e==null||e._reactInternals===void 0)throw Error(F(38));return rs(e,t,n,!1,r)};xt.version="18.3.1-next-f1338f8080-20240426";function Up(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Up)}catch(e){console.error(e)}}Up(),Uf.exports=xt;var uy=Uf.exports,$p,Tf=uy;$p=Tf.createRoot,Tf.hydrateRoot;let Bp=logseq.isMainUIVisible;function cy(e,t){return logseq.on(e,t),()=>{logseq.off(e,t)}}const fy=e=>cy("ui:visible:changed",({visible:t})=>{Bp=t,e()}),dy=()=>Ff.useSyncExternalStore(fy,()=>Bp),qr={apiUrl:"https://api.openai.com/v1/chat/completions",apiKey:"",modelName:"gpt-3.5-turbo",temperature:.7,maxTokens:2e3,enableHistory:!1,customPrompts:[{name:"总结",prompt:"请总结以下内容的要点："},{name:"扩展",prompt:"请基于以下内容进行扩展和补充："}]};async function py(){const e=logseq.settings||{},t=Object.assign({},qr,e);return Array.isArray(t.customPrompts)||(t.customPrompts=qr.customPrompts),t.temperature===void 0&&(t.temperature=qr.temperature),t.maxTokens===void 0&&(t.maxTokens=qr.maxTokens),await logseq.updateSettings(t),t}async function hy(e){await logseq.updateSettings(e)}async function Vu(){const e=logseq.settings||{};return Object.assign({},qr,e)}class Kr{static instance;currentTheme;listeners=[];mediaQuery;constructor(){this.mediaQuery=window.matchMedia("(prefers-color-scheme: dark)"),this.currentTheme={mode:"auto",isDark:!1,systemPrefersDark:this.mediaQuery.matches},this.mediaQuery.addEventListener("change",this.handleSystemThemeChange.bind(this)),this.initializeTheme()}static getInstance(){return Kr.instance||(Kr.instance=new Kr),Kr.instance}async initializeTheme(){try{const t=await logseq.App.getStateFromStore("ui/theme");this.currentTheme.isDark=t==="dark",this.currentTheme.mode=t==="dark"?"dark":"light",this.applyTheme(),logseq.App.onThemeModeChanged(({mode:n})=>{this.currentTheme.isDark=n==="dark",this.currentTheme.mode=n==="dark"?"dark":"light",this.applyTheme(),this.notifyListeners()})}catch(t){console.error("主题初始化失败:",t),this.currentTheme.isDark=this.currentTheme.systemPrefersDark,this.applyTheme()}}handleSystemThemeChange=t=>{this.currentTheme.systemPrefersDark=t.matches,this.currentTheme.mode==="auto"&&(this.currentTheme.isDark=t.matches,this.applyTheme(),this.notifyListeners())};applyTheme(){const t=document.documentElement;this.currentTheme.isDark?t.classList.add("dark"):t.classList.remove("dark"),this.setCSSVariables()}setCSSVariables(){const t=document.documentElement;this.currentTheme.isDark?(t.style.setProperty("--theme-bg-primary","#1a1a1a"),t.style.setProperty("--theme-bg-secondary","#2a2a2a"),t.style.setProperty("--theme-bg-tertiary","#3a3a3a"),t.style.setProperty("--theme-text-primary","#e0e0e0"),t.style.setProperty("--theme-text-secondary","#a0a0a0"),t.style.setProperty("--theme-text-muted","#666666"),t.style.setProperty("--theme-border-primary","#404040"),t.style.setProperty("--theme-border-secondary","#505050"),t.style.setProperty("--theme-accent","#0A84FF"),t.style.setProperty("--theme-accent-hover","#0066CC")):(t.style.setProperty("--theme-bg-primary","#ffffff"),t.style.setProperty("--theme-bg-secondary","#f8f9fa"),t.style.setProperty("--theme-bg-tertiary","#e9ecef"),t.style.setProperty("--theme-text-primary","#333333"),t.style.setProperty("--theme-text-secondary","#666666"),t.style.setProperty("--theme-text-muted","#999999"),t.style.setProperty("--theme-border-primary","#e0e0e0"),t.style.setProperty("--theme-border-secondary","#f0f0f0"),t.style.setProperty("--theme-accent","#007AFF"),t.style.setProperty("--theme-accent-hover","#0056CC"))}getTheme(){return{...this.currentTheme}}setTheme(t){switch(this.currentTheme.mode=t,t){case"light":this.currentTheme.isDark=!1;break;case"dark":this.currentTheme.isDark=!0;break;case"auto":this.currentTheme.isDark=this.currentTheme.systemPrefersDark;break}this.applyTheme(),this.notifyListeners()}toggleTheme(){const t=this.currentTheme.isDark?"light":"dark";this.setTheme(t)}addListener(t){this.listeners.push(t)}removeListener(t){const n=this.listeners.indexOf(t);n>-1&&this.listeners.splice(n,1)}notifyListeners(){this.listeners.forEach(t=>{try{t(this.getTheme())}catch(n){console.error("主题监听器执行失败:",n)}})}getThemeClasses(){const t=["theme-transition"];return this.currentTheme.isDark&&t.push("dark"),t.join(" ")}isDarkMode(){return this.currentTheme.isDark}getThemeColor(t){const n=this.currentTheme.isDark?{primary:"#e0e0e0",secondary:"#a0a0a0",muted:"#666666",accent:"#0A84FF",background:"#1a1a1a",surface:"#2a2a2a",border:"#404040"}:{primary:"#333333",secondary:"#666666",muted:"#999999",accent:"#007AFF",background:"#ffffff",surface:"#f8f9fa",border:"#e0e0e0"};return n[t]||n.primary}destroy(){this.mediaQuery.removeEventListener("change",this.handleSystemThemeChange),this.listeners=[]}}const fo=()=>Kr.getInstance(),my=({className:e="",showLabel:t=!0})=>{const n=fo(),[r,o]=ae.useState(n.getTheme());ae.useEffect(()=>{const f=p=>{o(p)};return n.addListener(f),()=>{n.removeListener(f)}},[n]);const i=f=>{n.setTheme(f)},a=f=>{switch(f){case"light":return"☀️";case"dark":return"🌙";case"auto":return"🔄";default:return"☀️"}},c=f=>{switch(f){case"light":return"亮色模式";case"dark":return"暗色模式";case"auto":return"跟随系统";default:return"亮色模式"}};return he("div",{className:`theme-toggle ${e}`,children:[t&&W("label",{className:"block text-sm font-medium mb-2",style:{color:n.getThemeColor("primary")},children:"主题设置"}),W("div",{className:"flex items-center space-x-2",children:["light","dark","auto"].map(f=>he("button",{onClick:()=>i(f),className:`
              flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200
              ${r.mode===f?"ring-2 ring-offset-2":"hover:opacity-80"}
            `,style:{backgroundColor:r.mode===f?n.getThemeColor("accent"):n.getThemeColor("surface"),color:r.mode===f?"#ffffff":n.getThemeColor("primary")},children:[W("span",{className:"text-base",children:a(f)}),W("span",{children:c(f)})]},f))}),r.mode==="auto"&&he("div",{className:"mt-2 text-xs opacity-75",style:{color:n.getThemeColor("secondary")},children:["当前: ",r.isDark?"暗色":"亮色",r.systemPrefersDark?" (系统偏好暗色)":" (系统偏好亮色)"]})]})},gy=({className:e=""})=>{const t=fo(),[n,r]=ae.useState(t.getTheme());return ae.useEffect(()=>{const o=i=>{r(i)};return t.addListener(o),()=>{t.removeListener(o)}},[t]),he("div",{className:`flex items-center space-x-2 text-sm ${e}`,style:{color:t.getThemeColor("secondary")},children:[W("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:n.isDark?"#4A90E2":"#F5A623"}}),W("span",{children:n.mode==="auto"?`自动 (${n.isDark?"暗色":"亮色"})`:n.isDark?"暗色模式":"亮色模式"})]})},yy=()=>{const[e,t]=ae.useState(qr),[n,r]=ae.useState(!0),[o,i]=ae.useState(!1),[a,c]=ae.useState(""),[f,p]=ae.useState({name:"",prompt:""}),[w,_]=ae.useState(null),v=fo(),[E,k]=ae.useState(v.isDarkMode());ae.useEffect(()=>{const T=R=>{k(R.isDark)};return v.addListener(T),()=>{v.removeListener(T)}},[v]),ae.useEffect(()=>{(async()=>{try{const R=await Vu();t(R)}catch(R){console.error("加载设置出错:",R)}finally{r(!1)}})()},[]),ae.useEffect(()=>{const T=R=>{R.key==="Escape"&&M()};return window.addEventListener("keydown",T),()=>{window.removeEventListener("keydown",T)}},[]);const N=T=>{const{name:R,value:ee}=T.target;t(le=>({...le,[R]:ee}))},L=T=>{const{name:R,checked:ee}=T.target;t(le=>({...le,[R]:ee}))},h=T=>{const{name:R,value:ee}=T.target;p(le=>({...le,[R]:ee}))},d=(T,R)=>{const{name:ee,value:le}=T.target;t(J=>{const _e=[...J.customPrompts];return _e[R]={..._e[R],[ee]:le},{...J,customPrompts:_e}})},g=()=>{!f.name||!f.prompt||(t(T=>({...T,customPrompts:[...T.customPrompts,{...f}]})),p({name:"",prompt:""}))},C=T=>{t(R=>({...R,customPrompts:R.customPrompts.filter((ee,le)=>le!==T)})),w===T&&_(null)},I=T=>{_(T)},j=()=>{_(null)},y=()=>{_(null)},x=async()=>{i(!0),c("");try{await hy(e),c("设置已保存！"),setTimeout(()=>c(""),3e3)}catch(T){console.error("保存设置出错:",T),c("保存设置失败，请重试。")}finally{i(!1)}},M=()=>{window.logseq&&window.logseq.hideMainUI()};return n?W("div",{className:"p-4 text-center",style:{color:v.getThemeColor("primary")},children:"加载中..."}):he("div",{className:"p-4 max-w-2xl mx-auto theme-transition",style:{backgroundColor:v.getThemeColor("background"),color:v.getThemeColor("primary")},children:[he("div",{className:"flex justify-between items-center mb-6",children:[W("h1",{className:"text-2xl font-bold",style:{color:v.getThemeColor("primary")},children:"AI 聊天设置"}),W("button",{onClick:M,className:"p-2 rounded-full transition-colors",style:{color:v.getThemeColor("secondary"),backgroundColor:"transparent"},onMouseEnter:T=>{T.currentTarget.style.backgroundColor=v.getThemeColor("surface")},onMouseLeave:T=>{T.currentTarget.style.backgroundColor="transparent"},title:"关闭设置",children:W("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:W("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),he("div",{className:"mb-6",children:[W("h2",{className:"text-xl font-semibold mb-3",style:{color:v.getThemeColor("primary")},children:"界面设置"}),W("div",{className:"mb-4",children:W(my,{})}),W("div",{className:"mb-4",children:W(gy,{})})]}),he("div",{className:"mb-6",children:[W("h2",{className:"text-xl font-semibold mb-3",style:{color:v.getThemeColor("primary")},children:"API 配置"}),he("div",{className:"mb-4",children:[W("label",{className:"block mb-2 font-medium",style:{color:v.getThemeColor("primary")},children:"API URL"}),W("input",{type:"text",name:"apiUrl",value:e.apiUrl,onChange:N,className:"apple-input w-full p-2 rounded",style:{backgroundColor:v.getThemeColor("surface"),color:v.getThemeColor("primary"),borderColor:v.getThemeColor("border")},placeholder:"https://api.openai.com/v1/chat/completions"})]}),he("div",{className:"mb-4",children:[W("label",{className:"block mb-2 font-medium",style:{color:v.getThemeColor("primary")},children:"API Key"}),W("input",{type:"password",name:"apiKey",value:e.apiKey,onChange:N,className:"apple-input w-full p-2 rounded",style:{backgroundColor:v.getThemeColor("surface"),color:v.getThemeColor("primary"),borderColor:v.getThemeColor("border")},placeholder:"sk-..."})]}),he("div",{className:"mb-4",children:[W("label",{className:"block mb-2 font-medium",style:{color:v.getThemeColor("primary")},children:"模型名称"}),W("input",{type:"text",name:"modelName",value:e.modelName,onChange:N,className:"apple-input w-full p-2 rounded",style:{backgroundColor:v.getThemeColor("surface"),color:v.getThemeColor("primary"),borderColor:v.getThemeColor("border")},placeholder:"gpt-3.5-turbo"})]})]}),he("div",{className:"mb-6",children:[W("h2",{className:"text-xl font-semibold mb-3",style:{color:v.getThemeColor("primary")},children:"历史记录设置"}),he("div",{className:"flex items-center",children:[W("input",{type:"checkbox",id:"enableHistory",name:"enableHistory",checked:e.enableHistory,onChange:L,className:"mr-2",style:{accentColor:v.getThemeColor("accent")}}),W("label",{htmlFor:"enableHistory",className:"font-medium",style:{color:v.getThemeColor("primary")},children:"启用会话历史记录"})]}),W("p",{className:"text-sm mt-1",style:{color:v.getThemeColor("secondary")},children:"启用后，将在同一聊天会话中保存历史对话记录。"})]}),he("div",{className:"mb-6",children:[W("h2",{className:"text-xl font-semibold mb-3",style:{color:v.getThemeColor("primary")},children:"自定义提示词"}),he("div",{className:"p-4 rounded mb-4",style:{backgroundColor:v.getThemeColor("surface")},children:[W("h3",{className:"font-medium mb-2",style:{color:v.getThemeColor("primary")},children:"添加新提示词"}),he("div",{className:"mb-3",children:[W("label",{className:"block mb-1 text-sm",style:{color:v.getThemeColor("primary")},children:"提示词名称"}),W("input",{type:"text",name:"name",value:f.name,onChange:h,className:"apple-input w-full p-2 rounded",style:{backgroundColor:v.getThemeColor("background"),color:v.getThemeColor("primary"),borderColor:v.getThemeColor("border")},placeholder:"提示名称，如：总结、翻译等"})]}),he("div",{className:"mb-3",children:[W("label",{className:"block mb-1 text-sm",style:{color:v.getThemeColor("primary")},children:"提示词内容"}),W("textarea",{name:"prompt",value:f.prompt,onChange:h,className:"apple-input w-full p-2 rounded",style:{backgroundColor:v.getThemeColor("background"),color:v.getThemeColor("primary"),borderColor:v.getThemeColor("border")},rows:3,placeholder:"提示词内容，如：请总结以下内容的要点："})]}),W("button",{onClick:g,disabled:!f.name||!f.prompt,className:"apple-button px-3 py-1 rounded",style:{backgroundColor:!f.name||!f.prompt?v.getThemeColor("muted"):v.getThemeColor("accent"),color:"#ffffff",opacity:!f.name||!f.prompt?.5:1},children:"添加提示词"})]}),he("div",{children:[W("h3",{className:"font-medium mb-2",style:{color:v.getThemeColor("primary")},children:"现有提示词"}),e.customPrompts.length===0?W("p",{style:{color:v.getThemeColor("secondary")},children:"暂无自定义提示词"}):W("div",{className:"space-y-3",children:e.customPrompts.map((T,R)=>W("div",{className:"rounded-lg overflow-hidden",style:{border:`1px solid ${v.getThemeColor("border")}`,backgroundColor:v.getThemeColor("surface")},children:w===R?he("div",{className:"p-3",children:[he("div",{className:"mb-2",children:[W("label",{className:"block mb-1 text-sm",children:"提示词名称"}),W("input",{type:"text",name:"name",value:T.name,onChange:ee=>d(ee,R),className:"w-full p-2 border border-gray-300 rounded"})]}),he("div",{className:"mb-2",children:[W("label",{className:"block mb-1 text-sm",children:"提示词内容"}),W("textarea",{name:"prompt",value:T.prompt,onChange:ee=>d(ee,R),className:"w-full p-2 border border-gray-300 rounded",rows:3})]}),he("div",{className:"flex space-x-2",children:[W("button",{onClick:y,className:"px-2 py-1 bg-green-500 text-white text-sm rounded",children:"完成"}),W("button",{onClick:j,className:"px-2 py-1 bg-gray-300 text-gray-700 text-sm rounded",children:"取消"})]})]}):he("div",{className:"flex justify-between p-3",children:[he("div",{children:[W("div",{className:"font-medium",style:{color:v.getThemeColor("primary")},children:T.name}),W("div",{className:"text-sm mt-1 line-clamp-2",style:{color:v.getThemeColor("secondary")},children:T.prompt})]}),he("div",{className:"flex items-start space-x-2",children:[W("button",{onClick:()=>I(R),className:"transition-colors",style:{color:v.getThemeColor("accent")},title:"编辑提示词",onMouseEnter:ee=>{ee.currentTarget.style.opacity="0.7"},onMouseLeave:ee=>{ee.currentTarget.style.opacity="1"},children:W("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:W("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})})}),W("button",{onClick:()=>C(R),className:"transition-colors",style:{color:"#EF4444"},title:"删除提示词",onMouseEnter:ee=>{ee.currentTarget.style.opacity="0.7"},onMouseLeave:ee=>{ee.currentTarget.style.opacity="1"},children:W("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:W("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]})]})},R))})]})]}),he("div",{className:"mt-6 flex items-center",children:[W("button",{onClick:x,disabled:o,className:"apple-button px-4 py-2 rounded font-medium",style:{backgroundColor:o?v.getThemeColor("muted"):"#10B981",color:"#ffffff",opacity:o?.5:1,cursor:o?"not-allowed":"pointer"},children:o?"保存中...":"保存设置"}),a&&W("span",{className:"ml-3",style:{color:"#10B981"},children:a})]})]})};class rl extends Error{constructor(t){super(t),this.name="ApiConnectionError"}}function vy(e,t,n){const r=[];n?r.push({role:"system",content:n}):r.push({role:"system",content:"你是一个有用的助手，可以帮助用户处理和分析文本内容。提供简洁、准确的回答。"});let o=e;if(t.content&&!e.includes(t.content)){let i="";switch(t.type){case"selection":i=`以下是选中的文本：

`;break;case"block":i=`以下是当前块的内容：

`;break;case"blocks":i=`以下是选中的多个块：

`;break}o=`${i}${t.content}

${e}`}return r.push({role:"user",content:o}),r}function ta(e){try{const t=e.split(`
`).filter(r=>r.trim()!==""&&r.trim()!=="data: [DONE]");let n="";for(const r of t)if(r.startsWith("data: ")){const o=r.replace(/^data: /,"");if(!o||o==="[DONE]")continue;try{const i=JSON.parse(o);i.choices&&i.choices[0]?i.choices[0].delta&&i.choices[0].delta.content?n+=i.choices[0].delta.content:i.choices[0].message&&i.choices[0].message.content&&(n+=i.choices[0].message.content):i.content?n+=i.content:i.text&&(n+=i.text)}catch{console.warn("解析 JSON 失败:",o)}}return n}catch(t){return console.error("解析数据块失败:",t),""}}async function wy(e,t,n,r){try{const o=await Vu();if(!o.apiUrl||!o.apiKey)throw new rl("API URL 或 API Key 未配置");const a={messages:vy(e,t,r),stream:!0,model:o.modelName};o.temperature!==void 0&&(a.temperature=o.temperature),o.maxTokens!==void 0&&o.maxTokens>0&&(a.max_tokens=o.maxTokens);const c=await fetch(o.apiUrl,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${o.apiKey}`},body:JSON.stringify(a)});if(!c.ok){let f=`API 请求失败: ${c.status} ${c.statusText}`;try{const p=await c.json();f=`${f}. ${p.error?.message||JSON.stringify(p)}`}catch{}throw new rl(f)}if(c.body){const f=c.body.getReader(),p=new TextDecoder("utf-8");let w="",_="",v=Date.now();const E=50;for(;;){const{done:k,value:N}=await f.read();if(k){if(_.length>0){const C=ta(_);C&&(w+=C,n.onChunk(C))}n.onComplete(w);break}const L=p.decode(N,{stream:!0});_+=L;const h=_.split(`

`),d=h.pop()||"";if(h.length>0){const C=ta(h.join(`

`));C&&(w+=C,n.onChunk(C))}_=d;const g=Date.now();if(g-v>=E&&_.includes("data: ")){const C=ta(_);C&&(w+=C,n.onChunk(C),_=""),v=g}}}else throw new rl("API 响应中没有正文")}catch(o){o instanceof rl?n.onError(o.message):n.onError(`请求失败: ${o instanceof Error?o.message:String(o)}`)}}function $l(e){if(!e||typeof e!="string")return[];e=e.replace(/^>+\s/gm,"");const t=e.split(/\r?\n/),n=[];let r="",o=!1,i="",a=0;const c=t.filter(p=>p.trim().length>0).map(p=>{const w=p.match(/^(\s+)/);return w?w[1].length:0}).filter(p=>p>0);let f=2;if(c.length>0){const p=c.filter(w=>w>0);p.length>0&&(f=Math.min(...p))}for(let p=0;p<t.length;p++){const w=t[p],_=w.match(/^(\s+)/),v=_?_[1].length:0,E=Math.floor(v/f);if(w.trim().startsWith("```")){if(o){o=!1;const k=a>0?"  ".repeat(a):"";n.push(`${k}\`\`\``)}else{if(r.trim()){const h=a>0?"  ".repeat(a):"";n.push(`${h}${r.trim()}`),r=""}o=!0,i="    ";const k=w.trim().match(/^```(.*)$/),N=k?k[1].trim():"",L=E>0?"  ".repeat(E):"";N?r=`\`\`\` ${N}`:r="```",n.push(`${L}${r}`),r="",a=E}continue}if(o){const k=a>0?"  ".repeat(a):"";n.push(`${k}${i}${w.trimStart()}`);continue}if(w.trim().length>0&&(a=E),w.trim().match(/^(\d+\.|-|\*|\+)\s/)){if(r.trim()){const L=a>0?"  ".repeat(a):"";n.push(`${L}${r.trim()}`),r=""}const k=E>0?"  ".repeat(E):"",N=w.trim().replace(/^(\d+\.|-|\*|\+)\s+/,"");n.push(`${k}${N}`);continue}if(w.trim().match(/^#{1,6}\s/)){if(r.trim()){const N=a>0?"  ".repeat(a):"";n.push(`${N}${r.trim()}`),r=""}const k=E>0?"  ".repeat(E):"";n.push(`${k}${w.trim()}`);continue}if(!w.trim()){if(r.trim()){const k=a>0?"  ".repeat(a):"";n.push(`${k}${r.trim()}`),r=""}continue}r?r+=" "+w.trim():r=w.trim()}if(r.trim()){const p=a>0?"  ".repeat(a):"";n.push(`${p}${r.trim()}`)}return n}const ky=({context:e,onClose:t,onReplace:n,onInsert:r})=>{const[o,i]=ae.useState(""),[a,c]=ae.useState(!1),[f,p]=ae.useState(""),[w,_]=ae.useState(null),[v,E]=ae.useState("custom"),[k,N]=ae.useState(""),[L,h]=ae.useState(!0),[d,g]=ae.useState(0),[C,I]=ae.useState([]),[j,y]=ae.useState(-1),[x,M]=ae.useState(!1),T=ae.useRef(null),R=ae.useRef(null),ee=ae.useRef(null),le=ae.useRef(null),J=fo(),[_e,Ae]=ae.useState(J.isDarkMode());ae.useEffect(()=>{const G=oe=>{Ae(oe.isDark)};return J.addListener(G),()=>{J.removeListener(G)}},[J]),ae.useEffect(()=>{(async()=>{try{const oe=await Vu();_(oe)}catch(oe){console.error("加载设置出错:",oe),p("无法加载插件设置，请检查配置")}})()},[]),ae.useEffect(()=>{v!=="default"&&v!=="custom"&&e?.content&&Ce(),v==="custom"?(h(!0),setTimeout(()=>{R.current?.focus()},50)):h(!1)},[v]),ae.useEffect(()=>{L&&setTimeout(()=>{R.current?.focus()},50)},[]),ae.useEffect(()=>{const G=oe=>{le.current&&!le.current.contains(oe.target)&&M(!1)};return document.addEventListener("mousedown",G),()=>{document.removeEventListener("mousedown",G)}},[]);const Pe=G=>{E(G.target.value)},q=G=>{const oe=G.target.value;if(N(oe),w&&oe.trim()!==""){const ye=w.customPrompts.filter(pe=>pe.name.toLowerCase().includes(oe.toLowerCase()));I(ye),M(ye.length>0),y(-1)}else I([]),M(!1)},O=(G,oe)=>{const ye=`使用提示词: ${G}`;N(ye),E("custom"),M(!1),setTimeout(()=>{Ce(oe)},100)},z=G=>{if(G.key==="Escape"){M(!1);return}if(!x){G.key==="Enter"&&k.trim()&&!a&&(G.preventDefault(),Ce());return}if(G.key==="ArrowDown")G.preventDefault(),y(oe=>oe<C.length-1?oe+1:oe);else if(G.key==="ArrowUp")G.preventDefault(),y(oe=>oe>0?oe-1:0);else if(G.key==="Enter"&&j>=0){G.preventDefault();const oe=C[j];O(oe.name,oe.prompt)}},B=()=>{k.trim()&&!a&&Ce()},ne=G=>{G.key==="Enter"&&k.trim()&&!a&&!x&&(G.preventDefault(),Ce())},Ce=async G=>{if(!(!e?.content||a)){if(!w||!w.apiKey){p("请先在设置中配置API密钥");return}c(!0),p(""),i(""),g(0),M(!1);try{let oe;if(G)oe=G;else if(v==="custom"){if(!k.trim()){p("请输入自定义提示词"),c(!1);return}oe=k}else if(v!=="default"&&w){const pe=w.customPrompts.find(Ue=>Ue.name===v);pe&&(oe=pe.prompt)}const ye={onChunk:pe=>{i(Ue=>{const Ge=Ue+pe;return setTimeout(()=>{g(Ge.length)},10),Ge})},onComplete:pe=>{c(!1),g(pe.length)},onError:pe=>{p(`API 请求失败: ${pe}`),c(!1)}};await wy(e.content,e,ye,oe)}catch(oe){console.error("发送消息出错:",oe),p("发送消息失败，请重试"),c(!1)}}},V=()=>o?he("div",{className:"typewriter-text",children:[o.slice(0,d),a&&d===o.length&&W("span",{className:"blinking-cursor",children:"|"})]}):null,se=()=>w?he(om,{children:[W("option",{value:"custom",children:"自定义提示词"}),w.customPrompts.map((G,oe)=>W("option",{value:G.name,children:G.name},oe))]}):null,ce=()=>!x||C.length===0?null:W("div",{ref:le,className:"absolute z-10 left-4 right-16 top-full mt-1 bg-white dark:bg-gray-800 shadow-lg rounded-lg border border-gray-200 dark:border-gray-700 max-h-60 overflow-y-auto",children:C.map((G,oe)=>W("div",{className:`px-4 py-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 ${oe===j?"bg-gray-100 dark:bg-gray-700":""}`,onClick:()=>O(G.name,G.prompt),children:W("div",{className:"font-medium text-gray-900 dark:text-white",children:G.name})},oe))}),ze=async G=>{try{if($l(G).length===0||e.blockUUIDs.length===0){p("无法替换原文：无有效块数据或无原始块 UUID");return}n(G)}catch(oe){console.error("替换原文出错:",oe),p("替换原文失败，请重试")}},Qe=async G=>{try{if($l(G).length===0||e.blockUUIDs.length===0){p("无法插入子块：无有效块数据或无原始块 UUID");return}r(G)}catch(oe){console.error("插入子块出错:",oe),p("插入子块失败，请重试")}};return he("div",{ref:T,className:`apple-modal rounded-xl shadow-lg w-full flex flex-col ${J.getThemeClasses()}`,style:{backdropFilter:"blur(20px)",WebkitBackdropFilter:"blur(20px)",maxHeight:"60vh",border:`1px solid ${J.getThemeColor("border")}`,backgroundColor:J.getThemeColor("background"),color:J.getThemeColor("primary")},children:[he("div",{className:"p-4 border-b flex justify-between items-center",style:{borderColor:J.getThemeColor("border")},children:[W("h2",{className:"text-lg font-medium",style:{color:J.getThemeColor("primary")},children:"AI 聊天"}),he("div",{className:"flex items-center",children:[W("select",{value:v,onChange:Pe,className:"apple-select mr-3 text-sm rounded-md border-none h-8 px-3",style:{backgroundColor:J.getThemeColor("surface"),color:J.getThemeColor("primary")},children:se()}),W("button",{onClick:t,className:"h-8 w-8 flex items-center justify-center rounded-full transition-colors",style:{color:J.getThemeColor("secondary"),backgroundColor:"transparent"},onMouseEnter:G=>{G.currentTarget.style.backgroundColor=J.getThemeColor("surface")},onMouseLeave:G=>{G.currentTarget.style.backgroundColor="transparent"},children:"✕"})]})]}),L&&he("div",{className:"p-4 border-b relative",style:{borderColor:J.getThemeColor("border")},children:[he("div",{className:"flex items-center",children:[W("input",{ref:R,type:"text",value:k,onChange:q,onKeyPress:ne,onKeyDown:z,placeholder:"输入提示词，支持自动补全",className:"apple-input flex-grow text-sm rounded-lg px-4 py-2",style:{backgroundColor:J.getThemeColor("surface"),color:J.getThemeColor("primary"),borderColor:J.getThemeColor("border")}}),W("button",{onClick:B,disabled:!k.trim()||a,className:"apple-button ml-2 px-4 py-2 rounded-lg text-sm font-medium",style:{backgroundColor:!k.trim()||a?J.getThemeColor("muted"):J.getThemeColor("accent"),color:"#ffffff",opacity:!k.trim()||a?.5:1,cursor:!k.trim()||a?"not-allowed":"pointer"},children:"发送"})]}),ce()]}),he("div",{ref:ee,className:"p-4 flex-grow overflow-y-auto max-h-[40vh] relative",children:[f&&W("div",{className:"mb-4 p-3 rounded-lg text-sm",style:{backgroundColor:"rgba(239, 68, 68, 0.1)",borderColor:"rgba(239, 68, 68, 0.3)",color:_e?"#FCA5A5":"#DC2626",border:"1px solid"},children:f}),a&&!o&&W("div",{className:"flex justify-center items-center py-8",children:W("div",{className:"apple-spinner w-6 h-6 border-2 rounded-full animate-spin",style:{borderColor:J.getThemeColor("border"),borderTopColor:J.getThemeColor("accent")}})}),o&&W("div",{className:"ai-response rounded-lg p-4 text-sm whitespace-pre-wrap relative",style:{backgroundColor:J.getThemeColor("surface"),color:J.getThemeColor("primary")},children:V()})]}),o&&!a&&he("div",{className:"p-3 border-t flex justify-end space-x-2",style:{borderColor:J.getThemeColor("border")},children:[W("button",{onClick:()=>ze(o),className:"apple-button-secondary px-4 py-2 rounded-lg text-sm font-medium transition-colors",style:{backgroundColor:J.getThemeColor("surface"),color:J.getThemeColor("primary"),border:`1px solid ${J.getThemeColor("border")}`},onMouseEnter:G=>{G.currentTarget.style.backgroundColor=J.getThemeColor("border")},onMouseLeave:G=>{G.currentTarget.style.backgroundColor=J.getThemeColor("surface")},children:"替换原文"}),W("button",{onClick:()=>Qe(o),className:"apple-button-primary px-4 py-2 rounded-lg text-sm font-medium transition-colors",style:{backgroundColor:J.getThemeColor("accent"),color:"#ffffff"},onMouseEnter:G=>{G.currentTarget.style.opacity="0.9"},onMouseLeave:G=>{G.currentTarget.style.opacity="1"},children:"插入子块"})]})]})};function Sy({chatContext:e}={}){const t=ae.useRef(null),n=dy(),[r,o]=ae.useState("settings"),[i,a]=ae.useState(e),[c,f]=ae.useState(null),[p,w]=ae.useState(!1),_=fo();ae.useEffect(()=>{e&&(a(e),o("chat"),setTimeout(v,50))},[e]),ae.useEffect(()=>{w(_.isDarkMode());const d=g=>{w(g.isDark)};return _.addListener(d),()=>{_.removeListener(d)}},[_]);const v=async()=>{if(!(!i||!i.blockUUIDs.length))try{const d=i.blockUUIDs[0],g=document.querySelector(`[blockid="${d}"]`);if(g){const C=g.getBoundingClientRect();f({top:C.bottom,left:C.left,width:C.width})}}catch(d){console.error("获取块位置失败:",d)}},E=async d=>{if(!(!i||!i.blockUUIDs.length))try{const g=$l(d);for(const j of i.blockUUIDs)await logseq.Editor.removeBlock(j);const C=i.blockUUIDs[0],I=await logseq.Editor.getBlock(C);if(!I){console.error("无法获取块信息");return}if(I.parent){const j=I.parent.id||I.parent.uuid;for(const y of g)await logseq.Editor.insertBlock(j,y,{sibling:!0,before:!1})}else if(I.page){const j=I.page.originalName||I.page.name;for(const y of g)await logseq.Editor.insertBlock(j,y)}logseq.hideMainUI()}catch(g){console.error("替换内容失败:",g)}},k=async d=>{if(!(!i||!i.blockUUIDs.length))try{const g=$l(d),C=i.blockUUIDs[0],I=N(g);await L(C,I),logseq.hideMainUI()}catch(g){console.error("插入内容失败:",g)}},N=d=>{if(!d.length)return[];const g=[],C=[];for(const I of d){const j=I.match(/^(\s+)/),y=j?Math.floor(j[1].length/2):0,T={content:I.trimStart().replace(/^(\d+\.|-|\*|\+)\s+/,"")};if(C.length===0)g.push(T),C.push({node:T,indent:y});else{for(;C.length>0&&C[C.length-1].indent>=y;)C.pop();if(C.length===0)g.push(T);else{const R=C[C.length-1].node;R.children||(R.children=[]),R.children.push(T)}C.push({node:T,indent:y})}}return g},L=async(d,g)=>{for(const C of g){const I=await logseq.Editor.insertBlock(d,C.content,{sibling:!1});C.children&&C.children.length>0&&I&&await L(I.uuid,C.children)}},h=()=>{logseq.hideMainUI()};return n?he("main",{className:"fixed inset-0 z-50 flex items-center justify-center",style:{backdropFilter:"none",WebkitBackdropFilter:"none",backgroundColor:"transparent"},onClick:d=>{t.current?.contains(d.target)||window.logseq.hideMainUI()},children:[r==="settings"&&W("div",{ref:t,className:`apple-modal rounded-xl shadow-2xl w-full max-w-3xl max-h-[90vh] overflow-y-auto pointer-events-auto mx-auto ${_.getThemeClasses()}`,style:{backdropFilter:"blur(20px)",WebkitBackdropFilter:"blur(20px)",backgroundColor:_.getThemeColor("background"),color:_.getThemeColor("primary")},onClick:d=>d.stopPropagation(),children:W(yy,{})}),r==="chat"&&i&&W("div",{ref:t,className:"pointer-events-auto",style:c?{position:"absolute",top:`${c.top+10}px`,left:`${c.left}px`,zIndex:9999,maxWidth:"700px",width:`${Math.max(580,Math.min(c.width*1.5,700))}px`}:{maxWidth:"700px",width:"100%"},onClick:d=>d.stopPropagation(),children:W(ky,{context:i,onClose:h,onReplace:E,onInsert:k})})]}):null}async function _y(){try{const e=await logseq.Editor.getEditingCursorPosition();if(e&&e.pos){const t=await logseq.Editor.getCurrentBlock();if(t){const n=t.content,r=e.pos;if(r.start!==r.end)return n.substring(r.start,r.end)}}return null}catch(e){return console.error("获取选中文本失败:",e),null}}async function Cy(){try{return await logseq.Editor.getSelectedBlocks()||[]}catch(e){return console.error("获取选中块失败:",e),[]}}async function Nf(){try{return await logseq.Editor.getCurrentBlock()}catch(e){return console.error("获取当前块失败:",e),null}}async function xy(){const e=await _y();if(e){const r=await Nf();return{type:"selection",content:e,blockUUIDs:r?[r.uuid]:[]}}const t=await Cy();if(t&&t.length>0)return{type:"blocks",content:t.map(o=>o.content).join(`

`),blockUUIDs:t.map(o=>o.uuid)};const n=await Nf();return n?{type:"block",content:n.content,blockUUIDs:[n.uuid]}:{type:"none",content:"",blockUUIDs:[]}}const Ey={id:"logseq-plugin-ai-chat",icon:"./logo.svg",title:"AI 聊天助手",description:"在Logseq中使用自定义API进行AI聊天，支持智能上下文感知和完整暗色模式"},Ty=(e,...t)=>String.raw(e,...t),Hp=Ey.id,Ny=$p(document.getElementById("app"));let Ga;async function Py(){console.info(`#${Hp}: MAIN`),await py(),await Oy(),logseq.useSettingsSchema([{key:"apiUrl",type:"string",default:"https://api.openai.com/v1/chat/completions",title:"API URL",description:"AI服务的API地址"},{key:"apiKey",type:"string",default:"",title:"API Key",description:"访问AI服务所需的API密钥"},{key:"modelName",type:"string",default:"gpt-3.5-turbo",title:"模型名称",description:"使用的AI模型名称"},{key:"enableHistory",type:"boolean",default:!1,title:"启用历史记录",description:"是否保存聊天历史记录"}]),na();function e(){return{show(){Ga=void 0,na(),logseq.showMainUI()},async openAIChat(){const r=await xy();console.log("获取到的上下文:",r),Ga=r,na(),logseq.showMainUI()}}}const t=e();logseq.provideModel(t),logseq.setMainUIInlineStyle({zIndex:9}),logseq.Editor.registerSlashCommand("AI聊天",async()=>t.openAIChat()),logseq.App.registerCommandPalette({key:"open-ai-chat",label:"打开AI聊天",keybinding:{binding:"ctrl+g",mac:"cmd+g"}},async()=>t.openAIChat());const n="ai-chat-plugin-open";logseq.provideStyle(Ty`
    .${n} {
      opacity: 0.8;
      font-size: 22px;
      margin-top: 4px;
      color: var(--ls-primary-text-color);
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
    }

    .${n}:hover {
      opacity: 1;
      color: var(--ls-active-primary-color);
      transform: scale(1.1);
    }
  `),logseq.App.registerUIItem("toolbar",{key:n,template:`
    <a data-on-click="show">
        <div class="${n}">🤖</div>
    </a>    
`})}function na(){Ny.render(W(Ff.StrictMode,{children:W(Sy,{chatContext:Ga})}))}async function Oy(){try{const e=fo();console.info(`#${Hp}: 主题管理器已初始化`)}catch(e){console.error("主题初始化失败:",e)}}logseq.ready(Py).catch(console.error);
